APP_NAME="Sistem Pondok (Testing)"
APP_ENV=testing
APP_KEY=base64:rpPUJ/h4pllOYSSBXjd1ZcWqk75qKleZVUtCbg2DARM=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost
APP_PORT=8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=4

LOG_CHANNEL=stack
LOG_LEVEL=warning

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sistem_pondok_testing
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=array
SESSION_LIFETIME=30
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=array
CACHE_PREFIX=testing_
