<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Kelas;
use App\Models\SantriKelas;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Throwable;

class KelasController extends Controller
{
    public function index()
    {
        try {
            $kelas = Kelas::with('komplek')->get();

            return response()->json([
                'success' => true,
                'message' => 'Data kelas berhasil diambil.',
                'data' => $kelas,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            $kelas = Kelas::with('komplek')->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'Detail kelas berhasil diambil.',
                'data' => $kelas,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }


    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'nama' => ['required', 'string', 'max:100'],
                'id_komplek' => ['required', 'exists:kompleks,id'],
            ]);

            $kelas = Kelas::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil ditambahkan.',
                'data' => $kelas,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $kelas = Kelas::findOrFail($id);

            $validated = $request->validate([
                'nama' => ['required', 'string', 'max:100'],
                'id_komplek' => ['required', 'exists:kompleks,id'],
            ]);

            $kelas->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil diperbarui.',
                'data' => $kelas,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $kelas = Kelas::findOrFail($id);
            $kelas->delete();

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil dihapus.',
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }




    public function santri($id)
    {
        try {
            // Optional: validasi bahwa kelas dengan ID tersebut ada
            $exists = Kelas::find($id);
            if (! $exists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak ditemukan.',
                ], 404);
            }

            $santri = SantriKelas::with('santri')
                ->where('id_kelas', $id)
                ->whereNull('sampai_tanggal')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Daftar santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function history()
    {
        try {
            $riwayat = SantriKelas::with(['santri', 'kelas.komplek'])
                ->orderByDesc('mulai_tanggal')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Riwayat perpindahan santri berhasil diambil.',
                'data' => $riwayat,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil riwayat santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
