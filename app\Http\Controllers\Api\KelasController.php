<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Kelas;
use App\Models\SantriKelas;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Throwable;

class KelasController extends Controller
{
    public function index($komplek_id)
    {
        try {
            // Validate that komplek exists
            if (!is_numeric($komplek_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID komplek harus berupa angka.',
                ], 400);
            }

            // Check if komplek exists
            $komplekExists = \App\Models\Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $kelas = Kelas::with('komplek')
                ->where('id_komplek', $komplek_id)
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Data kelas berhasil diambil.',
                'data' => $kelas,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function show($komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = \App\Models\Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find kelas that belongs to the specified komplek
            $kelas = Kelas::with('komplek')
                ->where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            return response()->json([
                'success' => true,
                'message' => 'Detail kelas berhasil diambil.',
                'data' => $kelas,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }


    public function store(Request $request, $komplek_id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = \App\Models\Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $validated = $request->validate([
                'nama_kelas' => ['required', 'string', 'max:100'],
            ]);

            // Add komplek_id from route parameter
            $validated['id_komplek'] = $komplek_id;

            $kelas = Kelas::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil ditambahkan.',
                'data' => $kelas,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function update(Request $request, $komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = \App\Models\Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find kelas that belongs to the specified komplek
            $kelas = Kelas::where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            $validated = $request->validate([
                'nama_kelas' => ['required', 'string', 'max:100'],
            ]);

            $kelas->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil diperbarui.',
                'data' => $kelas,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function destroy($komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = \App\Models\Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find kelas that belongs to the specified komplek
            $kelas = Kelas::where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            $kelas->delete();

            return response()->json([
                'success' => true,
                'message' => 'Kelas berhasil dihapus.',
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus kelas.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function santri($komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = \App\Models\Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Validate that kelas exists and belongs to the komplek
            $kelas = Kelas::where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->first();

            if (!$kelas) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kelas tidak ditemukan.',
                ], 404);
            }

            $santri = SantriKelas::with('santri')
                ->where('id_kelas', $id)
                ->whereNull('sampai_tanggal')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Daftar santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function history()
    {
        try {
            $riwayat = SantriKelas::with(['santri', 'kelas.komplek'])
                ->orderByDesc('mulai_tanggal')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Riwayat perpindahan santri berhasil diambil.',
                'data' => $riwayat,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil riwayat santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
