<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PengaturanTahfidzKomplek;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Throwable;

class PengaturanTahfidzController extends Controller
{
    public function show($idKomplek)
    {
        try {
            $setting = PengaturanTahfidzKomplek::where('id_komplek', $idKomplek)
                ->where('aktif', true)
                ->orderByDesc('berlaku_mulai')
                ->first();

            if (! $setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pengaturan tahfidz tidak ditemukan untuk komplek ini.',
                    'data' => null, // Tambahkan data null supaya konsisten
                ], 200);
            }

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan tahfidz ditemukan.',
                'data' => [
                    'id' => $setting->id,
                    'mode' => $setting->mode,
                    'berlaku_mulai' => $setting->berlaku_mulai,
                ],
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil pengaturan tahfidz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }


    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'mode' => ['required', 'in:halaman,ayat'],
                'berlaku_mulai' => ['required', 'date'],
                'aktif' => ['required', 'boolean'],
                'id_komplek' => ['required', 'integer'],
            ]);

            $setting = PengaturanTahfidzKomplek::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan tahfidz berhasil dibuat.',
                'data' => $setting,
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan pada server.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }



    public function update(Request $request, $id)
    {
        try {
            $setting = PengaturanTahfidzKomplek::findOrFail($id);

            $validated = $request->validate([
                'mode' => ['required', 'in:halaman,ayat'],
                'berlaku_mulai' => ['required', 'date'],
                'aktif' => ['required', 'boolean'],
            ]);

            $setting->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan tahfidz berhasil diperbarui.',
                'data' => $setting,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Pengaturan tahfidz tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui pengaturan tahfidz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $setting = PengaturanTahfidzKomplek::findOrFail($id);
            $setting->delete();

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan tahfidz berhasil dihapus.',
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Pengaturan tahfidz tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus pengaturan tahfidz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
