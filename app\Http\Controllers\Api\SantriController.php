<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Santri;
use App\Models\Komplek;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Throwable;

class SantriController extends Controller
{
    /**
     * Get all santri for a specific komplek
     */
    public function index($komplek_id, Request $request)
    {
        try {
            // Validate that komplek exists
            if (!is_numeric($komplek_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID komplek harus berupa angka.',
                ], 400);
            }

            // Check if komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $query = Santri::with(['komplek', 'kamar', 'kabupaten', 'kecamatan'])
                ->where('id_komplek', $komplek_id);

            // Optional filters
            if ($request->has('status') && $request->status) {
                switch ($request->status) {
                    case 'aktif':
                        $query->where('is_aktif', true);
                        break;
                    case 'nonaktif':
                        $query->where('is_nonaktif', true);
                        break;
                    case 'alumni':
                        $query->where('is_alumni', true);
                        break;
                    case 'diterima':
                        $query->where('diterima', true);
                        break;
                }
            }

            // Search by name
            if ($request->has('search') && $request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('nama', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk_baru', 'like', '%' . $request->search . '%');
                });
            }

            // Filter by gender
            if ($request->has('jenis_kelamin') && $request->jenis_kelamin) {
                $query->where('jenis_kelamin', $request->jenis_kelamin);
            }

            // Filter by year
            if ($request->has('tahun_masuk') && $request->tahun_masuk) {
                $query->where('tahun_masuk', $request->tahun_masuk);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $santri = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Data santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get all santri (for admin/super admin)
     */
    public function all(Request $request)
    {
        try {
            $query = Santri::with(['komplek', 'kamar', 'kabupaten', 'kecamatan']);

            // Optional filters
            if ($request->has('id_komplek') && $request->id_komplek) {
                $query->where('id_komplek', $request->id_komplek);
            }

            if ($request->has('status') && $request->status) {
                switch ($request->status) {
                    case 'aktif':
                        $query->where('is_aktif', true);
                        break;
                    case 'nonaktif':
                        $query->where('is_nonaktif', true);
                        break;
                    case 'alumni':
                        $query->where('is_alumni', true);
                        break;
                    case 'diterima':
                        $query->where('diterima', true);
                        break;
                }
            }

            // Search by name
            if ($request->has('search') && $request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('nama', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk', 'like', '%' . $request->search . '%')
                      ->orWhere('nomor_induk_baru', 'like', '%' . $request->search . '%');
                });
            }

            // Filter by gender
            if ($request->has('jenis_kelamin') && $request->jenis_kelamin) {
                $query->where('jenis_kelamin', $request->jenis_kelamin);
            }

            // Filter by year
            if ($request->has('tahun_masuk') && $request->tahun_masuk) {
                $query->where('tahun_masuk', $request->tahun_masuk);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $santri = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'message' => 'Data santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get specific santri by ID
     */
    public function show($komplek_id, $id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            // Find santri that belongs to the specified komplek
            $santri = Santri::with(['komplek', 'kamar', 'kabupaten', 'kecamatan', 'admin'])
                ->where('id', $id)
                ->where('id_komplek', $komplek_id)
                ->firstOrFail();

            return response()->json([
                'success' => true,
                'message' => 'Detail santri berhasil diambil.',
                'data' => $santri,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Santri tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get santri statistics for a komplek
     */
    public function statistics($komplek_id)
    {
        try {
            // Validate that komplek exists
            $komplekExists = Komplek::find($komplek_id);
            if (!$komplekExists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Komplek tidak ditemukan.',
                ], 404);
            }

            $stats = [
                'total' => Santri::where('id_komplek', $komplek_id)->count(),
                'aktif' => Santri::where('id_komplek', $komplek_id)->where('is_aktif', true)->count(),
                'nonaktif' => Santri::where('id_komplek', $komplek_id)->where('is_nonaktif', true)->count(),
                'alumni' => Santri::where('id_komplek', $komplek_id)->where('is_alumni', true)->count(),
                'diterima' => Santri::where('id_komplek', $komplek_id)->where('diterima', true)->count(),
                'laki_laki' => Santri::where('id_komplek', $komplek_id)->where('jenis_kelamin', 'laki-laki')->count(),
                'perempuan' => Santri::where('id_komplek', $komplek_id)->where('jenis_kelamin', 'perempuan')->count(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Statistik santri berhasil diambil.',
                'data' => $stats,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil statistik santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
