<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Santri;
use App\Models\Kelas;
use App\Models\SantriKelas;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Throwable;

class SantriKelasController extends Controller
{
    /**
     * Menambahkan santri ke kelas
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'id_santri' => ['required', 'exists:santris,id'],
                'id_kelas' => ['required', 'exists:kelas,id'],
                'mulai_tanggal' => ['required', 'date'],
            ]);

            // 🔐 Cek apakah santri masih aktif di kelas lain
            $aktif = SantriKelas::where('id_santri', $validated['id_santri'])
                ->whereNull('sampai_tanggal')
                ->first();

            if ($aktif) {
                return response()->json([
                    'success' => false,
                    'message' => 'Santri masih terdaftar di kelas aktif lain.',
                    'data' => $aktif,
                ], 409); // Conflict
            }

            $entry = SantriKelas::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Santri berhasil dimasukkan ke kelas.',
                'data' => $entry,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }


    /**
     * Mengeluarkan santri dari kelas (mengisi sampai_tanggal)
     */
    public function keluar(Request $request)
    {
        try {
            $validated = $request->validate([
                'id_santri' => ['required', 'exists:santris,id'],
                'id_kelas' => ['required', 'exists:kelas,id'],
                'sampai_tanggal' => ['required', 'date'],
            ]);

            $entry = SantriKelas::where('id_santri', $validated['id_santri'])
                ->where('id_kelas', $validated['id_kelas'])
                ->whereNull('sampai_tanggal')
                ->first();

            if (! $entry) {
                return response()->json([
                    'success' => false,
                    'message' => 'Santri tidak ditemukan di kelas aktif.',
                ], 404);
            }

            $entry->sampai_tanggal = $validated['sampai_tanggal'];
            $entry->save();

            return response()->json([
                'success' => true,
                'message' => 'Santri berhasil dikeluarkan dari kelas.',
                'data' => $entry,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Mendapatkan daftar santri yang masih aktif di dalam kelas tertentu
     */
    public function getByKelas($idKelas)
    {
        try {
            $kelas = Kelas::findOrFail($idKelas);

            $santriKelas = SantriKelas::with('santri')
                ->where('id_kelas', $idKelas)
                ->whereNull('sampai_tanggal')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Daftar santri dalam kelas berhasil diambil.',
                'kelas' => $kelas->nama ?? null,
                'data' => $santriKelas,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Kelas tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data santri.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
