<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SetoranHafalan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Throwable;

class SetoranController extends Controller
{
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'id_santri' => 'required|exists:santris,id',
                'id_kelas' => 'nullable|exists:kelas,id',
                'id_tipe_setoran' => 'required|exists:tipe_setorans,id',
                'id_sesi_setoran' => 'required|exists:sesi_setorans,id',
                'tanggal' => 'required|date',

                'juz' => 'nullable|integer',
                'halaman_awal' => 'nullable|integer',
                'halaman_dalam_juz' => 'nullable|integer',
                'jumlah_halaman' => 'nullable|integer',

                'surat_id' => 'nullable|integer',
                'ayat_awal' => 'nullable|integer',
                'ayat_akhir' => 'nullable|integer',

                'status' => 'required|in:setor,murojaah,ulang',
                'catatan' => 'nullable|string',
            ]);

            /** @var \App\Models\User|null $user */
            $user = Auth::user();
            $data['dibuat_oleh'] = $user?->id;

            $setoran = SetoranHafalan::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Setoran berhasil disimpan.',
                'data' => $setoran,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan setoran.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function riwayat(int $idSantri)
    {
        try {
            $data = SetoranHafalan::where('id_santri', $idSantri)
                ->orderByDesc('tanggal')
                ->limit(50)
                ->get();

            if ($data->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Belum ada riwayat setoran.',
                    'data' => [],
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Riwayat setoran berhasil diambil.',
                'data' => $data,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil riwayat setoran.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
