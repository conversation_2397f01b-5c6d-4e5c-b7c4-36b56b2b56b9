<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Throwable;

class UstadzController extends Controller
{
    /**
     * Get all ustadz users
     */
    public function index(Request $request)
    {
        try {
            $query = User::with(['roleUsers.komplek', 'roleUsers.role'])
                ->whereHas('roles', function ($q) {
                    $q->where('name', 'ustadz');
                });

            // Filter by komplek if provided
            if ($request->has('id_komplek') && $request->id_komplek) {
                $query->whereHas('roleUsers', function ($q) use ($request) {
                    $q->where('id_komplek', $request->id_komplek);
                });
            }

            // Search by name or email
            if ($request->has('search') && $request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('email', 'like', '%' . $request->search . '%');
                });
            }

            $ustadz = $query->get()->map(function ($user) {
                $roleUser = $user->roleUsers->first();
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'email_verified_at' => $user->email_verified_at,
                    'is_confirmed' => $user->is_confirmed,
                    'komplek' => $roleUser && $roleUser->komplek ? [
                        'id' => $roleUser->komplek->id,
                        'nama_komplek' => $roleUser->komplek->nama_komplek,
                    ] : null,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Data ustadz berhasil diambil.',
                'data' => $ustadz,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data ustadz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get specific ustadz by ID
     */
    public function show($id)
    {
        try {
            $user = User::with(['roleUsers.komplek', 'roleUsers.role', 'kelasDiampu'])
                ->whereHas('roles', function ($q) {
                    $q->where('name', 'ustadz');
                })
                ->findOrFail($id);

            $roleUser = $user->roleUsers->first();

            $ustadz = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'email_verified_at' => $user->email_verified_at,
                'is_confirmed' => $user->is_confirmed,
                'komplek' => $roleUser && $roleUser->komplek ? [
                    'id' => $roleUser->komplek->id,
                    'nama_komplek' => $roleUser->komplek->nama_komplek,
                ] : null,
                'kelas_diampu' => $user->kelasDiampu->map(function ($kelas) {
                    return [
                        'id' => $kelas->id,
                        'nama' => $kelas->nama,
                    ];
                }),
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Detail ustadz berhasil diambil.',
                'data' => $ustadz,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ustadz tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil detail ustadz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get ustadz by komplek ID
     */
    public function getByKomplek($idKomplek)
    {
        try {
            $ustadz = User::with(['roleUsers.komplek'])
                ->whereHas('roleUsers', function ($q) use ($idKomplek) {
                    $q->where('id_komplek', $idKomplek)
                      ->whereHas('role', function ($roleQuery) {
                          $roleQuery->where('name', 'ustadz');
                      });
                })
                ->get()
                ->map(function ($user) {
                    return [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'phone' => $user->phone,
                        'is_confirmed' => $user->is_confirmed,
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'Data ustadz berhasil diambil.',
                'data' => $ustadz,
            ]);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data ustadz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Create new ustadz user
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone' => 'nullable|string|max:20',
                'password' => 'required|string|min:8',
                'id_komplek' => 'required|exists:kompleks,id',
            ]);

            // Create user
            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'] ?? null,
                'password' => bcrypt($validated['password']),
                'is_confirmed' => false,
            ]);

            // Get ustadz role
            $ustadzRole = Role::where('name', 'ustadz')->first();

            if (!$ustadzRole) {
                throw new \Exception('Role ustadz tidak ditemukan.');
            }

            // Assign ustadz role to user
            $user->roleUsers()->create([
                'role_id' => $ustadzRole->id,
                'id_komplek' => $validated['id_komplek'],
            ]);

            // Load relationships for response
            $user->load(['roleUsers.komplek']);
            $roleUser = $user->roleUsers->first();

            $responseData = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'is_confirmed' => $user->is_confirmed,
                'komplek' => $roleUser && $roleUser->komplek ? [
                    'id' => $roleUser->komplek->id,
                    'nama_komplek' => $roleUser->komplek->nama_komplek,
                ] : null,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Ustadz berhasil ditambahkan.',
                'data' => $responseData,
            ], 201);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menambahkan ustadz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Update ustadz user
     */
    public function update(Request $request, $id)
    {
        try {
            $user = User::whereHas('roles', function ($q) {
                $q->where('name', 'ustadz');
            })->findOrFail($id);

            $validated = $request->validate([
                'name' => 'sometimes|required|string|max:255',
                'email' => 'sometimes|required|email|unique:users,email,' . $id,
                'phone' => 'nullable|string|max:20',
                'password' => 'sometimes|nullable|string|min:8',
                'id_komplek' => 'sometimes|required|exists:kompleks,id',
            ]);

            // Update user data
            $updateData = [];
            if (isset($validated['name'])) $updateData['name'] = $validated['name'];
            if (isset($validated['email'])) $updateData['email'] = $validated['email'];
            if (isset($validated['phone'])) $updateData['phone'] = $validated['phone'];
            if (isset($validated['password'])) $updateData['password'] = bcrypt($validated['password']);

            if (!empty($updateData)) {
                $user->update($updateData);
            }

            // Update komplek if provided
            if (isset($validated['id_komplek'])) {
                $user->roleUsers()->update(['id_komplek' => $validated['id_komplek']]);
            }

            // Load updated relationships
            $user->load(['roleUsers.komplek']);
            $roleUser = $user->roleUsers->first();

            $responseData = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'is_confirmed' => $user->is_confirmed,
                'komplek' => $roleUser && $roleUser->komplek ? [
                    'id' => $roleUser->komplek->id,
                    'nama_komplek' => $roleUser->komplek->nama_komplek,
                ] : null,
            ];

            return response()->json([
                'success' => true,
                'message' => 'Data ustadz berhasil diperbarui.',
                'data' => $responseData,
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ustadz tidak ditemukan.',
            ], 404);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $e->errors(),
            ], 422);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data ustadz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Delete ustadz user
     */
    public function destroy($id)
    {
        try {
            $user = User::whereHas('roles', function ($q) {
                $q->where('name', 'ustadz');
            })->findOrFail($id);

            // Delete role assignments first
            $user->roleUsers()->delete();
            
            // Delete user
            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'Ustadz berhasil dihapus.',
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Ustadz tidak ditemukan.',
            ], 404);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus ustadz.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
