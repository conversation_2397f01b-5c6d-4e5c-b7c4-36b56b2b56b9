<?php

namespace App\Http\Controllers\KTS;

use App\Http\Controllers\Controller;
use App\Services\KtsService;
use App\Models\Santri;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;


class GenerateKts extends Controller
{
    protected $ktsService;

    /**
     * Inject the KtsService into the controller.
     */
    public function __construct(KtsService $ktsService)
    {
        $this->ktsService = $ktsService;
    }

    /**
     * Generate KTS for a single santri.
     */
    public function generateSingle(Request $request)
    {

        // Retrieve the santri based on nomor_induk
        $santri = Santri::find($request->input('id'));

        if (!$santri) {
            return redirect()->back()->with(['error' => 'Santri tidak ditemukan.']);
        }

        // Use KtsService to generate the KTS for the single santri
        return $this->ktsService->generateKtsForSingleSantri($santri, 'I');
    }

    public function generateMultiple(Request $request)
    {
//        dd($request->all());
        // Retrieve the santri based on nomor_induk
        $santri = Santri::whereIn('id', $request->input('id'))
            ->orderBy('tahun_masuk', 'asc') // Urutkan berdasarkan tahun_masuk dari lama ke baru
            ->get();

        if (!$santri) {
            return redirect()->back()->with(['error' => 'Santri tidak ditemukan.']);
        }

//        dd($santri->toArray());

        // Use KtsService to generate the KTS for the single santri
        return $this->ktsService->generateKtsForMultipleSantri($santri, 'I');
    }

    public function cetak(Request $request)
    {
        try {
            // Decrypt the data
            $decryptedData = Crypt::decrypt($request->query('data'));

            // Convert decrypted data to array or collection if needed
            $data = json_decode($decryptedData, true);

            $santri = Santri::findMany($data);
            if (!$santri) {
                return redirect()->back()->with(['error' => 'Santri tidak ditemukan.']);
            }

            // Generate KTS with the decrypted data
            $ktsService = new KtsService();
            return $ktsService->generateKtsForMultipleSantri($santri, 'I');
        } catch (\Exception $e) {
            dd($e);
            return abort(500, 'Terjadi kesalahan saat mendekripsi data: ' . $e->getMessage());
        }
    }

    // Other methods (e.g., for multiple santri) can be added here
}
