<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CanAccessAbsensiKomplek
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $komplekId = $user->roleUsers()->first() ? $user->roleUsers()->first()->id_komplek : null;

        if (! $user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        // Superadmin tidak boleh akses absensi
        if ($user->is_super_admin) {
            return response()->json(['message' => 'Forbidden: Superadmin cannot access absensi'], 403);
        }

        // Admin dan ustadz boleh akses absensi
        $hasAccess = $user->roleUsers()
            ->where('id_komplek', $komplekId)
            ->whereHas('role', function ($query) {
                $query->whereIn('name', ['admin', 'ustadz']);
            })->exists();

        if (! $hasAccess) {
            return response()->json(['message' => 'Forbidden: You are not assigned to this komplek'], 403);
        }

        return $next($request);
    }
}
