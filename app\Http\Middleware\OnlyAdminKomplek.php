<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Log;
use Symfony\Component\HttpFoundation\Response;

use function Illuminate\Log\log;

class OnlyAdminKomplek
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $komplekId = $user->roleUsers()->first() ? $user->roleUsers()->first()->id_komplek : null;

        if (! $user) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        // Superadmin tidak boleh akses
        if ($user->is_super_admin) {
            return response()->json(['message' => 'Forbidden: Superadmin cannot access absensi'], 403);
        }

        // Admin  boleh akses
        $hasAccess = $user->roleUsers()
            ->where('id_komplek', $komplekId)
            ->whereHas('role', function ($query) {
                $query->whereIn('name', ['admin']);
            })->exists();

        log($hasAccess);
        //     log($komplekId);
        //     log($user->roleUsers()->get());
        // // log($user->roleUsers()->with('role')->where('id_komplek', $komplekId)->get();

        // log($hasAccess);

        if (! $hasAccess) {
            return response()->json(['message' => 'Forbidden: You are not assigned to this komplek'], 403);
        }

        return $next($request);
    }
}
