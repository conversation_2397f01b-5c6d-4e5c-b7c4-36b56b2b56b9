<?php

namespace App\Livewire\Asrama;

use App\Models\Komplek;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Mary\Traits\Toast;
use Livewire\WithFileUploads;

class AsramaCreate extends Component
{
    use Toast, WithFileUploads;

    public $nama_komplek;
    public $nama_pengasuh;
    public $komplek_type;
    public $description;
    public $logo;
    public $alamat;
    public $komplekId;
    public $kode_komplek;
    public $badan_status;

    public $alias_name;

    public $komplek_type_options = [['id' => 'putra', 'name' => 'Putra'], ['id' => 'putri', 'name' => 'Putri'], ['id' => 'putra_putri', 'name' => 'Putra & Putri']];

    public $badan_status_options = [['id' => 0, 'name' => 'Non Otonom'], ['id' => 1, 'name' => 'Otonom']];

    protected $rules = [
        'nama_komplek' => 'required|string',
        'nama_pengasuh' => 'required|string',
        'komplek_type' => 'required|string|in:putra,putri,putra_putri',
        'description' => 'required|string',
        'logo' => 'nullable|image', // Assumes logo is an image file, max 1MB
        'alamat' => 'required|string|max:255',
        'badan_status' => 'required|integer',
        'kode_komplek' => 'required|string',
        'alias_name' => 'required|string',
    ];

    protected $messages = [
        'nama_komplek.required' => 'Nama komplek wajib diisi.',
        'nama_pengasuh.required' => 'Nama pengasuh wajib diisi.',
        'komplek_type.required' => 'Tipe komplek wajib diisi.',
        'komplek_type.in' => 'Tipe komplek harus salah satu dari: putra, putri, atau putra_putri.',
        'description.required' => 'Deskripsi wajib diisi.',
        'logo.image' => 'Logo harus berupa file gambar.',
        'logo.max' => 'Ukuran logo tidak boleh lebih dari 1MB.',
        'alamat.required' => 'Alamat wajib diisi.',
        'alamat.max' => 'Alamat tidak boleh lebih dari 255 karakter.',
        'badan_status.required' => 'Badan status wajib diisi.',
        'badan_status.integer' => 'Badan status harus berupa angka.',
        'kode_komplek.required' => 'Kode komplek wajib diisi.',
        'kode_komplek.string' => 'Kode komplek harus berupa teks.',
        'alias_name.required' => 'Alias name wajib diisi.',
        'alias_name.string' => 'Alias name harus berupa teks.',
    ];

    public function mount($id = null)
    {
        logger('Komplek ID : ' . $id);
        if ($id) {
            $this->komplekId = $id;
            $this->loadKomplekData($id);
        } else {
            $this->logo = asset('images/logo.webp');
        }
    }

    public function updatedKomplekId($value)
    {
        logger('Komplek ID updated: ' . $value);
        $this->loadKomplekData($value);
    }

    private function loadKomplekData($id)
    {
        $komplek = Komplek::find($id);

        if (!$komplek) {
            abort(404, 'Data tidak ditemukan.');
        }

        $this->komplekId = $komplek->id;
        $this->nama_komplek = $komplek->nama_komplek;
        $this->nama_pengasuh = $komplek->nama_pengasuh;
        $this->komplek_type = $komplek->komplek_type;
        $this->description = $komplek->description;
        $this->alamat = $komplek->alamat;
        $this->logo = $komplek->logo ? asset('storage/' . $komplek->logo) : asset('images/logo.webp');
        $this->badan_status = $komplek->badan_status;
        $this->kode_komplek = $komplek->kode_komplek;
        $this->alias_name = $komplek->alias_name;
    }

    public function submit()
    {
        $validatedData = $this->validate([
            'nama_komplek' => 'required|string',
            'nama_pengasuh' => 'required|string',
            'komplek_type' => 'required|string|in:putra,putri,putra_putri',
            'description' => 'required|string',
            'logo' => $this->logo instanceof TemporaryUploadedFile ? 'image' : 'nullable',
            'alamat' => 'required|string|max:255',
            'badan_status' => 'required|integer',
            'kode_komplek' => 'required|string',
            'alias_name' => 'required|string',
        ]);

        try {
            $komplekData = [
                'nama_komplek' => $this->nama_komplek,
                'nama_pengasuh' => $this->nama_pengasuh,
                'komplek_type' => $this->komplek_type,
                'description' => $this->description,
                'alamat' => $this->alamat,
                'badan_status' => $this->badan_status,
                'kode_komplek' => $this->kode_komplek,
                'alias_name' => $this->alias_name,
            ];

            if ($this->logo instanceof TemporaryUploadedFile) {
                $komplekData['logo'] = $this->logo->store('logos', 'public');
            }

            if ($this->komplekId) {
                $komplek = Komplek::findOrFail($this->komplekId);
                if (!($this->logo instanceof TemporaryUploadedFile)) {
                    unset($komplekData['logo']); // Jangan ubah logo jika tidak ada yang diunggah
                }
                $komplek->update($komplekData);
                $this->success('Data komplek ' . $this->nama_komplek . ' berhasil diperbarui.', redirectTo: route('asrama.index'));
            } else {
                Komplek::create($komplekData);
                $this->success('Data ' . $this->nama_komplek . ' berhasil disimpan.', redirectTo: route('asrama.index'));
            }
        } catch (\Exception $e) {
            $this->error('Terjadi kesalahan saat menyimpan data.');
        }
    }

    public function render()
    {
        return view('livewire.asrama.asrama-create');
    }
}
