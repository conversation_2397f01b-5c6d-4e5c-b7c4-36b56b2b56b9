<?php

namespace App\Livewire\Asrama;

use App\Models\Komplek;
use Livewire\Component;
use Livewire\WithPagination;

class AsramaData extends Component
{
    use WithPagination;

    public $perPage = 15;

    public function mount()
    {
        $auth = auth()->user();

        if (!$auth->is_superadmin) {
            return abort(404);
        }
    }

    public function render()
    {
        $kompleks = Komplek::where('is_deleted', 0)->paginate($this->perPage);


        return view('livewire.asrama.asrama-data', [
            'kompleks' => $kompleks
        ]);
    }
}
