<?php

namespace App\Livewire\Asrama;

use App\Models\Komplek;
use Livewire\Component;

class AsramaDetail extends Component
{
    public $asrama;
    public $santriPerYear = [];

    public function mount(int $id)
    {
        $auth = auth()->user();

        // Jika bukan superadmin dan id_komplek pengguna tidak sama dengan id yang diminta, abort
        if (!$auth->is_superadmin && $auth->roleUsers()->value('id_komplek') !== $id) {
            abort(404);
        }

        $this->asrama = Komplek::withCount(['santris', 'kamar'])->findOrFail($id);
        $this->santriPerYear = $this->getSantriPerYear($id);
    }

    private function getSantriPerYear($kompleksId)
    {
        // Logika untuk mendapatkan jumlah santri dari tahun ke tahun
        // Ini adalah contoh sederhana, sesuaikan dengan struktur database Anda
        return [
            ['year' => 2020, 'count' => 50],
            ['year' => 2021, 'count' => 70],
            ['year' => 2022, 'count' => 90],
        ];
    }
    public function render()
    {
        return view('livewire.asrama.asrama-detail');
    }
}
