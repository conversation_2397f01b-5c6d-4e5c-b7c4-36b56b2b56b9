<?php

namespace App\Livewire;


use App\Services\SantriService;
use Livewire\Component;


class Dashboard extends Component
{

    public $showRangkuman = true;
    public $modalPengembangan = false;

    public $logo = "";
    public $name = "";

    public function mount(){
        $user = auth()->user();
        if ($user->isAdmin) {
            $komplek = $user->assignedKomplek();

            if ($komplek) {

                $this -> name = $komplek -> nama_komplek;
                if ($komplek -> logo){
                    $this->logo = asset( 'storage/'.$komplek -> logo);

                }
            }

        } else {
            $this -> name = 'Al Munawwir';
            $this -> logo = asset('images/default.png');
        }


    }

    public function render()
    {

        return view('livewire.dashboard');
    }
}


