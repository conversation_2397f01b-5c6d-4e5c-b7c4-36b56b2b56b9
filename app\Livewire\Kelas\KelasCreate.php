<?php

namespace App\Livewire\Kelas;

use App\Models\KelasCategory;
use App\Models\Komplek;
use Livewire\Component;

class KelasCreate extends Component
{
    public $kelasId;
    public $nama_kelas;
    public $id_kategori;
    public $tahun_ajaran;
    public $deskripsi;
    public $id_komplek;

    public function mount($id = null){
        $this->kelasId = $id;
    }
    public function render()
    {
        return view('livewire.kelas.kelas-create', [
            'kompleks' => $this->kompleks,
            'kelasCategory' => $this->kelasCategory,
        ]);
    }



    public function  submit()
    {

    }

    public function getKompleksProperty()
    {
        return Komplek::all()->map(function ($komplek) {
            return [
                'id' => $komplek->id,
                'name' => $komplek->nama_komplek,
            ];
        })->toArray();
    }

    public function getKelasCategoryProperty()
    {
        return KelasCategory::all()->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->nama_kategori,
            ];
        })->toArray();
    }


}
