<?php

namespace App\Livewire\Santri;

use App\Models\Komplek;
use App\Models\Santri;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class SantriData extends Component
{
    use WithPagination;

    public $showFilterDrawer = false;

    #[Url(history: true)]
    public $komplek;

    #[Url(history: true)]
    public $jenis_kelamin;

    #[Url(history: true)]
    public $tahun_masuk;

    #[Url(history: true)]
    public $status;
    #[Url(history: true)]
    public $sortField = 'nama';
    #[Url(history: true)]
    public $sortDirection = 'asc';
    #[Url(history: true)]
    public $perPage = 15;

    #[Url(history: true)]
    public $search = '';

    #[Url(history: true)]
    public $kabupaten = '';

    public function updatedFilter()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function getKompleksProperty()
    {
        $user = Auth::user();
        $userKomplekId = $user->is_admin ? $user->assignedKomplek()->id : null;

        if ($userKomplekId) {
            return Komplek::where('id', $userKomplekId)->get()->map(function ($komplek) {
                return [
                    'id' => $komplek->id,
                    'nama_komplek' => $komplek->nama_komplek,
                ];
            })->toArray();
        }

        return Komplek::all()->map(function ($komplek) {
            return [
                'id' => $komplek->id,
                'nama_komplek' => $komplek->nama_komplek,
            ];
        })->toArray();
    }

    public function render()
    {
        // Periksa apakah pengguna adalah admin yang terkait dengan komplek tertentu
        $user = Auth::user();
        $userKomplekId = $user->is_admin ? $user->id_komplek : null;

        $santri = Santri::query()
            ->when($this->komplek || $userKomplekId, function ($query) use ($userKomplekId) {
                $query->where('id_komplek', $this->komplek ?? $userKomplekId);
            })
            ->when($this->jenis_kelamin, function ($query) {
                $query->where('jenis_kelamin', $this->jenis_kelamin);
            })
            ->when($this->tahun_masuk, function ($query) {
                $query->whereYear('tahun_masuk', $this->tahun_masuk);
            })
            ->when($this->status, function ($query) {
                if ($this->status === 'is_aktif') {
                    $query->where('is_aktif', true);
                } elseif ($this->status === 'is_nonaktif') {
                    $query->where('is_nonaktif', true);
                } elseif ($this->status === 'is_alumni') {
                    $query->where('is_alumni', true);
                }
            })
            ->when($this->kabupaten, function ($query) {
                $query->whereHas('kabupaten', function ($subQuery) {
                    $subQuery->where('code', $this->kabupaten);
                });
            })
            ->when($this->search, function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('nama', 'like', '%' . $this->search . '%')
                        ->orWhere('alamat', 'like', '%' . $this->search . '%')
                        ->orWhere('nomor_induk', 'like', '%' . $this->search . '%')
                        ->orWhere('nomor_induk_baru', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.santri.santri-data', [
            'santri' => $santri,
            'kompleks' => $this->kompleks,
        ]);
    }


    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function applyFilters()
    {
        $this->showFilterDrawer = false;
    }

    public function resetFilters()
    {
        $this->reset(['komplek', 'jenis_kelamin', 'tahun_masuk', 'status', 'sortField', 'sortDirection', 'search', 'kabupaten']);
        $this->showFilterDrawer = false;
    }
}
