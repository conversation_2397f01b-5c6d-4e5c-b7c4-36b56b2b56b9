<?php

namespace App\Livewire\Santri;

use App\Models\Komplek;
use App\Models\Santri;
use App\Services\NomorIndukService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Laravolt\Indonesia\Models\City;
use Laravolt\Indonesia\Models\District;
use Laravolt\Indonesia\Models\Province;
use Livewire\Attributes\Rule;
use Livewire\Component;
use Livewire\WithFileUploads;
use Mary\Traits\Toast;

class SantriDetail extends Component
{
    use Toast, WithFileUploads;

    public $santri;

    #[Rule(['fotoSantri' => 'required'])]
    public $fotoSantri;

    public $dataDiriDrawer = 'data-diri';
    public $dataOrangTuaDrawer = 'data-orang-tua';
    public $dataPendidikanDrawer = 'data-pendidikan';
    public $dataLainnyaDrawer = 'data-lainnya';

    public $editDataDiri = false;
    public $editDataOrangTua = false;
    public $editDataPendidikan = false;
    public $editDataLainnya = false;

    public $drawerType;
    public $showDrawer = false;

    public $nama, $tempat_lahir, $tanggal_lahir, $jenis_kelamin, $hp, $email, $alamat, $kabupaten, $kecamatan, $provinsi, $nik, $kk;
    public $nama_ayah, $nama_ibu, $pekerjaan_ayah, $pekerjaan_ibu, $hp_orang_tua, $alamat_orang_tua;
    public $pendidikan_terakhir, $pendidikan_formal = [], $pendidikan_non_formal = [];
    public $tahun_masuk, $komplek, $status_santri;

    public $tempPendidikanFormal = ['pendidikan' => '', 'instansi' => '', 'tahun_lulus' => ''];
    public $tempPendidikanNonFormal = ['instansi' => '', 'tahun_lulus' => ''];
    public $jenis_kelamin_options;

    public $initialFoto;
    public $provinces;
    public $cities;
    public $districts;

    public $pendidikanOptions;
    public $komplekOptions;
    public $statusSantriOptions;
    public $edit_mode = false;

    public $modalNomorInduk = false;
    public $modalStatusSantri = false;
    public $modalDeleteSantri = false;

    public $alasan = '';
    public $active_at = '';
    public $non_active_at = '';
    public $alumni_at = '';


    public $santriMenus;

    public $jumlahDuplikat;

    public function mount($id)
    {

        $user = auth()->user();  // Mendapatkan data pengguna yang sedang login

        $this->santri = Santri::with('komplek')->find($id);

        // Jika santri tidak ditemukan, redirect ke 404
        if (!$this->santri) {
            abort(404);
        }

        $komplek = $user->assignedKomplek();

        // Cek role pengguna: Jika bukan superadmin, lakukan pengecekan id_komplek
        if (!$user->hasRole('superadmin') && $komplek->id !== $this->santri->id_komplek) {
            abort(404);  // Jika id_komplek tidak sesuai, arahkan ke 404
        }

        $this->komplekOptions = Komplek::all()->map(function ($komplek) {
            return [
                'id' => $komplek->id,
                'name' => $komplek->nama_komplek
            ];
        });

        $const_status_santri = config('constants.status_santri');

        $this->statusSantriOptions = array_map(function ($name, $id) {
            return ['id' => $id, 'name' => $name];
        }, $const_status_santri, array_keys($const_status_santri));

        $this->fillData();

        $this->provinces = Province::all()->map(function ($province) {
            return [
                'id' => $province->code,
                'name' => $province->name,
            ];
        })->toArray();

        if ($this->santri->kabupaten !== null && $this->santri->kabupaten->province_code !== null) {
            $this->cities = City::where('province_code', $this->santri->kabupaten->province_code)->get()->map(function ($city) {
                return [
                    'id' => $city->code,
                    'name' => $city->name,
                ];
            })->toArray();
        }

        if ($this->santri->kecamatan !== null && $this->santri->kecamatan->city_code !== null) {
            $this->districts = District::where('city_code', $this->santri->kecamatan->city_code)->get()->map(function ($district) {
                return [
                    'id' => $district->code,
                    'name' => $district->name,
                ];
            })->toArray();
        }

        $this->initialFoto = $this->santri->foto;

        $this->jenis_kelamin_options = collect(config('constants.jenis_kelamin'))
            ->map(function ($name, $id) {
                return ['id' => $id, 'name' => $name];
            })->toArray();

        $this -> santriMenus = [
            ['name' => 'Data Diri', 'link' => '#', 'drawerType' => $this -> dataDiriDrawer],
            ['name' => 'Data Orang Tua', 'link' => '#', 'drawerType' => $this -> dataOrangTuaDrawer],
            ['name' => 'Data Pendidikan', 'link' => '#', 'drawerType' => $this -> dataPendidikanDrawer],
            ['name' => 'Data Lainnya', 'link' => '#', 'drawerType' => $this -> dataLainnyaDrawer],
        ];

        $this->countDuplicateData();
    }

    public function countDuplicateData(){
        $this->jumlahDuplikat = Santri::where('nama', $this->santri->nama)
            ->where('tanggal_lahir', $this->santri->tanggal_lahir)
            ->where('id_komplek', $this->santri->id_komplek)
            ->where('id', '!=', $this->santri->id) // Hindari menghitung dirinya sendiri
            ->count();
    }

    public function render()
    {
        return view('livewire.santri.santri-detail', [
            'santri' => $this->santri
        ]);
    }

    public function updatedProvinsi($value)
    {
        $this->cities = City::where('province_code', $value)->get()->map(function ($city) {
            return [
                'id' => $city->code,
                'name' => $city->name,
            ];
        })->toArray();
    }

    public function updatedKabupaten($value): void
    {
        $this->districts = District::where('city_code', $value)->get()->map(function ($district) {
            return [
                'id' => $district->code,
                'name' => $district->name,
            ];
        })->toArray();
    }

    public function getFotoUrl()
    {
        if ($this->cekFoto()) {
            return asset('storage/' . $this->santri->foto);
        } elseif ($this->santri->jenis_kelamin === 'laki-laki') {
            return asset('images/putra.svg');
        } else {
            return asset('images/putri.svg');
        }
    }

    public function cekFoto()
    {
        if ($this->santri->foto === 'photos/') {
            return false;
        }
        return $this->santri->foto && Storage::disk('public')->exists($this->santri->foto) ? true : false;
    }

    public function openDrawer($drawerType)
    {
        $this->drawerType = $drawerType;
        $this->showDrawer = true;
    }

    public function closeDrawer()
    {
        $this->drawerType = null;
        $this->showDrawer = false;
        $this->resetEditFlags();
    }

    public function openEdit($drawerType)
    {

        $this->resetEditFlags();

        $this->edit_mode = true;
        switch ($drawerType) {
            case $this->dataDiriDrawer:
                $this->editDataDiri = true;
                break;
            case $this->dataOrangTuaDrawer:
                $this->editDataOrangTua = true;
                break;
            case $this->dataPendidikanDrawer:
                $this->editDataPendidikan = true;
                break;
            case $this->dataLainnyaDrawer:
                $this->editDataLainnya = true;
                break;
        }
    }

    public function saveDataDiri()
    {
        $this->validate([
            'nama' => 'required|string|max:255',
            'tempat_lahir' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|string|in:laki-laki,perempuan',
            'hp' => 'required|string|max:15',
            'email' => 'required|email|max:255',
            'alamat' => 'required|string|max:255',
            'kabupaten' => 'required|string|max:255',
            'provinsi' => 'required|string|max:255',
            'kecamatan' => 'required|string|max:255',
            'nik' => 'string|max:16|nullable',
            'kk' => 'string|max:16|nullable',
        ]);

        $this->santri->update([
            'nama' => $this->nama,
            'tempat_lahir' => $this->tempat_lahir,
            'tanggal_lahir' => $this->tanggal_lahir,
            'jenis_kelamin' => $this->jenis_kelamin,
            'hp' => $this->hp,
            'email' => $this->email,
            'alamat' => $this->alamat,
            'kabupaten' => $this->kabupaten,
            'provinsi' => $this->provinsi,
            'kecamatan' => $this->kecamatan,
            'nik' => $this->nik,
            'kk' => $this->kk,
        ]);

        $this->closeDrawer();
        $this->fillData();
        $this->resetEditFlags();

        $this->success('Data diri santri berhasil disimpan');
    }

    public function saveDataOrangTua()
    {
        $this->validate([
            'nama_ayah' => 'required|string|max:255',
            'nama_ibu' => 'required|string|max:255',
            'pekerjaan_ayah' => 'required|string|max:255',
            'pekerjaan_ibu' => 'required|string|max:255',
            'hp_orang_tua' => 'required|string|max:15',
            'alamat_orang_tua' => 'required|string|max:255',
        ]);

        $this->santri->update([
            'nama_ayah' => $this->nama_ayah,
            'nama_ibu' => $this->nama_ibu,
            'pekerjaan_ayah' => $this->pekerjaan_ayah,
            'pekerjaan_ibu' => $this->pekerjaan_ibu,
            'hp_orang_tua' => $this->hp_orang_tua,
            'alamat_orang_tua' => $this->alamat_orang_tua,
        ]);

        $this->closeDrawer();
        $this->fillData();
        $this->resetEditFlags();

        $this->success('Data orang tua santri berhasil disimpan');
    }

    public function saveDataPendidikan()
    {
        $this->validate([
            'pendidikan_terakhir' => 'required|string|max:255',
        ]);

        $this->santri->update([
            'pendidikan_terakhir' => $this->pendidikan_terakhir,
            'pendidikan_formal' => $this->pendidikan_formal,
            'pendidikan_non_formal' => $this->pendidikan_non_formal,
        ]);

        $this->closeDrawer();
        $this->fillData();
        $this->resetEditFlags();

        $this->success('Data pendidikan santri berhasil disimpan');
    }

    public function deleteSantri(){
        $this->santri->delete();
        $this->success('Data santri berhasil dihapus', redirectTo: route('santri.index'));
    }

    public function saveStatusSantri()
    {

        $this->validate(
            [
                'status_santri' => 'required',
            ],
            [
                'status_santri.required' => 'Status santri harus dipilih',
            ]
        );

        if ($this->status_santri == 0){
            $this->santri->update([
                'is_aktif' => 0,
                'is_nonaktif' => 1,
                'is_alumni' => 0,
                'nonaktif_at' => $this->non_active_at,
                'alumni_at' => null
            ]);

            $this->success('Status Santri diubah menjadi Nonaktif');

        } elseif ($this->status_santri == 1){
            $this->santri->update([
                'is_aktif' => 1,
                'is_nonaktif' => 0,
                'is_alumni' => 0,
                'nonaktif_at' => null,
                'alumni_at' => null
            ]);

            $this->success('Status Santri diubah menjadi Aktif');

        } elseif ($this->status_santri == 2){
            $this->santri->update([
                'is_aktif' => 0,
                'is_nonaktif' => 0,
                'is_alumni' => 1,
                'nonaktif_at' => null,
                'alumni_at' => $this->alumni_at,
            ]);

            $this->success('Status Santri diubah menjadi Alumni');

        } else {
            $this->error('Status santri tidak valid');
        }



        // exit
        $this->modalStatusSantri = false;
    }

    public function saveDataLainnya()
    {
        // Validasi data input
        $this->validate(
            [
                'tahun_masuk' => 'required|integer|min:1980|max:' . (date('Y') + 1),
                'komplek' => 'required',
            ],
            [
                'tahun_masuk.required' => 'Tahun masuk harus diisi',
                'tahun_masuk.integer' => 'Tahun masuk harus berupa angka',
                'tahun_masuk.min' => 'Tahun masuk tidak boleh kurang dari 1980',
                'tahun_masuk.max' => 'Tahun masuk tidak boleh lebih dari ' . (date('Y') + 1),
                'komplek.required' => 'Komplek harus diisi',
            ]
        );

        $tahun_masuk_before = $this->santri->tahun_masuk;
        $komplek_before = $this->santri->komplek;

        // Menentukan status santri
        //        $is_aktif = $this->status_santri === 'aktif';
        //        $is_nonaktif = $this->status_santri === 'nonaktif';
        //        $is_alumni = $this->status_santri === 'alumni';

        // Update data santri
        $this->santri->update([
            'tahun_masuk' => $this->tahun_masuk,
            'komplek' => $this->komplek,
            //            'is_aktif' => $is_aktif,
            //            'is_nonaktif' => $is_nonaktif,
            //            'is_alumni' => $is_alumni,
        ]);


        // Cek apakah tahun masuk dan komplek berubah
        if ($tahun_masuk_before !== $this->tahun_masuk || $komplek_before !== $this->komplek) {
            // Reset nomor induk
            $this->resetNomorInduk();
        }



        $this->closeDrawer();
        $this->fillData();
        $this->resetEditFlags();

        $this->success('Data santri berhasil disimpan');
    }

    protected function resetNomorInduk()
    {
        // Reset nomor induk lama
        $this->santri->nomor_induk_baru = null;
        $this->santri->save();

        // Generate nomor induk baru
        $nomorIndukService = new NomorIndukService();
        $nomorIndukService->generateNomorIndukForSingleSantri($this->santri);
    }


    public function addPendidikanFormal()
    {
        $this->validate(
            [
                'tempPendidikanFormal.pendidikan' => 'required|string',
                'tempPendidikanFormal.instansi' => 'required|string',
                'tempPendidikanFormal.tahun_lulus' => 'required|digits:4|integer|min:1980|max:' . (date('Y') + 1),
            ],
            [
                'tempPendidikanFormal.pendidikan.required' => 'Pendidikan wajib diisi',
                'tempPendidikanFormal.instansi.required' => 'Instansi wajib diisi',
                'tempPendidikanFormal.tahun_lulus.required' => 'Tahun lulus wajib diisi',
                'tempPendidikanFormal.tahun_lulus.integer' => 'Tahun lulus harus berupa angka',
                'tempPendidikanFormal.tahun_lulus.min' => 'Tahun lulus minimal 1980',
                'tempPendidikanFormal.tahun_lulus.max' => 'Tahun lulus maksimal ' . (date('Y') + 1),
            ]
        );
        $this->pendidikan_formal[] = $this->tempPendidikanFormal;
        $this->tempPendidikanFormal = ['pendidikan' => '', 'instansi' => '', 'tahun_lulus' => ''];
    }

    public function removePendidikanFormal($index)
    {
        unset($this->pendidikan_formal[$index]);
        $this->pendidikan_formal = array_values($this->pendidikan_formal);
    }

    public function addPendidikanNonFormal()
    {
        $this->pendidikan_non_formal[] = $this->tempPendidikanNonFormal;
        $this->tempPendidikanNonFormal = ['instansi' => '', 'tahun_lulus' => ''];
    }

    public function removePendidikanNonFormal($index)
    {
        unset($this->pendidikan_non_formal[$index]);
        $this->pendidikan_non_formal = array_values($this->pendidikan_non_formal);
    }

    public function generateNomorInduk()
    {
        //        dd($this->santri);
        $nomorIndukSrv = new NomorIndukService();
        $nomorInduk = $nomorIndukSrv->generateNomorIndukForSingleSantri($this->santri);

        $this->santri->nomor_induk_baru = $nomorInduk;
    }

    private function fillData()
    {
        $this->nama = $this->santri->nama;
        $this->tempat_lahir = $this->santri->tempat_lahir;
        $this->tanggal_lahir = $this->santri->tanggal_lahir;
        $this->jenis_kelamin = $this->santri->jenis_kelamin;
        $this->hp = $this->santri->hp;
        $this->email = $this->santri->email;
        $this->alamat = $this->santri->alamat;
        $this->kabupaten = $this->santri->city_code;
        $this->provinsi = $this->santri->kabupaten->province_code;
        $this->kecamatan = $this->santri->district_code;
        $this->nik = $this->santri->nik;
        $this->kk = $this->santri->kk;

        $this->nama_ayah = $this->santri->nama_ayah;
        $this->nama_ibu = $this->santri->nama_ibu;
        $this->pekerjaan_ayah = $this->santri->pekerjaan_ayah;
        $this->pekerjaan_ibu = $this->santri->pekerjaan_ibu;
        $this->hp_orang_tua = $this->santri->hp_orang_tua;
        $this->alamat_orang_tua = $this->santri->alamat_orang_tua;

        $this->pendidikan_terakhir = $this->santri->pendidikan_terakhir;
        $this->pendidikan_formal = $this->santri->pendidikan_formal ?? [];
        $this->pendidikan_non_formal = $this->santri->pendidikan_non_formal ?? [];
        $this->pendidikanOptions = config('constants.pendidikan_options');

        $this->tahun_masuk = $this->santri->tahun_masuk;
        $this->komplek = $this->santri->komplek->id;



        if ($this->santri->is_aktif) {
            $this->status_santri = 1;
        } elseif ($this->santri->is_nonaktif) {
            $this->status_santri = 0;
        } else {
            $this->status_santri = 2;
        }

        $this->alasan = $this->santri->alasan;
        $this->non_active_at = $this->santri->nonaktif_at;
        $this->alumni_at = $this->santri->alumni_at;
    }

    private function resetEditFlags()
    {
        $this->editDataDiri = false;
        $this->editDataOrangTua = false;
        $this->editDataPendidikan = false;
        $this->editDataLainnya = false;

        $this->edit_mode = false;
    }

    public function saveFotoSantri()
    {
        $this->validate([
            'fotoSantri' => 'required|image|max:1024',
        ]);

        $this->santri->update([
            'foto' => $this->fotoSantri->store('santri-foto', 'public'),
        ]);

        $this->success('Foto santri berhasil disimpan');
        $this->fotoSantri = null;
        $this->initialFoto = $this->santri->foto;
    }

    public function cancelFotoSantri()
    {
        $this->reset('fotoSantri');
        $this->santri->foto = $this->initialFoto;
    }

    public function closeEdit()
    {

        $this->resetEditFlags();
        $this->fillData();
    }
}
