<?php

namespace App\Livewire\Santri;

use App\Models\Komplek;
use App\Models\Santri;
use App\Services\NomorIndukService;
use Illuminate\Support\Collection;
use Laravolt\Indonesia\Models\District;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithFileUploads;
use Laravolt\Indonesia\Models\Province;
use Laravolt\Indonesia\Models\City;
use Mary\Traits\Toast;

class SantriWizard extends Component
{
    use Toast;
    use WithFileUploads;

    public $currentStep = 1;

    public Collection $kompleks;

    // Step 1 data

    public $nama;
    public $tempat_lahir;
    public $tanggal_lahir;
    public $jenis_kelamin;
    public $hp;
    public $email;
    public $alamat;
    public $nik;
    public $kk;

    // Step 2 data
    public $nama_ayah;
    public $nama_ibu;
    public $pekerjaan_ayah;
    public $pekerjaan_ibu;
    public $hp_orang_tua;
    public $alamat_orang_tua;
    public $anak_ke;
    public $jumlah_saudara;

    // Step 3 data
    public $pendidikan_terakhir;
    public $pendidikan_formal = [];
    public $pendidikan_non_formal = [];

    public $id_komplek;
    public $diterima_at;
    public $tahun_masuk;
    public $alasan;

    public $provinces;
    public $cities;
    public $districts;
    public $selectedProvince = null;
    public $selectedCity = null;

    public $selectedDistrict = null;

    public $jenisKelaminOpts = [];

    protected $rules = [
        // Step 1 validation rules
        'nama' => 'required|string',
        'tempat_lahir' => 'required|string',
        'tanggal_lahir' => 'required|date',
        'jenis_kelamin' => 'required|string',
        'hp' => 'required|string',
        'email' => 'required|email',
        'alamat' => 'required|string',
        'selectedCity' => 'required',
        'selectedDistrict' => 'required',
        'selectedProvince' => 'required',
        'nik' => 'nullable|string|max:16',
        'kk' => 'nullable|string|max:16',

        // Step 2 validation rules
        'nama_ayah' => 'required|string',
        'nama_ibu' => 'required|string',
        'pekerjaan_ayah' => 'required|string',
        'pekerjaan_ibu' => 'required|string',
        'hp_orang_tua' => 'required|string',
        'alamat_orang_tua' => 'required|string',
        'anak_ke' => 'required|integer',
        'jumlah_saudara' => 'required|integer',

        // Step 3 validation rules
        'pendidikan_terakhir' => 'required|string',
        'pendidikan_formal' => 'nullable|array',
        'pendidikan_non_formal' => 'nullable|array',
        'id_komplek' => 'required|integer',
        'diterima_at' => 'required|date',
        'tahun_masuk' => 'required|integer',
        'alasan' => 'required|string',
    ];

    protected $messages = [
        // Step 1 validation messages
        'nama.required' => 'Nama wajib diisi.',
        'nama.string' => 'Nama harus berupa teks.',
        'tempat_lahir.required' => 'Tempat lahir wajib diisi.',
        'tempat_lahir.string' => 'Tempat lahir harus berupa teks.',
        'tanggal_lahir.required' => 'Tanggal lahir wajib diisi.',
        'tanggal_lahir.date' => 'Tanggal lahir harus berupa tanggal yang valid.',
        'jenis_kelamin.required' => 'Jenis kelamin wajib diisi.',
        'jenis_kelamin.string' => 'Jenis kelamin harus berupa teks.',
        'hp.required' => 'Nomor HP wajib diisi.',
        'hp.string' => 'Nomor HP harus berupa teks.',
        'email.required' => 'Email wajib diisi.',
        'email.email' => 'Email harus berupa format email yang valid.',
        'alamat.required' => 'Alamat wajib diisi.',
        'alamat.string' => 'Alamat harus berupa teks.',
        'selectedCity.required' => 'Kota wajib dipilih.',
        'selectedDistrict.required' => 'Kecamatan wajib dipilih.',
        'selectedProvince.required' => 'Provinsi wajib dipilih.',
        'nik.string' => 'NIK harus berupa teks.',
        'nik.max' => 'NIK tidak boleh lebih dari 16 karakter.',
        'kk.string' => 'KK harus berupa teks.',
        'kk.max' => 'KK tidak boleh lebih dari 16 karakter.',

        // Step 2 validation messages
        'nama_ayah.required' => 'Nama ayah wajib diisi.',
        'nama_ayah.string' => 'Nama ayah harus berupa teks.',
        'nama_ibu.required' => 'Nama ibu wajib diisi.',
        'nama_ibu.string' => 'Nama ibu harus berupa teks.',
        'pekerjaan_ayah.required' => 'Pekerjaan ayah wajib diisi.',
        'pekerjaan_ayah.string' => 'Pekerjaan ayah harus berupa teks.',
        'pekerjaan_ibu.required' => 'Pekerjaan ibu wajib diisi.',
        'pekerjaan_ibu.string' => 'Pekerjaan ibu harus berupa teks.',
        'hp_orang_tua.required' => 'Nomor HP orang tua wajib diisi.',
        'hp_orang_tua.string' => 'Nomor HP orang tua harus berupa teks.',
        'alamat_orang_tua.required' => 'Alamat orang tua wajib diisi.',
        'alamat_orang_tua.string' => 'Alamat orang tua harus berupa teks.',
        'anak_ke.required' => 'Urutan anak wajib diisi.',
        'anak_ke.integer' => 'Urutan anak harus berupa angka.',
        'jumlah_saudara.required' => 'Jumlah saudara wajib diisi.',
        'jumlah_saudara.integer' => 'Jumlah saudara harus berupa angka.',

        // Step 3 validation messages
        'pendidikan_terakhir.required' => 'Pendidikan terakhir wajib diisi.',
        'pendidikan_terakhir.string' => 'Pendidikan terakhir harus berupa teks.',
        'pendidikan_formal.array' => 'Pendidikan formal harus berupa array.',
        'pendidikan_non_formal.array' => 'Pendidikan nonformal harus berupa array.',
        'id_komplek.required' => 'Komplek wajib dipilih.',
        'id_komplek.integer' => 'Komplek harus berupa angka.',
        'diterima_at.required' => 'Tanggal diterima wajib diisi.',
        'diterima_at.date' => 'Tanggal diterima harus berupa tanggal yang valid.',
        'tahun_masuk.required' => 'Tahun masuk wajib diisi.',
        'tahun_masuk.integer' => 'Tahun masuk harus berupa angka.',
        'alasan.required' => 'Alasan wajib diisi.',
        'alasan.string' => 'Alasan harus berupa teks.',

        'tempPendidikanFormal.pendidikan.required' => 'Pendidikan wajib diisi.',
        'tempPendidikanFormal.instansi.required' => 'Instansi wajib diisi.',
        'tempPendidikanFormal.tahun_lulus.required' => 'Tahun lulus wajib diisi.',
        'tempPendidikanFormal.tahun_lulus.digits' => 'Tahun lulus harus terdiri dari 4 digit.',
        'tempPendidikanFormal.tahun_lulus.integer' => 'Tahun lulus harus berupa angka.',
        'tempPendidikanFormal.tahun_lulus.min' => 'Tahun lulus tidak boleh kurang dari 1900.',
        'tempPendidikanFormal.tahun_lulus.max' => 'Tahun lulus tidak boleh lebih dari tahun saat ini.',
        'tempPendidikanNonFormal.instansi.required' => 'Instansi wajib diisi.',
        'tempPendidikanNonFormal.tahun_lulus.required' => 'Tahun lulus wajib diisi.',
        'tempPendidikanNonFormal.tahun_lulus.digits' => 'Tahun lulus harus terdiri dari 4 digit.',
        'tempPendidikanNonFormal.tahun_lulus.integer' => 'Tahun lulus harus berupa angka.',
        'tempPendidikanNonFormal.tahun_lulus.min' => 'Tahun lulus tidak boleh kurang dari 1900.',
        'tempPendidikanNonFormal.tahun_lulus.max' => 'Tahun lulus tidak boleh lebih dari tahun saat ini.',
    ];

    public $pendidikanOptions;

    public $pendidikanFormalModal = false;
    public $pendidikanNonFormalModal = false;
    public $tempPendidikanFormal = ['pendidikan' => '', 'instansi' => '', 'tahun_lulus' => ''];
    public $tempPendidikanNonFormal = ['instansi' => '', 'tahun_lulus' => ''];

    public function mount()
    {
        $user = auth()->user();

        // Ambil komplek yang terkait dengan role admin
        $komplek = $user->assignedKomplek();

        if ($komplek) {
            $komplekType = $komplek->komplek_type;

            // Tentukan opsi jenis kelamin berdasarkan komplek_type
            if ($komplekType === 'putra') {
                $this->jenisKelaminOpts = [['id' => 'laki-laki', 'name' => 'Laki-laki']];
            } elseif ($komplekType === 'putri') {
                $this->jenisKelaminOpts = [['id' => 'perempuan', 'name' => 'Perempuan']];
            } else {
                // putra_putri
                $this->jenisKelaminOpts = [['id' => 'laki-laki', 'name' => 'Laki-laki'], ['id' => 'perempuan', 'name' => 'Perempuan']];
            }
        } else {
            $this->jenisKelaminOpts = [['id' => 'laki-laki', 'name' => 'Laki-laki'], ['id' => 'perempuan', 'name' => 'Perempuan']];
        }

        $this->searchKompleks();
        $this->pendidikanOptions = config('constants.pendidikan_options');

        $this->provinces = Province::all()
            ->map(function ($province) {
                return [
                    'id' => $province->code,
                    'name' => $province->name,
                ];
            })
            ->toArray();
        $this->selectedProvince = null;
    }

    public function render()
    {
        return view('livewire.santri.santri-wizard');
    }

    public function updatedSelectedProvince($value)
    {
        $this->cities = City::where('province_code', $value)
            ->get()
            ->map(function ($city) {
                return [
                    'id' => $city->code,
                    'name' => $city->name,
                ];
            })
            ->toArray();
    }

    public function updatedSelectedCity($value)
    {
        $this->districts = District::where('city_code', $value)
            ->get()
            ->map(function ($district) {
                return [
                    'id' => $district->code,
                    'name' => $district->name,
                ];
            })
            ->toArray();
    }

    public function searchKompleks(string $value = '', int $extra1 = 0, string $extra2 = '')
    {
        $user = auth()->user(); // Mendapatkan pengguna yang sedang login

        $query = Komplek::query()->where('nama_komplek', 'like', "%$value%");

        // Jika pengguna bukan superadmin, filter dengan id_komplek pengguna
        if (!$user->hasRole('superadmin')) {
            $query->where('id', $user->id_komplek);
        }

        $this->kompleks = $query->get()->map(function ($komplek) {
            return [
                'id' => $komplek->id,
                'name' => $komplek->nama_komplek,
                'avatar' => $komplek->logo,
            ];
        });
    }

    public function addPendidikanFormal()
    {
        $currentYear = date('Y');

        $this->validate([
            'tempPendidikanFormal.pendidikan' => 'required|string',
            'tempPendidikanFormal.instansi' => 'required|string',
            'tempPendidikanFormal.tahun_lulus' => "required|digits:4|integer|min:1900|max:$currentYear",
        ]);

        $this->pendidikan_formal[] = $this->tempPendidikanFormal;
        $this->tempPendidikanFormal = ['pendidikan' => '', 'instansi' => '', 'tahun_lulus' => ''];
        $this->pendidikanFormalModal = false;
    }

    public function addPendidikanNonFormal()
    {
        $currentYear = date('Y');

        $this->validate([
            'tempPendidikanNonFormal.instansi' => 'required|string',
            'tempPendidikanNonFormal.tahun_lulus' => "required|digits:4|integer|min:1900|max:$currentYear",
        ]);

        $this->pendidikan_non_formal[] = $this->tempPendidikanNonFormal;
        $this->tempPendidikanNonFormal = ['instansi' => '', 'tahun_lulus' => ''];
        $this->pendidikanNonFormalModal = false;
    }

    public function removePendidikanFormal($index)
    {
        unset($this->pendidikan_formal[$index]);
        $this->pendidikan_formal = array_values($this->pendidikan_formal);
    }

    public function removePendidikanNonFormal($index)
    {
        unset($this->pendidikan_non_formal[$index]);
        $this->pendidikan_non_formal = array_values($this->pendidikan_non_formal);
    }

    public function increaseStep()
    {
        $this->resetErrorBag();

        if ($this->currentStep === 1) {
            $this->validate([
                'nama' => 'required|string',
                'tempat_lahir' => 'required|string',
                'tanggal_lahir' => 'required|date',
                'jenis_kelamin' => 'required|string',
                'hp' => 'required|string',
                'email' => 'required|email',
                'alamat' => 'required|string',
                'selectedCity' => 'required',
                'selectedProvince' => 'required',
                'nik' => 'required|string|max:16',
                'kk' => 'required|string|max:16',
            ]);
        } elseif ($this->currentStep === 2) {
            $this->validate([
                'nama_ayah' => 'required|string',
                'nama_ibu' => 'required|string',
                'pekerjaan_ayah' => 'required|string',
                'pekerjaan_ibu' => 'required|string',
                'hp_orang_tua' => 'required|string',
                'alamat_orang_tua' => 'required|string',
                'anak_ke' => 'required|integer',
                'jumlah_saudara' => 'required|integer',
            ]);
            return;
        }

        $this->currentStep++;
    }

    public function decreaseStep()
    {
        $this->currentStep--;
    }

    public function submit()
    {
        $this->validate();

        // Cek apakah santri dengan data unik sudah ada
        $existingSantri = Santri::where(function ($query) {
                $query->where('nama', $this->nama)->where('tempat_lahir', $this->tempat_lahir)->where('tanggal_lahir', $this->tanggal_lahir);
            })
            ->first();

        if ($existingSantri) {
            session()->flash('error', 'Data santri sudah ada!');
            $this->error('Data santri sudah ada!');
            // return;
        }

        // Create Santri record
        $santri = Santri::create([
            'nama' => $this->nama,
            'tempat_lahir' => $this->tempat_lahir,
            'tanggal_lahir' => $this->tanggal_lahir,
            'jenis_kelamin' => $this->jenis_kelamin,
            'hp' => $this->hp,
            'email' => $this->email,
            'alamat' => $this->alamat,
            'city_code' => $this->selectedCity,
            'district_code' => $this->selectedDistrict,
            'nik' => $this->nik,
            'kk' => $this->kk,
            'nama_ayah' => $this->nama_ayah,
            'nama_ibu' => $this->nama_ibu,
            'pekerjaan_ayah' => $this->pekerjaan_ayah,
            'pekerjaan_ibu' => $this->pekerjaan_ibu,
            'hp_orang_tua' => $this->hp_orang_tua,
            'alamat_orang_tua' => $this->alamat_orang_tua,
            'anak_ke' => $this->anak_ke,
            'jumlah_saudara' => $this->jumlah_saudara,
            'pendidikan_terakhir' => $this->pendidikan_terakhir,
            'pendidikan_formal' => $this->pendidikan_formal,
            'pendidikan_non_formal' => $this->pendidikan_non_formal,
            'id_komplek' => $this->id_komplek,
            'id_admin' => auth()->user()->id,
            'diterima' => true,
            'diterima_at' => $this->diterima_at,
            'tahun_masuk' => $this->tahun_masuk,
            'alasan' => $this->alasan,
            'is_aktif' => true,
            'aktif_at' => now(),
            'is_nonaktif' => false,
            //            'nonaktif_at' => $this->nonaktif_at,
            'is_alumni' => false,
            //            'alumni_at' => $this->alumni_at,
        ]);

        $nomorIndukService = new NomorIndukService();
        $nomorIndukService->generateNomorIndukForSingleSantri($santri);

        session()->flash('message', 'Santri berhasil ditambahkan.');
        return redirect()->route('santri.index');
    }
}
