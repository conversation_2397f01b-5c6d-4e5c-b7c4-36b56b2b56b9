<?php

namespace App\Livewire\Stats;

use App\Models\Santri;
use Laravolt\Indonesia\Models\City;
use Laravolt\Indonesia\Models\Province;
use Livewire\Component;

class SantriPerKabupaten extends Component
{
    public $province_code;
    public $province;
    public $data;
    public function mount($province_code){

        $this->province_code = $province_code;

        $this->province = Province::where('code', $province_code)->first();
        abort_if($this->province == null, 404);


        $data = Santri::getTotalSantriPerCity($this->province_code);
        $this->data = $data;


    }
    public function render()
    {
        return view('livewire.stats.santri-per-kabupaten');
    }
}
