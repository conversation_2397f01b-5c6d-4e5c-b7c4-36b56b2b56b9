<?php

namespace App\Livewire\User;

use App\Models\ConfirmationLink;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Mary\Traits\Toast;

class ConfirmationMail extends Component
{
    use Toast;
    public $token;
    public $email;
    public $password;
    public $password_confirmation;

    public $user;

    #[Layout("layouts.guest")]

    public function mount($token){
        $this->token = $token;

        $user = ConfirmationLink::where("token", $token)->first();

        if (empty($user)) {
            abort(404);
        }

        $this->user = $user;

        $this->email = $user->user->email;

    }

    public function save()
    {
        // Validasi input
        $this->validate([
            'password' => [
                'required',
                'string',
                'min:8', // Minimum 8 karakter
                'confirmed', // Harus sama dengan password_confirmation
            ],
            'password_confirmation' => 'required',
        ], [
            'password.required' => 'Password wajib diisi.',
            'password.string' => 'Password harus berupa teks.',
            'password.min' => 'Password harus terdiri dari minimal 8 karakter.',
            'password.confirmed' => 'Password dan konfirmasi password tidak cocok.',
            'password_confirmation.required' => 'Konfirmasi password wajib diisi.',
        ]);

        // Update password user
        $this->user->user->update([
            'password' => Hash::make($this->password),
            'email_verified_at' => now(),
            'is_confirmed' => true,
        ]);

        // // Hapus token setelah konfirmasi berhasil
        $this->user->delete();

        // Redirect ke halaman login atau halaman lain

        $this->success('Password Berhasil diperbarui', 'Silakan login untuk melanjutkan', timeout: 3000, redirectTo: route('login'));

    }

    public function render()
    {
        return view('livewire.user.confirmation-mail');
    }
}
