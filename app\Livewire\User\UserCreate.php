<?php

namespace App\Livewire\User;

use App\Models\Komplek;
use App\Models\Role;
use Livewire\Component;

class UserCreate extends Component
{
    public $roles;
    public $kompleks;

    public function mount()
    {
        $this->initializeRoles();
        $this->initializeKompleks();
    }

    protected function initializeRoles()
    {
        $user = auth()->user();

        // Superadmin sees all roles; others don't see the superadmin role
        $this->roles = $user->hasRole('superadmin')
            ? Role::all()
            : Role::where('name', '!=', 'superadmin')->get();
    }

    protected function initializeKompleks()
    {
        $user = auth()->user();

        // Superadmin sees all kompleks; admin sees only their assigned kompleks
        if ($user->hasRole('superadmin')) {
            $this->kompleks = Komplek::all()->toArray();
        } elseif ($user->hasRole('admin')) {
            $assignedKomplek = $user->assignedKomplek();
            $this->kompleks = $assignedKomplek ? [$assignedKomplek->toArray()] : [];
        } else {
            $this->kompleks = [];
        }
    }

    public function render()
    {
        return view('livewire.user.user-create', [
            'roles' => $this->roles,
            'kompleks' => $this->kompleks,
        ]);
    }
}
