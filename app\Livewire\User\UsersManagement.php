<?php

namespace App\Livewire\User;

use App\Models\Role;
use App\Models\Komplek;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class UsersManagement extends Component
{
    use WithPagination;

    public $roles;
    public $kompleks;

    #[Url(history: true)]
    public $search = '';

    #[Url(history: true)]
    public $sortField = 'name';

    #[Url(history: true)]
    public $sortDirection = 'asc';

    #[Url(history: true)]
    public $perPage = 50;

    public function mount()
    {
        $this->initializeRoles();
        $this->initializeKompleks();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function getUsersQuery()
    {
        $currentUser = Auth::user();

        // Jika superadmin, ambil user dengan role admin atau superadmin
        if ($currentUser->hasRole('superadmin')) {
            $query = User::with(['roles', 'roleUsers.komplek'])
                ->whereHas('roles', function ($q) {
                    $q->whereIn('name', ['admin', 'superadmin']);
                });
        } elseif ($currentUser->hasRole('admin')) {
            // Jika admin, ambil user dengan komplek yang sama
            $idKomplek = $currentUser->roleUsers()->value('id_komplek');

            $query = User::with(['roles', 'roleUsers.komplek'])
                ->whereHas('roleUsers', function ($q) use ($idKomplek) {
                    $q->where('id_komplek', $idKomplek);
                });
        } else {
            // Untuk role lain, kembalikan query kosong
            $query = User::query()->whereRaw('1 = 0');
        }

        // Tambahkan pencarian berdasarkan nama, email, atau nama komplek
        return $query->when($this->search, function ($q) {
            $q->where(function ($subQuery) {
                $subQuery->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhereHas('roleUsers.komplek', function ($q) {
                        $q->where('nama_komplek', 'like', '%' . $this->search . '%');
                    });
            });
        });
    }

    public function render()
    {
        $users = $this->getUsersQuery()
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.user.users-management', [
            'roles' => $this->roles,
            'kompleks' => $this->kompleks,
            'users' => (object) $users,
        ]);
    }

    protected function initializeRoles()
    {
        $user = Auth::user();

        // Superadmin sees all roles; others don't see the superadmin role
        $this->roles = $user->hasRole('superadmin')
            ? Role::all()
            : Role::where('name', '!=', 'superadmin')->get();
    }

    protected function initializeKompleks()
    {
        $user = Auth::user();

        // Superadmin sees all kompleks; admin sees only their assigned kompleks
        if ($user->hasRole('superadmin')) {
            $this->kompleks = Komplek::all()->toArray();
        } elseif ($user->hasRole('admin')) {
            $assignedKomplek = $user->assignedKomplek();
            $this->kompleks = $assignedKomplek ? [$assignedKomplek->toArray()] : [];
        } else {
            $this->kompleks = [];
        }
    }
}
