<?php

namespace App\Livewire;

use App\Services\SantriService;
use Livewire\Component;

class WarningsComponent extends Component
{
    public $warnings = [];
    public $idKomplek;

    public function mount()
    {
        $santriService = new SantriService();

        $auth = auth()->user();

        $this->idKomplek = $auth->roleUsers()->value('id_komplek');

        if (isset($this->idKomplek)) {
            $santriTanpaNomorInduk = $santriService->getSantriWithoutNomorIndukBaru($this->idKomplek)->count();
        } else {
            $santriTanpaNomorInduk = $santriService->getSantriWithoutNomorIndukBaru()->count();
        }


        // Warning: Jumlah santri tanpa nomor induk
        
        if ($santriTanpaNomorInduk > 0) {
            $this->warnings[] = (object) [
                'title' => 'Jumlah Santri Tanpa Nomor Induk',
                'message' => "Ada {$santriTanpaNomorInduk} santri yang belum memiliki nomor induk.",
                'type' => 'warning',
                'link' => route('santri.santrinoninduk'),
            ];
        }
    }
    public function render()
    {
        return view('livewire.warnings-component');
    }

    public function placeholder()
    {
        return view('livewire.placeholders.dashboard-warnings-skeleton');
    }
}
