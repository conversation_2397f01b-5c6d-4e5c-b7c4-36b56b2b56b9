<?php

namespace App\Livewire\Widgets;

use App\Models\Komplek;
use Livewire\Component;

class SantriPerKomplek extends Component
{
    public $santriKompleks;
    public $showAll = false;

    public function mount($showAll = false){
        $this->showAll = $showAll;
        $this->santriKompleks = $this->loadData();
    }

    public function render()
    {
        return view('livewire.widgets.santri-per-komplek');
    }

    public function loadData(){

        if ($this->showAll) {
            return Komplek::withCount(['santris' => function ($query) {
                $query->where('is_aktif', true);
            }])
            ->orderBy('santris_count', 'desc')
            ->get();
        }

        return Komplek::withCount(['santris' => function ($query) {
            $query->where('is_aktif', true);
        }])
        ->orderBy('santris_count', 'desc')
        ->limit(5)
        ->get();
    }
}
