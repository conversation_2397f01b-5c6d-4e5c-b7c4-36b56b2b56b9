<?php

namespace App\Livewire\Widgets;

use App\Models\Santri;
use App\Services\SantriService;
use Livewire\Attributes\Url;
use Livewire\Component;

class SantriPerProvinsi extends Component
{
    #[Url(history: true)]
    public $showAll = false;
    public $santriPerProvinsi;
    public function mount($showAll = false){

        if ($this->showAll) {
            $this->santriPerProvinsi = Santri::getTotalSantriPerProvince(null, 'desc');
        } else {
            $this->santriPerProvinsi = Santri::getTotalSantriPerProvince(5, 'desc');
        }

    }
    public function render()
    {
        return view('livewire.widgets.santri-per-provinsi');
    }

}
