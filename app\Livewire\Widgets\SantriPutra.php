<?php

namespace App\Livewire\Widgets;

use App\Models\Santri;
use Livewire\Component;

class SantriPutra extends Component
{
    public $totalSantri;
    public $class;



    public function mount($class = '')
    {

        $user = auth()->user();
        if ($user->isAdmin) {
            $idKomplek = $user->roleUsers()->value('id_komplek');
            $this->totalSantri = Santri::where('jenis_kelamin','laki-laki')->where('is_aktif', 1)->where('id_komplek', $idKomplek)->count();
        } else {
            $this->totalSantri = Santri::where('jenis_kelamin','laki-laki')->where('is_aktif', 1)->count();
        }
        // Menghitung jumlah santri aktif

    }
    public function render()
    {
        return view('livewire.widgets.santri-putra');
    }
}
