<?php

namespace App\Livewire\Widgets;

use App\Models\Santri;
use Livewire\Component;
use Carbon\Carbon;

class StatistikTahunMasuk extends Component
{
    public $tahunMasuk = [];
    public $jumlahSantri = [];
    public array $myChart = [];

    // Batasan tahun, bisa diubah sesuai kebutuhan (4 tahun terakhir, 5 tahun terakhir, dsb)
    public $batasanTahun = 4;

    public function mount($batasanTahun = 4)
    {
        // Mengambil tahun terkini
        $tahunTerkini = Carbon::now()->year;

        // Menentukan batas tahun yang diambil (misalnya 4 tahun terakhir)
        $tahunBatas = $tahunTerkini - $this->batasanTahun + 1;

        // Ambil data santri hanya untuk tahun yang lebih besar dari tahun batas
        $data = Santri::selectRaw('tahun_masuk, COUNT(*) as jumlah')
            ->where('tahun_masuk', '>=', $tahunBatas) // Filter berdasarkan tahun
            ->groupBy('tahun_masuk')
            ->orderBy('tahun_masuk', 'asc')
            ->get();

        $tahunMasuk = $data->pluck('tahun_masuk')->toArray();
        $jumlahSantri = $data->pluck('jumlah')->toArray();

        // Menyiapkan data untuk chart
        $this->myChart = [
            'type' => 'bar',
            'data' => [
                'labels' => $tahunMasuk,
                'datasets' => [
                    [
                        'label' => 'Jumlah Santri',
                        'data' => $jumlahSantri,
                        'backgroundColor' => 'rgba(54, 162, 235, 0.6)',
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'borderWidth' => 1,
                    ]
                ]
            ]
        ];
    }

    public function render()
    {
        return view('livewire.widgets.statistik-tahun-masuk');
    }
}
