<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Kelas extends Model
{
    public function komplek()
    {
        return $this->belongsTo(Komplek::class, 'id_komplek');
    }

    public function santriKelas()
    {
        return $this->hasMany(SantriKelas::class, 'id_kelas');
    }

    public function ustadzs()
    {
        return $this->belongsToMany(User::class, 'kelas_ustadz', 'id_kelas', 'id_ustadz')->withTimestamps();
    }
}
