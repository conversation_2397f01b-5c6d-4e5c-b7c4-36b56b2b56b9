<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description'];

    public function users()
    {
        return $this->belongsToMany(User::class, 'role_users')
            ->using(RoleUser::class) // Model pivot kustom
            ->withPivot('id_komplek'); // Kolom tambahan di tabel pivot
    }
}
