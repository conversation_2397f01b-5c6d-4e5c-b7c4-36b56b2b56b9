<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Laravolt\Indonesia\Models\City;
use Laravolt\Indonesia\Models\District;
use Laravolt\Indonesia\Models\Province;

class Santri extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = ['nomor_induk', 'nomor_induk_baru', 'nama', 'tanggal_lahir', 'tempat_lahir', 'jenis_kelamin', 'foto', 'hp', 'email', 'alamat', 'city_code', 'district_code', 'nik', 'kk', 'foto_ktp', 'foto_kk', 'nama_ayah', 'nama_ibu', 'pekerjaan_ayah', 'peker<PERSON>an_ibu', 'hp_orang_tua', 'alamat_orang_tua', 'anak_ke', 'jumlah_saudara', 'pendidikan_terakhir', 'pendidikan_formal', 'pendidikan_non_formal', 'nomor_peserta', 'id_komplek', 'id_admin', 'id_kamar', 'diterima', 'diterima_at', 'tahun_masuk', 'alasan', 'is_aktif', 'aktif_at', 'is_nonaktif', 'nonaktif_at', 'is_alumni', 'alumni_at'];

    //    protected $casts = [
    //        'pendidikan_formal' => 'array',
    //        'pendidikan_non_formal' => 'array',
    //        'diterima' => 'boolean',
    //        'is_aktif' => 'boolean',
    //        'is_nonaktif' => 'boolean',
    //        'is_alumni' => 'boolean'
    //    ];

    protected function casts(): array
    {
        return [
            'pendidikan_formal' => 'array',
            'pendidikan_non_formal' => 'array',
            'diterima' => 'boolean',
            'is_aktif' => 'boolean',
            'is_nonaktif' => 'boolean',
            'is_alumni' => 'boolean',
        ];
    }

    protected $dates = ['deleted_at'];

    //    public function getPendidikanFormalAttribute($value)
    //    {
    //        return $value ? json_decode($value, true) : [];
    //    }
    //
    //    public function getPendidikanNonFormalAttribute($value)
    //    {
    //        return $value ? json_decode($value, true) : [];
    //    }

    // Relationship with Komplek
    public function komplek(): BelongsTo
    {
        return $this->belongsTo(Komplek::class, 'id_komplek');
    }

    // Relationship with User (Admin)
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_admin');
    }

    // Relationship with Kamar
    public function kamar(): BelongsTo
    {
        return $this->belongsTo(Kamar::class, 'id_kamar');
    }

    public function kabupaten(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_code', 'code');
    }

    public function kecamatan(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_code', 'code');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id_santri');
    }

    public static function getTotalSantriPerProvince($limit = null, $sortOrder = 'desc')
    {
        $query = self::select('indonesia_provinces.code as province_id', 'indonesia_provinces.name as province_name', DB::raw('COUNT(santris.id) as total_santri'))
            ->leftJoin('indonesia_cities', 'santris.city_code', '=', 'indonesia_cities.code') // Gabungkan dengan tabel cities
            ->leftJoin('indonesia_provinces', 'indonesia_cities.province_code', '=', 'indonesia_provinces.code') // Gabungkan dengan tabel provinces
            ->whereNotNull('santris.city_code') // Pastikan city_code tidak null
            ->groupBy('indonesia_provinces.code', 'indonesia_provinces.name'); // Kelompokkan berdasarkan provinsi

        // Tentukan pengurutan berdasarkan jumlah santri
        if (in_array(strtolower($sortOrder), ['asc', 'desc'])) {
            $query->orderBy('total_santri', $sortOrder); // Urutkan berdasarkan jumlah santri
        } else {
            $query->orderBy('indonesia_provinces.name'); // Urutkan berdasarkan nama provinsi secara default
        }

        // Tambahkan limit jika diberikan
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    public static function getTotalSantriPerCity($province_code, $limit = null, $sortOrder = 'desc')
    {
        $query = self::select('indonesia_cities.code as city_id', 'indonesia_cities.name as city_name', DB::raw('COUNT(santris.id) as total_santri'))
            ->leftJoin('indonesia_cities', 'santris.city_code', '=', 'indonesia_cities.code') // Gabungkan dengan tabel cities
            ->where('indonesia_cities.province_code', $province_code) // Filter berdasarkan kode provinsi
            ->whereNotNull('santris.city_code') // Pastikan city_code tidak null
            ->groupBy('indonesia_cities.code', 'indonesia_cities.name'); // Kelompokkan berdasarkan kota

        // Tentukan pengurutan berdasarkan jumlah santri
        if (in_array(strtolower($sortOrder), ['asc', 'desc'])) {
            $query->orderBy('total_santri', $sortOrder); // Urutkan berdasarkan jumlah santri
        } else {
            $query->orderBy('indonesia_cities.name'); // Urutkan berdasarkan nama kota secara default
        }

        // Tambahkan limit jika diberikan
        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }
}
