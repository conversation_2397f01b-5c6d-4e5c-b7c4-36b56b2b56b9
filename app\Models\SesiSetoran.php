<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SesiSetoran extends Model
{
    protected $fillable = ['id_komplek', 'nama_sesi', 'jam_mulai', 'jam_selesai', 'urutan'];

    public function komplek()
    {
        return $this->belongsTo(Komplek::class, 'id_komplek');
    }

    public function setorans()
    {
        return $this->hasMany(SetoranHafalan::class, 'id_sesi_setoran');
    }
}
