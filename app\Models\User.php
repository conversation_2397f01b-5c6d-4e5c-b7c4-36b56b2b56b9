<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use <PERSON><PERSON>\Jetstream\HasTeams;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use HasTeams;
    use Notifiable;
    use TwoFactorAuthenticatable;
    //    use HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'email_verified_at',
        'is_confirmed',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
    //
    //    public function komplek()
    //    {
    //        return $this->belongsTo(Komplek::class, 'komplek_id');
    //    }

    public function santri()
    {
        return $this->belongsTo(Santri::class, 'id_santri');
    }

    public function kelasDiampu()
    {
        return $this->belongsToMany(Kelas::class, 'kelas_ustadzs', 'id_ustadz', 'id_kelas')->withTimestamps();
    }


    /**
     * Many-to-Many relation between User and Role, with pivot table role_users, and get the id_komplek as pivot.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_users')->withPivot('id_komplek')->withTimestamps();
    }

    /**
     * Retrieve a single role associated with the user from the pivot table "role_users",
     * including the "id_komplek" pivot field, using the custom RoleUser pivot model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function role()
    {
        $role = $this->belongsToMany(Role::class, 'role_users')
            ->withPivot('id_komplek')
            ->using(RoleUser::class)
            ->limit(1); // Mengambil satu role saja

        return $role;
    }

    /**
     * Check if the user has a specific role by name.
     *
     * @param string $roleName The name of the role to check.
     * @return bool True if the user has the specified role, false otherwise.
     */
    public function hasRole($roleName)
    {
        return $this->roles->contains('name', $roleName);
    }

    // User.php

    /**
     * Define a one-to-many relationship with the RoleUser model.
     *
     * This function establishes a relationship where a user can have multiple
     * roles assigned, represented by instances of the RoleUser model. The
     * relationship is based on the foreign key 'user_id' in the RoleUser model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function roleUsers()
    {
        return $this->hasMany(RoleUser::class, 'user_id');
    }

    /**
     * Summary of assignedKomplek
     *
     * @return mixed
     */
    public function assignedKomplek()
    {
        // Ambil komplek berdasarkan role user
        $roleUser = $this->roleUsers()->first(); // Bisa menggunakan filter jika perlu
        return $roleUser ? $roleUser->komplek : null;
    }



    // Accessor untuk mengecek apakah user adalah superadmin
    public function getIsSuperAdminAttribute()
    {
        return $this->roles()->where('name', 'superadmin')->exists();
    }

    // Accessor untuk mengecek apakah user adalah admin
    public function getIsAdminAttribute()
    {
        return $this->roles()->where('name', 'admin')->exists();
    }

    // Accessor untuk mengecek apakah user adalah sekretaris
    public function getIsSekretarisAttribute()
    {
        return $this->roles()->where('name', 'sekretaris')->exists();
    }

    // Accessor untuk mengecek apakah user adalah bendahara
    public function getIsBendaharaAttribute()
    {
        return $this->roles()->where('name', 'bendahara')->exists();
    }

    // Accessor untuk mengecek apakah user adalah pendidikan
    public function getIsPendidikanAttribute()
    {
        return $this->roles()->where('name', 'pendidikan')->exists();
    }

    // Accessor untuk mengecek apakah user adalah keamanan
    public function getIsKeamananAttribute()
    {
        return $this->roles()->where('name', 'keamanan')->exists();
    }

    public function getIsUstadzAttribute()
    {
        return $this->roles()->where('name', 'ustadz')->exists();
    }

    // Accessor untuk mendapatkan id_komplek jika user adalah admin
    public function getIdKomplekAttribute()
    {
        if ($this->is_admin) {
            return $this->roles()->where('name', 'admin')->first()->pivot->id_komplek;
        }
        return null; // Jika bukan admin, kembalikan null
    }

    public function hasRoleName(string $roleName): bool
    {
        return $this->roles()->where('name', $roleName)->exists();
    }
}
