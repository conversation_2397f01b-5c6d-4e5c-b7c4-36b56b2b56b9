<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\WebsiteSettingCacheService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
        $this->app->singleton(WebsiteSettingCacheService::class, function ($app) {
            return new WebsiteSettingCacheService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
