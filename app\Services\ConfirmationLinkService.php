<?php

namespace App\Services;

use App\Mail\ConfirmationEmail;
use App\Models\ConfirmationLink;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ConfirmationLinkService
{
    public function createConfirmationLink($user){
        $token = Str::random(35);

        $link = ConfirmationLink::create([
            "user_id"=> $user->id,
            "token"=> $token,
            "expires_at"=> now()->addMonths(2),
        ]);

        $link->save();

        $this->sendConirmationLink($link);


    }

    public function sendConirmationLink(ConfirmationLink $confirmationLink){
        Mail::to(
            $confirmationLink->user->email,
        ) -> send(new ConfirmationEmail($confirmationLink)
        );
    }

}
