<?php

namespace App\Services;

use App\Models\Komplek;
use App\Models\Santri;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use UnexpectedValueException;
use RuntimeException;

class NomorIndukService
{
    /**
     * Generate nomor induk for a single santri.
     *
     * @param Santri $santri
     * @return string
     * @throws InvalidArgumentException
     * @throws UnexpectedValueException
     * @throws RuntimeException
     */
    public function generateNomorIndukForSingleSantri(Santri $santri)
    {
        try {
            // Validasi data santri
            $this->validateSantriData($santri);

            // Jika nomor induk sudah ada, kembalikan nomor induk tersebut
            if ($santri->nomor_induk_baru) {
                return $santri->nomor_induk_baru;
            }

            // Format data
            $tahunMasukFormatted = $this->formatTahunMasuk($santri->tahun_masuk);
            $statusKomplekFormatted = $this->formatStatusKomplek($santri->komplek->badan_status);
            $nomorUrutKomplek = $this->formatNomorUrutKomplek($santri->komplek->kode_komplek);
            $nomorUrutSantri = $this->getNomorUrutSantri($santri->tahun_masuk, $santri->id_komplek, $santri->id);
            $nomorKomputerisasi = $this->getNomorKomputerisasi($santri->tahun_masuk, $santri->id_komplek, $nomorUrutSantri);

            // Buat nomor induk
            $nomorInduk = sprintf(
                '%s%s%s%03d%s',
                $tahunMasukFormatted,
                $statusKomplekFormatted,
                $nomorUrutKomplek,
                $nomorUrutSantri,
                $nomorKomputerisasi
            );

            // Simpan nomor induk ke santri
            $santri->nomor_induk_baru = $nomorInduk;
            $santri->save();

            return $nomorInduk;

        } catch (InvalidArgumentException $e) {
            throw new InvalidArgumentException('Invalid data provided: ' . $e->getMessage(), $e->getCode(), $e);
        } catch (UnexpectedValueException $e) {
            throw new UnexpectedValueException('Data is incomplete: ' . $e->getMessage(), $e->getCode(), $e);
        } catch (RuntimeException $e) {
            throw new RuntimeException('Error during generation: ' . $e->getMessage(), $e->getCode(), $e);
        } catch (\Exception $e) {
            throw new RuntimeException('Unexpected error: ' . $e->getMessage(), 0, $e);
        }
    }


    /**
     * Validate santri data.
     *
     * @param Santri $santri
     * @throws UnexpectedValueException
     */
    private function validateSantriData(Santri $santri)
    {
        if (empty($santri->tahun_masuk)) {
            throw new UnexpectedValueException('Tahun masuk is required');
        }

        if (empty($santri->id_komplek)) {
            throw new UnexpectedValueException('ID komplek is required');
        }

        if (empty($santri->komplek)) {
            throw new UnexpectedValueException('Komplek data is required');
        }

        if (!isset($santri->komplek->badan_status) || $santri->komplek->badan_status === '') {
            throw new UnexpectedValueException('Badan status is required');
        }
    }

    /**
     * Format tahun masuk.
     *
     * @param string $tahunMasuk
     * @return string
     * @throws InvalidArgumentException
     */
    private function formatTahunMasuk($tahunMasuk)
    {
        if (!is_numeric($tahunMasuk) || strlen($tahunMasuk) != 4) {
            throw new InvalidArgumentException('Invalid tahun masuk format');
        }

        return substr($tahunMasuk, 2, 2);
    }

    /**
     * Format status komplek.
     *
     * @param int $badanStatus
     * @return string
     * @throws InvalidArgumentException
     */
    private function formatStatusKomplek($badanStatus)
    {
        if (!in_array($badanStatus, [0, 1])) {
            throw new InvalidArgumentException('Invalid badan status');
        }

        return $badanStatus ? '1' : '0';
    }

    /**
     * Format nomor urut komplek.
     *
     * @param int $idKomplek
     * @return string
     * @throws InvalidArgumentException
     */
    private function formatNomorUrutKomplek($idKomplek)
    {
        if (!is_numeric($idKomplek)) {
            throw new InvalidArgumentException('Invalid ID komplek format');
        }

        return str_pad($idKomplek, 2, '0', STR_PAD_LEFT);
    }

    /**
     * Get nomor urut santri.
     *
     * @param string $tahunMasuk
     * @param int $idKomplek
     * @return int
     */
    private function getNomorUrutSantri($tahunMasuk, $idKomplek, $santriId)
    {
        // Ambil semua santri dengan tahun masuk dan id komplek yang sama
        // Urutkan berdasarkan waktu pendaftaran atau ID untuk menentukan urutan
        $santris = DB::table('santris')
            ->where('tahun_masuk', $tahunMasuk)
            ->where('id_komplek', $idKomplek)
            ->orderBy('created_at', 'asc') // Gunakan 'id' jika lebih sesuai
            ->pluck('id') // Ambil ID santri sebagai urutan
            ->toArray(); // Konversi ke array

        // Temukan posisi santri yang sesuai dalam daftar
        $nomorUrut = array_search($santriId, $santris) + 1;

        if (!$nomorUrut) {
            throw new RuntimeException('Santri ID not found in the list.');
        }

        return $nomorUrut;
    }



    /**
     * Get nomor komputerisasi.
     *
     * @param string $tahunMasuk
     * @param int $idKomplek
     * @param int $nomorUrutSantri
     * @return string
     */
    private function getNomorKomputerisasi($tahunMasuk, $idKomplek, $nomorUrutSantri)
    {
        // Format bagian yang relevan dari nomor induk
        $prefix = sprintf(
            '%02d%s%s%03d',
            $this->formatTahunMasuk($tahunMasuk),
            $this->formatStatusKomplek($this->getKomplekBadanStatus($idKomplek)),
            $this->formatNomorUrutKomplek($idKomplek),
            $nomorUrutSantri
        );

        // Hitung jumlah nomor induk yang ada dengan prefix yang sama
        $existingNomorInduk = DB::table('santris')
            ->where('tahun_masuk', $tahunMasuk)
            ->where('id_komplek', $idKomplek)
            ->whereRaw("SUBSTRING(nomor_induk_baru, 1, 12) = ?", [$prefix])
            ->count();

        // Kembalikan nomor komputerisasi yang sesuai
        return str_pad($existingNomorInduk + 1, 2, '0', STR_PAD_LEFT);
    }


    /**
     * Mendapatkan badan status komplek berdasarkan ID komplek.
     *
     * @param int $idKomplek
     * @return int
     */
    private function getKomplekBadanStatus($idKomplek)
    {
        // Ambil badan status komplek dari database atau cache jika ada
        $komplek = Komplek::find($idKomplek);

        if (!$komplek) {
            throw new UnexpectedValueException('Komplek not found for ID ' . $idKomplek);
        }

        return $komplek->badan_status;
    }
}
