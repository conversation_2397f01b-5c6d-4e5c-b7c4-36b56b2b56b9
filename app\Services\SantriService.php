<?php

namespace App\Services;

use App\Models\Santri;
use Illuminate\Support\Facades\DB; // Importing the DB facade

class SantriService
{
    /**
     * Menghitung jumlah santri yang belum memiliki nomor_induk_baru berdasarkan id_komplek atau tahun_masuk.
     *
     * @param int|null $idKomplek Filter berdasarkan id_komplek (opsional).
     * @param int|null $tahunMasuk Filter berdasarkan tahun_masuk (opsional).
     * @return int Jumlah santri tanpa nomor_induk_baru.
     */
    public function countSantriWithoutNomorIndukBaru(?int $idKomplek = null, ?int $tahunMasuk = null): int
    {
        $query = Santri::whereNull('nomor_induk_baru');

        if ($idKomplek !== null) {
            $query->where('id_komplek', $idKomplek);
        }

        if ($tahunMasuk !== null) {
            $query->where('tahun_masuk', $tahunMasuk);
        }

        return $query->count();
    }

    public function getSantriWithoutNomorIndukBaru(?int $idKomplek = null, ?int $tahunMasuk = null)
    {
        $query = Santri::whereNull('nomor_induk_baru');

        if ($idKomplek !== null) {
            $query->where('id_komplek', $idKomplek);
        }

        if ($tahunMasuk !== null) {
            $query->where('tahun_masuk', $tahunMasuk);
        }

        return $query->get();
    }

    public function getTotalSantriPerProvince($limit_province = null)
    {
        return Santri::getTotalSantriPerProvince($limit_province);
    }
}
