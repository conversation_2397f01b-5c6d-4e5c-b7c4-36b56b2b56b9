<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use App\Models\WebsiteSetting;

class WebsiteSettingCacheService
{
    /**
     * Mengambil semua pengaturan dari cache atau database.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getSettings()
    {
        return Cache::remember('website_settings', 3600*1000*100, function () {
            return WebsiteSetting::all()->keyBy('name');
        });
    }

    /**
     * Menyimpan atau memperbarui pengaturan dan menyegarkan cache.
     *
     * @param string $name
     * @param string $value
     * @return void
     */
    public function saveSetting($name, $value)
    {
        WebsiteSetting::updateOrCreate(['name' => $name], ['value' => $value]);
        $this->refreshCache();
    }

    /**
     * Menyegarkan cache pengaturan.
     *
     * @return void
     */
    public function refreshCache()
    {
        Cache::forget('website_settings');
        $this->getSettings();
    }

    /**
     * Mengambil nilai pengaturan berdasarkan nama dari cache.
     *
     * @param string $name
     * @param mixed $default
     * @return mixed
     */
    public function getSettingValue($name, $default = null)
    {
        $settings = $this->getSettings();

        return $settings->get($name, (object) ['value' => $default])->value;
    }
}
