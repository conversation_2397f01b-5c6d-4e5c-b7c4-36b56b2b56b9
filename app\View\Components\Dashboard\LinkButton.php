<?php

namespace App\View\Components\Dashboard;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class LinkButton extends Component
{
    public $route;
    public $svgName;
    public $title;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($route, $svgName, $title)
    {
        $this->route = $route;
        $this->svgName = $svgName;
        $this->title = $title;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('components.dashboard.link-button');
    }
}
