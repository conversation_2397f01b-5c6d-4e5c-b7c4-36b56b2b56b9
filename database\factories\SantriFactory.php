<?php

namespace Database\Factories;

use App\Models\Santri;
use Illuminate\Database\Eloquent\Factories\Factory;

class SantriFactory extends Factory
{
    protected $model = Santri::class;

    public function definition()
    {
        return [
            'nama' => $this->faker->name,
            'tanggal_lahir' => $this->faker->date,
            'informasi_tambahan' => $this->faker->paragraph,
            'nomor_induk' => $this->faker->unique()->numerify('################'),
            'nomor_induk_baru' => $this->faker->unique()->numerify('################'),
            'tempat_lahir' => $this->faker->city,
            'jenis_kelamin' => $this->faker->randomElement(['laki-laki', 'perempuan']),
            'foto' => 'path/to/photo.jpg',
            'hp' => $this->faker->phoneNumber,
            'email' => $this->faker->unique()->safeEmail,
            'alamat' => $this->faker->address,
            'kabupaten' => $this->faker->city,
            'provinsi' => $this->faker->state,
            'nik' => $this->faker->unique()->numerify('################'),
            'kk' => $this->faker->unique()->numerify('################'),
            'foto_ktp' => 'path/to/ktp.jpg',
            'foto_kk' => 'path/to/kk.jpg',
            'nama_ayah' => $this->faker->name('male'),
            'nama_ibu' => $this->faker->name('female'),
            'pekerjaan_ayah' => $this->faker->jobTitle,
            'pekerjaan_ibu' => $this->faker->jobTitle,
            'hp_orang_tua' => $this->faker->phoneNumber,
            'alamat_orang_tua' => $this->faker->address,
            'anak_ke' => $this->faker->numberBetween(1, 5),
            'jumlah_saudara' => $this->faker->numberBetween(1, 10),
            'pendidikan_terakhir' => 'SMA',
            'pendidikan_formal' => json_encode([
                [
                    'pendidikan' => 'SD',
                    'instansi' => 'SD Negeri 1',
                    'tahun_lulus' => '2010'
                ],
                [
                    'pendidikan' => 'SMP',
                    'instansi' => 'SMP Negeri 1',
                    'tahun_lulus' => '2013'
                ],
                [
                    'pendidikan' => 'SMA',
                    'instansi' => 'SMA Negeri 1',
                    'tahun_lulus' => '2016'
                ]
            ]),
            'pendidikan_non_formal' => json_encode([
                [
                    'instansi' => 'Kursus Bahasa Inggris',
                    'tahun_lulus' => '2017'
                ]
            ]),
            'nomor_peserta' => $this->faker->numerify('#########'),
            'id_komplek' => 1,
            'id_admin' => 1,
            'id_kamar' => null,
            'diterima' => 1,
            'diterima_at' => now(),
            'tahun_masuk' => 2024,
            'alasan' => $this->faker->sentence,
            'is_aktif' => true,
            'aktif_at' => now(),
            'is_nonaktif' => false,
            'nonaktif_at' => null,
            'is_alumni' => false,
            'alumni_at' => null,
            'deleted_at' => null,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
