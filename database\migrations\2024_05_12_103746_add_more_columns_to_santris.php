<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            // Data Santri
            $table->string('nomor_induk', 16);
            $table->string('nomor_induk_baru', 16);
            $table->string('tempat_lahir');
            $table->string('jenis_kelamin');
            $table->text('foto');
            $table->string('hp');
            $table->string('email');
            $table->text('alamat');
            $table->text('kabupaten');
            $table->text('provinsi');
            $table->string('nik', 16);
            $table->string('kk', 16);
            $table->text('foto_ktp');
            $table->text('foto_kk');

            // Orang Tua
            $table->string('nama_ayah');
            $table->string('nama_ibu');
            $table->string('pekerjaan_ayah');
            $table->string('pekerjaan_ibu');
            $table->string('hp_orang_tua');
            $table->text('alamat_orang_tua');
            $table->integer('anak_ke');
            $table->integer('jumlah_saudara');

            // Pendidikan
            $table->text('pendidikan_terakhir');
            $table->json('pendidikan_formal');
            $table->json('pendidikan_non_formal');

            // Lainnya
            $table->string('nomor_peserta');
            $table->foreignId('id_komplek');
            $table->foreignId('id_admin');
            $table->foreignId('id_kamar');
            $table->boolean('diterima');
            $table->dateTime('diterima_at')->nullable();
            $table->year('tahun_masuk');
            $table->text('alasan');
            $table->boolean('is_aktif');
            $table->dateTime('aktif_at')->nullable();
            $table->boolean('is_nonaktif');
            $table->dateTime('nonaktif_at')->nullable();
            $table->boolean('is_alumni');
            $table->dateTime('alumni_at')->nullable();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            //
        });
    }
};
