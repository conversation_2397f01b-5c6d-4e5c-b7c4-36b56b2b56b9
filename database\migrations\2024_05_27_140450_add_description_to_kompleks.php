<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kompleks', function (Blueprint $table) {
            $table->text('description')->nullable();
            $table->text('alamat')->nullable();
            $table->text('logo')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kompleks', function (Blueprint $table) {
            //
        });
    }
};
