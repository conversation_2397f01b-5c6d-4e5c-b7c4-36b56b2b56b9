<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            $table->string('nomor_induk')->nullable()->change();
            $table->string('nomor_induk_baru')->nullable()->change();
            $table->string('tempat_lahir')->nullable()->change();
            $table->string('jenis_kelamin')->nullable()->change();
            $table->text('foto')->nullable()->change();
            $table->string('hp')->nullable()->change();
            $table->string('email')->nullable()->change();
            $table->text('alamat')->nullable()->change();
            $table->text('kabupaten')->nullable()->change();
            $table->text('provinsi')->nullable()->change();
            $table->string('nik')->nullable()->change();
            $table->string('kk')->nullable()->change();
            $table->text('foto_ktp')->nullable()->change();
            $table->text('foto_kk')->nullable()->change();
            $table->string('nama_ayah')->nullable()->change();
            $table->string('nama_ibu')->nullable()->change();
            $table->string('pekerjaan_ayah')->nullable()->change();
            $table->string('pekerjaan_ibu')->nullable()->change();
            $table->string('hp_orang_tua')->nullable()->change();
            $table->text('alamat_orang_tua')->nullable()->change();
            $table->integer('anak_ke')->nullable()->change();
            $table->integer('jumlah_saudara')->nullable()->change();

            $table->text('pendidikan_terakhir')->nullable()->change();
            $table->json('pendidikan_formal')->nullable()->change();
            $table->json('pendidikan_non_formal')->nullable()->change();

            $table->string('nomor_peserta')->nullable()->change();
            $table->foreignId('id_komplek')->nullable()->change();
            $table->foreignId('id_kamar')->nullable()->change();
            $table->boolean('diterima')->nullable()->change();
            $table->dateTime('diterima_at')->nullable()->change();
            $table->year('tahun_masuk')->nullable()->change();
            $table->text('alasan')->nullable()->change();




        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
