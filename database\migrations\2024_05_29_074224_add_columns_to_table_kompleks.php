<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kompleks', function (Blueprint $table) {
            $table->string('hp')->nullable();
            $table->boolean('badan_status')->nullable();
            $table->string('kode_komplek')->nullable();
            $table->text('slug')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kompleks', function (Blueprint $table) {
            //
        });
    }
};
