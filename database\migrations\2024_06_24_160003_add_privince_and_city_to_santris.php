<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            $table->char('province_code', 2)->nullable();
            $table->foreign('province_code')->references('code')->on('indonesia_provinces')->onDelete('cascade');
            $table->char('city_code', 4)->nullable();
            $table->foreign('city_code')->references('code')->on('indonesia_cities')->onDelete('cascade');

            $table->dropColumn('provinsi');
            $table->dropColumn('kabupaten');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            $table->dropForeign(['city_code']);
        });
    }
};
