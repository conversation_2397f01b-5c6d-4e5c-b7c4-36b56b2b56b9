<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            // Hapus foreign key terlebih dahulu
            $table->dropForeign(['district_code']);
        });

        Schema::table('santris', function (Blueprint $table) {
            // Ubah tipe kolom
            $table->char('district_code', 7)->nullable()->change();
        });

        Schema::table('santris', function (Blueprint $table) {
            // Tambahkan kembali foreign key constraint
            $table->foreign('district_code')
                ->references('code')
                ->on('indonesia_districts')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
