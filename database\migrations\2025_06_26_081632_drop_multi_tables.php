<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('absensis');
        Schema::dropIfExists('pendidikan_kompleks');
        Schema::dropIfExists('kelas_ustadz');
        Schema::dropIfExists('kelas_santri');
        Schema::dropIfExists('kelas');
        Schema::dropIfExists('ustadzs');

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
