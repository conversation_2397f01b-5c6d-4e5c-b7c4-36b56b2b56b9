<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pengaturan_tahfidz_kompleks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_komplek')->constrained('kompleks')->cascadeOnDelete();
            $table->enum('mode', ['halaman', 'ayat'])->default('halaman');
            $table->date('berlaku_mulai');
            $table->boolean('aktif')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pengaturan_tahfidz_kompleks');
    }
};
