<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('setoran_hafalans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_santri')->constrained('santris')->cascadeOnDelete();
            $table->foreignId('id_kelas')->constrained('kelas')->cascadeOnDelete();

            $table->date('tanggal');
            $table->unsignedTinyInteger('sesi')->default(1); // contoh: pagi, siang, malam

            // Mode halaman (pojok)
            $table->unsignedTinyInteger('juz')->nullable();
            $table->unsignedTinyInteger('halaman_dalam_juz')->nullable();
            $table->unsignedTinyInteger('jumlah_halaman')->nullable();
            $table->unsignedSmallInteger('halaman_awal')->nullable();

            // Mode ayat
            $table->unsignedSmallInteger('surat_id')->nullable();
            $table->unsignedSmallInteger('ayat_awal')->nullable();
            $table->unsignedSmallInteger('ayat_akhir')->nullable();

            $table->enum('status', ['setor', 'murojaah', 'ulang'])->default('setor');
            $table->text('catatan')->nullable();

            $table->foreignId('dibuat_oleh')->constrained('users')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('setoran_hafalans');
    }
};
