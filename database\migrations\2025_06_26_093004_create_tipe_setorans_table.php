<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTipeSetoransTable extends Migration
{
    public function up(): void
    {
        Schema::create('tipe_setorans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_komplek')->constrained('kompleks');
            $table->string('nama_tipe'); // <PERSON><PERSON>, <PERSON><PERSON>, Halaqah
            $table->unsignedTinyInteger('urutan')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tipe_setorans');
    }
}
