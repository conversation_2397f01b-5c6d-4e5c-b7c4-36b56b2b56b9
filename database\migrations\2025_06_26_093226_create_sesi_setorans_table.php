<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSesiSetoransTable extends Migration
{
    public function up(): void
    {
        Schema::create('sesi_setorans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('id_komplek')->constrained('kompleks');
            $table->string('nama_sesi'); // Subuh, Pagi, Malam
            $table->time('jam_mulai')->nullable();
            $table->time('jam_selesai')->nullable();
            $table->unsignedTinyInteger('urutan')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sesi_setorans');
    }
}
