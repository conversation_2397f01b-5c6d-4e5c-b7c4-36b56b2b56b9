<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('setoran_hafalans', function (Blueprint $table) {
            Schema::table('setoran_hafalans', function (Blueprint $table) {
                $table->foreignId('id_tipe_setoran')->after('id_kelas')->constrained('tipe_setorans');
                $table->foreignId('id_sesi_setoran')->after('tanggal')->constrained('sesi_setorans');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('setoran_hafalans', function (Blueprint $table) {
            //
        });
    }
};
