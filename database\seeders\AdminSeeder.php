<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Hash;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat pengguna baru
        $user = User::create([
            'name' => 'Al Munawwir',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin12345'), // Hash password
        ]);

        // <PERSON>i role admin, jika ada, tambahkan role ini ke user
        $adminRole = Role::where('name', 'superadmin')->first();

        if ($adminRole) {
            $user->roles()->attach($adminRole->id);
        }

    }
}
