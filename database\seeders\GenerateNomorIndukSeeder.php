<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Santri;
use App\Services\NomorIndukService;
use Illuminate\Support\Facades\Log;

class GenerateNomorIndukSeeder extends Seeder
{
    public function run()
    {
        $batchSize = 50; // Jumlah santri per eksekusi
        $nomorIndukService = new NomorIndukService();

        // Hitung jumlah santri yang sudah memiliki nomor induk sebelum proses
        $totalSantriWithNomorIndukBefore = Santri::whereNotNull('nomor_induk_baru')->count();
        $this->command->info("Total Santri with nomor induk before: $totalSantriWithNomorIndukBefore");

        // Ambil santri yang belum memiliki nomor induk
        $santris = Santri::whereNull('nomor_induk_baru')
            ->limit($batchSize)
            ->get();

        $totalSantris = $santris->count();
        $successCount = 0;
        $failureCount = 0;

        foreach ($santris as $santri) {
            try {
                // Generate nomor induk untuk santri
                $nomorIndukService->generateNomorIndukForSingleSantri($santri);
                $successCount++;
            } catch (\Exception $e) {
                // Tangani kesalahan jika ada
                Log::error('Error generating nomor induk for Santri ID ' . $santri->id . ': ' . $e->getMessage());
                $failureCount++;
            }
        }

        // Hitung jumlah santri yang sudah memiliki nomor induk setelah proses
        $totalSantriWithNomorIndukAfter = Santri::whereNotNull('nomor_induk_baru')->count();
        $this->command->info("Total Santri with nomor induk after: $totalSantriWithNomorIndukAfter");

        // Tampilkan hasil ke console
        $this->command->info("Total Santri processed: $totalSantris");
        $this->command->info("Successfully generated nomor induk: $successCount");
        $this->command->info("Failed to generate nomor induk: $failureCount");
    }
}
