<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class KomplekSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Path ke file JSON
        $jsonPath = public_path('json/almunaww_siap2_table_kompleks_asrama.json');

        // Membaca dan decode file JSON
        $json = File::get($jsonPath);
        $data = json_decode($json, true)['data'];

        foreach ($data as $item) {
            DB::table('kompleks')->insert([
                'nama_komplek' => $item['nama_kompleks_asrama'],
                'description' => $item['ket_kompleks_asrama'],
                'nama_pengasuh' => $item['pengasuh'],
                'komplek_type' => $this->mapKomplekType($item['papi']),
                'alamat' => $item['alamat'],
                'logo' => 'logos/' . $item['logokompleks'],
                'kode_komplek' => $item['kodekompleks'],
                'hp' => $item['telp'],
                'badan_status' => $item['badan_status'],
                'is_deleted' => false,
                'deleted_at' => null,
                'created_at' => $item['created'],
                'updated_at' => $item['updated'],
            ]);
        }
    }

    /**
     * Map komplek type.
     *
     * @param string $papi
     * @return string
     */
    private function mapKomplekType($papi)
    {
        switch ($papi) {
            case 'pa':
                return 'putra';
            case 'pi':
                return 'putri';
            case 'papi':
                return 'putra_putri';
            default:
                return 'unknown';
        }
    }
}
