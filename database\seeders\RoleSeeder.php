<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        $roles = [
            ['name' => 'superadmin', 'description' => 'Superadmin with access to all komplek data'],
            ['name' => 'admin', 'description' => 'Admin of a specific komplek'],
            ['name' => 'sekretaris', 'description' => 'Secretary role'],
            ['name' => 'bendahara', 'description' => 'Treasurer role'],
            ['name' => 'pendidikan', 'description' => 'Education department role'],
            ['name' => 'keamanan', 'description' => 'Security role'],
            ['name' => 'ustadz', 'description' => 'Ustadz pengampu kelas'],
        ];

        foreach ($roles as $role) {
            Role::updateOrCreate(
                ['name' => $role['name']],
                ['description' => $role['description']]
            );
        }
    }
}
