<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Laravolt\Indonesia\Models\City;

class SantriSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Path ke file JSON
        $jsonPath = public_path('json/santri.json');

        // Membaca dan decode file JSON
        $json = File::get($jsonPath);
        $data = json_decode($json, true)['data'];

        $santris = [];
        foreach ($data as $item) {
            // Validasi data
            if (
                !is_numeric($item['id_kompleks_asrama']) ||
                !$this->isValidDate($item['tanggallahir']) ||
                !$this->isValidDate($item['tanggal_aktif'])
            ) {
                continue;
            }

            // Mencari city_code
            $cityName = $this->extractCityName($item['lahir']);
            $city = City::where('name', 'like', "%$cityName%")->first();

            // Menentukan diterima_at dan aktif_at
            $tanggal_aktif = Carbon::createFromFormat('Y-m-d', $item['tanggal_aktif'])->toDateString();
            $diterima_at = "$tanggal_aktif 00:00:00";
            $aktif_at = "$tanggal_aktif 00:00:00";

            // Menentukan tahun_masuk
            $tahun_masuk = (int) $item['tahunmondok'];
            if (!$this->isValidYear($tahun_masuk)) {
                $tahun_masuk = Carbon::createFromFormat('Y-m-d', $item['tanggal_aktif'])->year;
            }

            // Convert jenis_kelamin to lowercase
            $jenis_kelamin = strtolower($item['kelamin']);

            // Validate email format
            $email = filter_var($item['email'], FILTER_VALIDATE_EMAIL) ? $item['email'] : null;

            $santri = [
                'nama' => $item['nama'],
                'tanggal_lahir' => $item['tanggallahir'],
                'informasi_tambahan' => 'tidak ada',
                'nomor_induk' => $item['nomor_induk'],
                'nomor_induk_baru' => null,
                'tempat_lahir' => $item['lahir'],
                'jenis_kelamin' => $jenis_kelamin,
                'foto' => 'photos/' . $item['foto'],
                'hp' => $item['hp'],
                'email' => $email,
                'alamat' => $item['alamat'],
                'city_code' => $city ? $city->code : null,
                'nik' => $item['nik'],
                'kk' => $item['kk'],
                'foto_ktp' => null,
                'foto_kk' => null,
                'nama_ayah' => $item['ayah'],
                'nama_ibu' => $item['ibu'],
                'pekerjaan_ayah' => $item['pekerjaan_ayah'],
                'pekerjaan_ibu' => $item['pekerjaan_ibu'],
                'hp_orang_tua' => $item['hportu'],
                'alamat_orang_tua' => $item['alamat_ortu'],
                'anak_ke' => $item['anak_ke'],
                'jumlah_saudara' => $item['jmlsaudara'],
                'pendidikan_terakhir' => '',
                'pendidikan_formal' => null,
                'pendidikan_non_formal' => null,
                'nomor_peserta' => null,
                'id_komplek' => $item['id_kompleks_asrama'],
                'id_admin' => 1,
                'id_kamar' => null,
                'diterima' => 1,
                'diterima_at' => $diterima_at,
                'tahun_masuk' => $tahun_masuk,
                'alasan' => $item['alasan'],
                'is_aktif' => $item['status'] == 1 ? 1 : 0,
                'aktif_at' => $aktif_at,
                'is_nonaktif' => $item['status'] == 0 ? 1 : 0,
                'is_alumni' => $item['status'] == 2 ? 1 : 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            // Debugging: output the santri array
            Log::info('Santri data:', $santri);

            $santris[] = $santri;

            // If we have accumulated 500 records, insert them in a batch
            if (count($santris) >= 500) {
                DB::table('santris')->insert($santris);
                $santris = []; // Reset the array for the next batch
            }
        }

        // Insert any remaining records
        if (!empty($santris)) {
            DB::table('santris')->insert($santris);
        }
    }

    /**
     * Validasi format tanggal
     *
     * @param string $date
     * @return bool
     */
    private function isValidDate($date)
    {
        // Log tambahan untuk melihat input tanggal
        Log::info('Validating date:', ['date' => $date]);
        if ($date === '0000-00-00') {
            return false;
        }
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return false;
        }
        $year = date('Y', $timestamp);
        $month = date('m', $timestamp);
        $day = date('d', $timestamp);
        return checkdate($month, $day, $year);
    }

    /**
     * Validasi format tahun
     *
     * @param string $year
     * @return bool
     */
    private function isValidYear($year)
    {
        return is_numeric($year) && (int) $year > 0;
    }

    /**
     * Ekstrak nama kota dari nama lengkap tempat lahir
     *
     * @param string $fullName
     * @return string
     */
    private function extractCityName($fullName)
    {
        $keywords = ['Kabupaten', 'Kota'];
        foreach ($keywords as $keyword) {
            if (stripos($fullName, $keyword) !== false) {
                return trim(str_ireplace($keyword, '', $fullName));
            }
        }
        return $fullName;
    }
}
