var kl=Object.defineProperty;var Cl=(n,e,t)=>e in n?kl(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var E=(n,e,t)=>Cl(n,typeof e!="symbol"?e+"":e,t);function Lr(n,e){return function(){return n.apply(e,arguments)}}const{toString:Ol}=Object.prototype,{getPrototypeOf:xs}=Object,ui=(n=>e=>{const t=Ol.call(e);return n[t]||(n[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),Nt=n=>(n=n.toLowerCase(),e=>ui(e)===n),di=n=>e=>typeof e===n,{isArray:Ne}=Array,on=di("undefined");function Pl(n){return n!==null&&!on(n)&&n.constructor!==null&&!on(n.constructor)&&Pt(n.constructor.isBuffer)&&n.constructor.isBuffer(n)}const Fr=Nt("ArrayBuffer");function Al(n){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(n):e=n&&n.buffer&&Fr(n.buffer),e}const El=di("string"),Pt=di("function"),Ir=di("number"),fi=n=>n!==null&&typeof n=="object",Tl=n=>n===!0||n===!1,Wn=n=>{if(ui(n)!=="object")return!1;const e=xs(n);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in n)&&!(Symbol.iterator in n)},Rl=Nt("Date"),Ll=Nt("File"),Fl=Nt("Blob"),Il=Nt("FileList"),Nl=n=>fi(n)&&Pt(n.pipe),Bl=n=>{let e;return n&&(typeof FormData=="function"&&n instanceof FormData||Pt(n.append)&&((e=ui(n))==="formdata"||e==="object"&&Pt(n.toString)&&n.toString()==="[object FormData]"))},zl=Nt("URLSearchParams"),[Hl,jl,Wl,Vl]=["ReadableStream","Request","Response","Headers"].map(Nt),Yl=n=>n.trim?n.trim():n.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function pn(n,e,{allOwnKeys:t=!1}={}){if(n===null||typeof n>"u")return;let i,s;if(typeof n!="object"&&(n=[n]),Ne(n))for(i=0,s=n.length;i<s;i++)e.call(null,n[i],i,n);else{const o=t?Object.getOwnPropertyNames(n):Object.keys(n),r=o.length;let a;for(i=0;i<r;i++)a=o[i],e.call(null,n[a],a,n)}}function Nr(n,e){e=e.toLowerCase();const t=Object.keys(n);let i=t.length,s;for(;i-- >0;)if(s=t[i],e===s.toLowerCase())return s;return null}const ye=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Br=n=>!on(n)&&n!==ye;function Ki(){const{caseless:n}=Br(this)&&this||{},e={},t=(i,s)=>{const o=n&&Nr(e,s)||s;Wn(e[o])&&Wn(i)?e[o]=Ki(e[o],i):Wn(i)?e[o]=Ki({},i):Ne(i)?e[o]=i.slice():e[o]=i};for(let i=0,s=arguments.length;i<s;i++)arguments[i]&&pn(arguments[i],t);return e}const Ul=(n,e,t,{allOwnKeys:i}={})=>(pn(e,(s,o)=>{t&&Pt(s)?n[o]=Lr(s,t):n[o]=s},{allOwnKeys:i}),n),$l=n=>(n.charCodeAt(0)===65279&&(n=n.slice(1)),n),ql=(n,e,t,i)=>{n.prototype=Object.create(e.prototype,i),n.prototype.constructor=n,Object.defineProperty(n,"super",{value:e.prototype}),t&&Object.assign(n.prototype,t)},Xl=(n,e,t,i)=>{let s,o,r;const a={};if(e=e||{},n==null)return e;do{for(s=Object.getOwnPropertyNames(n),o=s.length;o-- >0;)r=s[o],(!i||i(r,n,e))&&!a[r]&&(e[r]=n[r],a[r]=!0);n=t!==!1&&xs(n)}while(n&&(!t||t(n,e))&&n!==Object.prototype);return e},Kl=(n,e,t)=>{n=String(n),(t===void 0||t>n.length)&&(t=n.length),t-=e.length;const i=n.indexOf(e,t);return i!==-1&&i===t},Jl=n=>{if(!n)return null;if(Ne(n))return n;let e=n.length;if(!Ir(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=n[e];return t},Gl=(n=>e=>n&&e instanceof n)(typeof Uint8Array<"u"&&xs(Uint8Array)),Zl=(n,e)=>{const i=(n&&n[Symbol.iterator]).call(n);let s;for(;(s=i.next())&&!s.done;){const o=s.value;e.call(n,o[0],o[1])}},Ql=(n,e)=>{let t;const i=[];for(;(t=n.exec(e))!==null;)i.push(t);return i},tc=Nt("HTMLFormElement"),ec=n=>n.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,i,s){return i.toUpperCase()+s}),Xs=(({hasOwnProperty:n})=>(e,t)=>n.call(e,t))(Object.prototype),nc=Nt("RegExp"),zr=(n,e)=>{const t=Object.getOwnPropertyDescriptors(n),i={};pn(t,(s,o)=>{let r;(r=e(s,o,n))!==!1&&(i[o]=r||s)}),Object.defineProperties(n,i)},ic=n=>{zr(n,(e,t)=>{if(Pt(n)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const i=n[t];if(Pt(i)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},sc=(n,e)=>{const t={},i=s=>{s.forEach(o=>{t[o]=!0})};return Ne(n)?i(n):i(String(n).split(e)),t},oc=()=>{},rc=(n,e)=>n!=null&&Number.isFinite(n=+n)?n:e,Ci="abcdefghijklmnopqrstuvwxyz",Ks="0123456789",Hr={DIGIT:Ks,ALPHA:Ci,ALPHA_DIGIT:Ci+Ci.toUpperCase()+Ks},ac=(n=16,e=Hr.ALPHA_DIGIT)=>{let t="";const{length:i}=e;for(;n--;)t+=e[Math.random()*i|0];return t};function lc(n){return!!(n&&Pt(n.append)&&n[Symbol.toStringTag]==="FormData"&&n[Symbol.iterator])}const cc=n=>{const e=new Array(10),t=(i,s)=>{if(fi(i)){if(e.indexOf(i)>=0)return;if(!("toJSON"in i)){e[s]=i;const o=Ne(i)?[]:{};return pn(i,(r,a)=>{const l=t(r,s+1);!on(l)&&(o[a]=l)}),e[s]=void 0,o}}return i};return t(n,0)},hc=Nt("AsyncFunction"),uc=n=>n&&(fi(n)||Pt(n))&&Pt(n.then)&&Pt(n.catch),jr=((n,e)=>n?setImmediate:e?((t,i)=>(ye.addEventListener("message",({source:s,data:o})=>{s===ye&&o===t&&i.length&&i.shift()()},!1),s=>{i.push(s),ye.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",Pt(ye.postMessage)),dc=typeof queueMicrotask<"u"?queueMicrotask.bind(ye):typeof process<"u"&&process.nextTick||jr,w={isArray:Ne,isArrayBuffer:Fr,isBuffer:Pl,isFormData:Bl,isArrayBufferView:Al,isString:El,isNumber:Ir,isBoolean:Tl,isObject:fi,isPlainObject:Wn,isReadableStream:Hl,isRequest:jl,isResponse:Wl,isHeaders:Vl,isUndefined:on,isDate:Rl,isFile:Ll,isBlob:Fl,isRegExp:nc,isFunction:Pt,isStream:Nl,isURLSearchParams:zl,isTypedArray:Gl,isFileList:Il,forEach:pn,merge:Ki,extend:Ul,trim:Yl,stripBOM:$l,inherits:ql,toFlatObject:Xl,kindOf:ui,kindOfTest:Nt,endsWith:Kl,toArray:Jl,forEachEntry:Zl,matchAll:Ql,isHTMLForm:tc,hasOwnProperty:Xs,hasOwnProp:Xs,reduceDescriptors:zr,freezeMethods:ic,toObjectSet:sc,toCamelCase:ec,noop:oc,toFiniteNumber:rc,findKey:Nr,global:ye,isContextDefined:Br,ALPHABET:Hr,generateString:ac,isSpecCompliantForm:lc,toJSONObject:cc,isAsyncFn:hc,isThenable:uc,setImmediate:jr,asap:dc};function B(n,e,t,i,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=n,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),i&&(this.request=i),s&&(this.response=s,this.status=s.status?s.status:null)}w.inherits(B,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:w.toJSONObject(this.config),code:this.code,status:this.status}}});const Wr=B.prototype,Vr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(n=>{Vr[n]={value:n}});Object.defineProperties(B,Vr);Object.defineProperty(Wr,"isAxiosError",{value:!0});B.from=(n,e,t,i,s,o)=>{const r=Object.create(Wr);return w.toFlatObject(n,r,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),B.call(r,n.message,e,t,i,s),r.cause=n,r.name=n.name,o&&Object.assign(r,o),r};const fc=null;function Ji(n){return w.isPlainObject(n)||w.isArray(n)}function Yr(n){return w.endsWith(n,"[]")?n.slice(0,-2):n}function Js(n,e,t){return n?n.concat(e).map(function(s,o){return s=Yr(s),!t&&o?"["+s+"]":s}).join(t?".":""):e}function gc(n){return w.isArray(n)&&!n.some(Ji)}const pc=w.toFlatObject(w,{},null,function(e){return/^is[A-Z]/.test(e)});function gi(n,e,t){if(!w.isObject(n))throw new TypeError("target must be an object");e=e||new FormData,t=w.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,x){return!w.isUndefined(x[m])});const i=t.metaTokens,s=t.visitor||h,o=t.dots,r=t.indexes,l=(t.Blob||typeof Blob<"u"&&Blob)&&w.isSpecCompliantForm(e);if(!w.isFunction(s))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(w.isDate(p))return p.toISOString();if(!l&&w.isBlob(p))throw new B("Blob is not supported. Use a Buffer instead.");return w.isArrayBuffer(p)||w.isTypedArray(p)?l&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function h(p,m,x){let y=p;if(p&&!x&&typeof p=="object"){if(w.endsWith(m,"{}"))m=i?m:m.slice(0,-2),p=JSON.stringify(p);else if(w.isArray(p)&&gc(p)||(w.isFileList(p)||w.endsWith(m,"[]"))&&(y=w.toArray(p)))return m=Yr(m),y.forEach(function(S,k){!(w.isUndefined(S)||S===null)&&e.append(r===!0?Js([m],k,o):r===null?m:m+"[]",c(S))}),!1}return Ji(p)?!0:(e.append(Js(x,m,o),c(p)),!1)}const u=[],d=Object.assign(pc,{defaultVisitor:h,convertValue:c,isVisitable:Ji});function f(p,m){if(!w.isUndefined(p)){if(u.indexOf(p)!==-1)throw Error("Circular reference detected in "+m.join("."));u.push(p),w.forEach(p,function(y,v){(!(w.isUndefined(y)||y===null)&&s.call(e,y,w.isString(v)?v.trim():v,m,d))===!0&&f(y,m?m.concat(v):[v])}),u.pop()}}if(!w.isObject(n))throw new TypeError("data must be an object");return f(n),e}function Gs(n){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(n).replace(/[!'()~]|%20|%00/g,function(i){return e[i]})}function ys(n,e){this._pairs=[],n&&gi(n,this,e)}const Ur=ys.prototype;Ur.append=function(e,t){this._pairs.push([e,t])};Ur.toString=function(e){const t=e?function(i){return e.call(this,i,Gs)}:Gs;return this._pairs.map(function(s){return t(s[0])+"="+t(s[1])},"").join("&")};function mc(n){return encodeURIComponent(n).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $r(n,e,t){if(!e)return n;const i=t&&t.encode||mc;w.isFunction(t)&&(t={serialize:t});const s=t&&t.serialize;let o;if(s?o=s(e,t):o=w.isURLSearchParams(e)?e.toString():new ys(e,t).toString(i),o){const r=n.indexOf("#");r!==-1&&(n=n.slice(0,r)),n+=(n.indexOf("?")===-1?"?":"&")+o}return n}class Zs{constructor(){this.handlers=[]}use(e,t,i){return this.handlers.push({fulfilled:e,rejected:t,synchronous:i?i.synchronous:!1,runWhen:i?i.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){w.forEach(this.handlers,function(i){i!==null&&e(i)})}}const qr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},bc=typeof URLSearchParams<"u"?URLSearchParams:ys,xc=typeof FormData<"u"?FormData:null,yc=typeof Blob<"u"?Blob:null,_c={isBrowser:!0,classes:{URLSearchParams:bc,FormData:xc,Blob:yc},protocols:["http","https","file","blob","url","data"]},_s=typeof window<"u"&&typeof document<"u",Gi=typeof navigator=="object"&&navigator||void 0,vc=_s&&(!Gi||["ReactNative","NativeScript","NS"].indexOf(Gi.product)<0),wc=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Mc=_s&&window.location.href||"http://localhost",Dc=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:_s,hasStandardBrowserEnv:vc,hasStandardBrowserWebWorkerEnv:wc,navigator:Gi,origin:Mc},Symbol.toStringTag,{value:"Module"})),gt={...Dc,..._c};function Sc(n,e){return gi(n,new gt.classes.URLSearchParams,Object.assign({visitor:function(t,i,s,o){return gt.isNode&&w.isBuffer(t)?(this.append(i,t.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},e))}function kc(n){return w.matchAll(/\w+|\[(\w*)]/g,n).map(e=>e[0]==="[]"?"":e[1]||e[0])}function Cc(n){const e={},t=Object.keys(n);let i;const s=t.length;let o;for(i=0;i<s;i++)o=t[i],e[o]=n[o];return e}function Xr(n){function e(t,i,s,o){let r=t[o++];if(r==="__proto__")return!0;const a=Number.isFinite(+r),l=o>=t.length;return r=!r&&w.isArray(s)?s.length:r,l?(w.hasOwnProp(s,r)?s[r]=[s[r],i]:s[r]=i,!a):((!s[r]||!w.isObject(s[r]))&&(s[r]=[]),e(t,i,s[r],o)&&w.isArray(s[r])&&(s[r]=Cc(s[r])),!a)}if(w.isFormData(n)&&w.isFunction(n.entries)){const t={};return w.forEachEntry(n,(i,s)=>{e(kc(i),s,t,0)}),t}return null}function Oc(n,e,t){if(w.isString(n))try{return(e||JSON.parse)(n),w.trim(n)}catch(i){if(i.name!=="SyntaxError")throw i}return(0,JSON.stringify)(n)}const mn={transitional:qr,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const i=t.getContentType()||"",s=i.indexOf("application/json")>-1,o=w.isObject(e);if(o&&w.isHTMLForm(e)&&(e=new FormData(e)),w.isFormData(e))return s?JSON.stringify(Xr(e)):e;if(w.isArrayBuffer(e)||w.isBuffer(e)||w.isStream(e)||w.isFile(e)||w.isBlob(e)||w.isReadableStream(e))return e;if(w.isArrayBufferView(e))return e.buffer;if(w.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(i.indexOf("application/x-www-form-urlencoded")>-1)return Sc(e,this.formSerializer).toString();if((a=w.isFileList(e))||i.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return gi(a?{"files[]":e}:e,l&&new l,this.formSerializer)}}return o||s?(t.setContentType("application/json",!1),Oc(e)):e}],transformResponse:[function(e){const t=this.transitional||mn.transitional,i=t&&t.forcedJSONParsing,s=this.responseType==="json";if(w.isResponse(e)||w.isReadableStream(e))return e;if(e&&w.isString(e)&&(i&&!this.responseType||s)){const r=!(t&&t.silentJSONParsing)&&s;try{return JSON.parse(e)}catch(a){if(r)throw a.name==="SyntaxError"?B.from(a,B.ERR_BAD_RESPONSE,this,null,this.response):a}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:gt.classes.FormData,Blob:gt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};w.forEach(["delete","get","head","post","put","patch"],n=>{mn.headers[n]={}});const Pc=w.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ac=n=>{const e={};let t,i,s;return n&&n.split(`
`).forEach(function(r){s=r.indexOf(":"),t=r.substring(0,s).trim().toLowerCase(),i=r.substring(s+1).trim(),!(!t||e[t]&&Pc[t])&&(t==="set-cookie"?e[t]?e[t].push(i):e[t]=[i]:e[t]=e[t]?e[t]+", "+i:i)}),e},Qs=Symbol("internals");function He(n){return n&&String(n).trim().toLowerCase()}function Vn(n){return n===!1||n==null?n:w.isArray(n)?n.map(Vn):String(n)}function Ec(n){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let i;for(;i=t.exec(n);)e[i[1]]=i[2];return e}const Tc=n=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(n.trim());function Oi(n,e,t,i,s){if(w.isFunction(i))return i.call(this,e,t);if(s&&(e=t),!!w.isString(e)){if(w.isString(i))return e.indexOf(i)!==-1;if(w.isRegExp(i))return i.test(e)}}function Rc(n){return n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,i)=>t.toUpperCase()+i)}function Lc(n,e){const t=w.toCamelCase(" "+e);["get","set","has"].forEach(i=>{Object.defineProperty(n,i+t,{value:function(s,o,r){return this[i].call(this,e,s,o,r)},configurable:!0})})}class Mt{constructor(e){e&&this.set(e)}set(e,t,i){const s=this;function o(a,l,c){const h=He(l);if(!h)throw new Error("header name must be a non-empty string");const u=w.findKey(s,h);(!u||s[u]===void 0||c===!0||c===void 0&&s[u]!==!1)&&(s[u||l]=Vn(a))}const r=(a,l)=>w.forEach(a,(c,h)=>o(c,h,l));if(w.isPlainObject(e)||e instanceof this.constructor)r(e,t);else if(w.isString(e)&&(e=e.trim())&&!Tc(e))r(Ac(e),t);else if(w.isHeaders(e))for(const[a,l]of e.entries())o(l,a,i);else e!=null&&o(t,e,i);return this}get(e,t){if(e=He(e),e){const i=w.findKey(this,e);if(i){const s=this[i];if(!t)return s;if(t===!0)return Ec(s);if(w.isFunction(t))return t.call(this,s,i);if(w.isRegExp(t))return t.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=He(e),e){const i=w.findKey(this,e);return!!(i&&this[i]!==void 0&&(!t||Oi(this,this[i],i,t)))}return!1}delete(e,t){const i=this;let s=!1;function o(r){if(r=He(r),r){const a=w.findKey(i,r);a&&(!t||Oi(i,i[a],a,t))&&(delete i[a],s=!0)}}return w.isArray(e)?e.forEach(o):o(e),s}clear(e){const t=Object.keys(this);let i=t.length,s=!1;for(;i--;){const o=t[i];(!e||Oi(this,this[o],o,e,!0))&&(delete this[o],s=!0)}return s}normalize(e){const t=this,i={};return w.forEach(this,(s,o)=>{const r=w.findKey(i,o);if(r){t[r]=Vn(s),delete t[o];return}const a=e?Rc(o):String(o).trim();a!==o&&delete t[o],t[a]=Vn(s),i[a]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return w.forEach(this,(i,s)=>{i!=null&&i!==!1&&(t[s]=e&&w.isArray(i)?i.join(", "):i)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const i=new this(e);return t.forEach(s=>i.set(s)),i}static accessor(e){const i=(this[Qs]=this[Qs]={accessors:{}}).accessors,s=this.prototype;function o(r){const a=He(r);i[a]||(Lc(s,r),i[a]=!0)}return w.isArray(e)?e.forEach(o):o(e),this}}Mt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);w.reduceDescriptors(Mt.prototype,({value:n},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>n,set(i){this[t]=i}}});w.freezeMethods(Mt);function Pi(n,e){const t=this||mn,i=e||t,s=Mt.from(i.headers);let o=i.data;return w.forEach(n,function(a){o=a.call(t,o,s.normalize(),e?e.status:void 0)}),s.normalize(),o}function Kr(n){return!!(n&&n.__CANCEL__)}function Be(n,e,t){B.call(this,n??"canceled",B.ERR_CANCELED,e,t),this.name="CanceledError"}w.inherits(Be,B,{__CANCEL__:!0});function Jr(n,e,t){const i=t.config.validateStatus;!t.status||!i||i(t.status)?n(t):e(new B("Request failed with status code "+t.status,[B.ERR_BAD_REQUEST,B.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function Fc(n){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(n);return e&&e[1]||""}function Ic(n,e){n=n||10;const t=new Array(n),i=new Array(n);let s=0,o=0,r;return e=e!==void 0?e:1e3,function(l){const c=Date.now(),h=i[o];r||(r=c),t[s]=l,i[s]=c;let u=o,d=0;for(;u!==s;)d+=t[u++],u=u%n;if(s=(s+1)%n,s===o&&(o=(o+1)%n),c-r<e)return;const f=h&&c-h;return f?Math.round(d*1e3/f):void 0}}function Nc(n,e){let t=0,i=1e3/e,s,o;const r=(c,h=Date.now())=>{t=h,s=null,o&&(clearTimeout(o),o=null),n.apply(null,c)};return[(...c)=>{const h=Date.now(),u=h-t;u>=i?r(c,h):(s=c,o||(o=setTimeout(()=>{o=null,r(s)},i-u)))},()=>s&&r(s)]}const ti=(n,e,t=3)=>{let i=0;const s=Ic(50,250);return Nc(o=>{const r=o.loaded,a=o.lengthComputable?o.total:void 0,l=r-i,c=s(l),h=r<=a;i=r;const u={loaded:r,total:a,progress:a?r/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&h?(a-r)/c:void 0,event:o,lengthComputable:a!=null,[e?"download":"upload"]:!0};n(u)},t)},to=(n,e)=>{const t=n!=null;return[i=>e[0]({lengthComputable:t,total:n,loaded:i}),e[1]]},eo=n=>(...e)=>w.asap(()=>n(...e)),Bc=gt.hasStandardBrowserEnv?((n,e)=>t=>(t=new URL(t,gt.origin),n.protocol===t.protocol&&n.host===t.host&&(e||n.port===t.port)))(new URL(gt.origin),gt.navigator&&/(msie|trident)/i.test(gt.navigator.userAgent)):()=>!0,zc=gt.hasStandardBrowserEnv?{write(n,e,t,i,s,o){const r=[n+"="+encodeURIComponent(e)];w.isNumber(t)&&r.push("expires="+new Date(t).toGMTString()),w.isString(i)&&r.push("path="+i),w.isString(s)&&r.push("domain="+s),o===!0&&r.push("secure"),document.cookie=r.join("; ")},read(n){const e=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(n){this.write(n,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Hc(n){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(n)}function jc(n,e){return e?n.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):n}function Gr(n,e){return n&&!Hc(e)?jc(n,e):e}const no=n=>n instanceof Mt?{...n}:n;function De(n,e){e=e||{};const t={};function i(c,h,u,d){return w.isPlainObject(c)&&w.isPlainObject(h)?w.merge.call({caseless:d},c,h):w.isPlainObject(h)?w.merge({},h):w.isArray(h)?h.slice():h}function s(c,h,u,d){if(w.isUndefined(h)){if(!w.isUndefined(c))return i(void 0,c,u,d)}else return i(c,h,u,d)}function o(c,h){if(!w.isUndefined(h))return i(void 0,h)}function r(c,h){if(w.isUndefined(h)){if(!w.isUndefined(c))return i(void 0,c)}else return i(void 0,h)}function a(c,h,u){if(u in e)return i(c,h);if(u in n)return i(void 0,c)}const l={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:a,headers:(c,h,u)=>s(no(c),no(h),u,!0)};return w.forEach(Object.keys(Object.assign({},n,e)),function(h){const u=l[h]||s,d=u(n[h],e[h],h);w.isUndefined(d)&&u!==a||(t[h]=d)}),t}const Zr=n=>{const e=De({},n);let{data:t,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:r,auth:a}=e;e.headers=r=Mt.from(r),e.url=$r(Gr(e.baseURL,e.url),n.params,n.paramsSerializer),a&&r.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(w.isFormData(t)){if(gt.hasStandardBrowserEnv||gt.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if((l=r.getContentType())!==!1){const[c,...h]=l?l.split(";").map(u=>u.trim()).filter(Boolean):[];r.setContentType([c||"multipart/form-data",...h].join("; "))}}if(gt.hasStandardBrowserEnv&&(i&&w.isFunction(i)&&(i=i(e)),i||i!==!1&&Bc(e.url))){const c=s&&o&&zc.read(o);c&&r.set(s,c)}return e},Wc=typeof XMLHttpRequest<"u",Vc=Wc&&function(n){return new Promise(function(t,i){const s=Zr(n);let o=s.data;const r=Mt.from(s.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=s,h,u,d,f,p;function m(){f&&f(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(h),s.signal&&s.signal.removeEventListener("abort",h)}let x=new XMLHttpRequest;x.open(s.method.toUpperCase(),s.url,!0),x.timeout=s.timeout;function y(){if(!x)return;const S=Mt.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),D={data:!a||a==="text"||a==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:S,config:n,request:x};Jr(function(O){t(O),m()},function(O){i(O),m()},D),x=null}"onloadend"in x?x.onloadend=y:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(y)},x.onabort=function(){x&&(i(new B("Request aborted",B.ECONNABORTED,n,x)),x=null)},x.onerror=function(){i(new B("Network Error",B.ERR_NETWORK,n,x)),x=null},x.ontimeout=function(){let k=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const D=s.transitional||qr;s.timeoutErrorMessage&&(k=s.timeoutErrorMessage),i(new B(k,D.clarifyTimeoutError?B.ETIMEDOUT:B.ECONNABORTED,n,x)),x=null},o===void 0&&r.setContentType(null),"setRequestHeader"in x&&w.forEach(r.toJSON(),function(k,D){x.setRequestHeader(D,k)}),w.isUndefined(s.withCredentials)||(x.withCredentials=!!s.withCredentials),a&&a!=="json"&&(x.responseType=s.responseType),c&&([d,p]=ti(c,!0),x.addEventListener("progress",d)),l&&x.upload&&([u,f]=ti(l),x.upload.addEventListener("progress",u),x.upload.addEventListener("loadend",f)),(s.cancelToken||s.signal)&&(h=S=>{x&&(i(!S||S.type?new Be(null,n,x):S),x.abort(),x=null)},s.cancelToken&&s.cancelToken.subscribe(h),s.signal&&(s.signal.aborted?h():s.signal.addEventListener("abort",h)));const v=Fc(s.url);if(v&&gt.protocols.indexOf(v)===-1){i(new B("Unsupported protocol "+v+":",B.ERR_BAD_REQUEST,n));return}x.send(o||null)})},Yc=(n,e)=>{const{length:t}=n=n?n.filter(Boolean):[];if(e||t){let i=new AbortController,s;const o=function(c){if(!s){s=!0,a();const h=c instanceof Error?c:this.reason;i.abort(h instanceof B?h:new Be(h instanceof Error?h.message:h))}};let r=e&&setTimeout(()=>{r=null,o(new B(`timeout ${e} of ms exceeded`,B.ETIMEDOUT))},e);const a=()=>{n&&(r&&clearTimeout(r),r=null,n.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),n=null)};n.forEach(c=>c.addEventListener("abort",o));const{signal:l}=i;return l.unsubscribe=()=>w.asap(a),l}},Uc=function*(n,e){let t=n.byteLength;if(t<e){yield n;return}let i=0,s;for(;i<t;)s=i+e,yield n.slice(i,s),i=s},$c=async function*(n,e){for await(const t of qc(n))yield*Uc(t,e)},qc=async function*(n){if(n[Symbol.asyncIterator]){yield*n;return}const e=n.getReader();try{for(;;){const{done:t,value:i}=await e.read();if(t)break;yield i}}finally{await e.cancel()}},io=(n,e,t,i)=>{const s=$c(n,e);let o=0,r,a=l=>{r||(r=!0,i&&i(l))};return new ReadableStream({async pull(l){try{const{done:c,value:h}=await s.next();if(c){a(),l.close();return}let u=h.byteLength;if(t){let d=o+=u;t(d)}l.enqueue(new Uint8Array(h))}catch(c){throw a(c),c}},cancel(l){return a(l),s.return()}},{highWaterMark:2})},pi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qr=pi&&typeof ReadableStream=="function",Xc=pi&&(typeof TextEncoder=="function"?(n=>e=>n.encode(e))(new TextEncoder):async n=>new Uint8Array(await new Response(n).arrayBuffer())),ta=(n,...e)=>{try{return!!n(...e)}catch{return!1}},Kc=Qr&&ta(()=>{let n=!1;const e=new Request(gt.origin,{body:new ReadableStream,method:"POST",get duplex(){return n=!0,"half"}}).headers.has("Content-Type");return n&&!e}),so=64*1024,Zi=Qr&&ta(()=>w.isReadableStream(new Response("").body)),ei={stream:Zi&&(n=>n.body)};pi&&(n=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!ei[e]&&(ei[e]=w.isFunction(n[e])?t=>t[e]():(t,i)=>{throw new B(`Response type '${e}' is not supported`,B.ERR_NOT_SUPPORT,i)})})})(new Response);const Jc=async n=>{if(n==null)return 0;if(w.isBlob(n))return n.size;if(w.isSpecCompliantForm(n))return(await new Request(gt.origin,{method:"POST",body:n}).arrayBuffer()).byteLength;if(w.isArrayBufferView(n)||w.isArrayBuffer(n))return n.byteLength;if(w.isURLSearchParams(n)&&(n=n+""),w.isString(n))return(await Xc(n)).byteLength},Gc=async(n,e)=>{const t=w.toFiniteNumber(n.getContentLength());return t??Jc(e)},Zc=pi&&(async n=>{let{url:e,method:t,data:i,signal:s,cancelToken:o,timeout:r,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:h,withCredentials:u="same-origin",fetchOptions:d}=Zr(n);c=c?(c+"").toLowerCase():"text";let f=Yc([s,o&&o.toAbortSignal()],r),p;const m=f&&f.unsubscribe&&(()=>{f.unsubscribe()});let x;try{if(l&&Kc&&t!=="get"&&t!=="head"&&(x=await Gc(h,i))!==0){let D=new Request(e,{method:"POST",body:i,duplex:"half"}),C;if(w.isFormData(i)&&(C=D.headers.get("content-type"))&&h.setContentType(C),D.body){const[O,P]=to(x,ti(eo(l)));i=io(D.body,so,O,P)}}w.isString(u)||(u=u?"include":"omit");const y="credentials"in Request.prototype;p=new Request(e,{...d,signal:f,method:t.toUpperCase(),headers:h.normalize().toJSON(),body:i,duplex:"half",credentials:y?u:void 0});let v=await fetch(p);const S=Zi&&(c==="stream"||c==="response");if(Zi&&(a||S&&m)){const D={};["status","statusText","headers"].forEach(F=>{D[F]=v[F]});const C=w.toFiniteNumber(v.headers.get("content-length")),[O,P]=a&&to(C,ti(eo(a),!0))||[];v=new Response(io(v.body,so,O,()=>{P&&P(),m&&m()}),D)}c=c||"text";let k=await ei[w.findKey(ei,c)||"text"](v,n);return!S&&m&&m(),await new Promise((D,C)=>{Jr(D,C,{data:k,headers:Mt.from(v.headers),status:v.status,statusText:v.statusText,config:n,request:p})})}catch(y){throw m&&m(),y&&y.name==="TypeError"&&/fetch/i.test(y.message)?Object.assign(new B("Network Error",B.ERR_NETWORK,n,p),{cause:y.cause||y}):B.from(y,y&&y.code,n,p)}}),Qi={http:fc,xhr:Vc,fetch:Zc};w.forEach(Qi,(n,e)=>{if(n){try{Object.defineProperty(n,"name",{value:e})}catch{}Object.defineProperty(n,"adapterName",{value:e})}});const oo=n=>`- ${n}`,Qc=n=>w.isFunction(n)||n===null||n===!1,ea={getAdapter:n=>{n=w.isArray(n)?n:[n];const{length:e}=n;let t,i;const s={};for(let o=0;o<e;o++){t=n[o];let r;if(i=t,!Qc(t)&&(i=Qi[(r=String(t)).toLowerCase()],i===void 0))throw new B(`Unknown adapter '${r}'`);if(i)break;s[r||"#"+o]=i}if(!i){const o=Object.entries(s).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let r=e?o.length>1?`since :
`+o.map(oo).join(`
`):" "+oo(o[0]):"as no adapter specified";throw new B("There is no suitable adapter to dispatch the request "+r,"ERR_NOT_SUPPORT")}return i},adapters:Qi};function Ai(n){if(n.cancelToken&&n.cancelToken.throwIfRequested(),n.signal&&n.signal.aborted)throw new Be(null,n)}function ro(n){return Ai(n),n.headers=Mt.from(n.headers),n.data=Pi.call(n,n.transformRequest),["post","put","patch"].indexOf(n.method)!==-1&&n.headers.setContentType("application/x-www-form-urlencoded",!1),ea.getAdapter(n.adapter||mn.adapter)(n).then(function(i){return Ai(n),i.data=Pi.call(n,n.transformResponse,i),i.headers=Mt.from(i.headers),i},function(i){return Kr(i)||(Ai(n),i&&i.response&&(i.response.data=Pi.call(n,n.transformResponse,i.response),i.response.headers=Mt.from(i.response.headers))),Promise.reject(i)})}const na="1.7.8",mi={};["object","boolean","number","function","string","symbol"].forEach((n,e)=>{mi[n]=function(i){return typeof i===n||"a"+(e<1?"n ":" ")+n}});const ao={};mi.transitional=function(e,t,i){function s(o,r){return"[Axios v"+na+"] Transitional option '"+o+"'"+r+(i?". "+i:"")}return(o,r,a)=>{if(e===!1)throw new B(s(r," has been removed"+(t?" in "+t:"")),B.ERR_DEPRECATED);return t&&!ao[r]&&(ao[r]=!0,console.warn(s(r," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(o,r,a):!0}};mi.spelling=function(e){return(t,i)=>(console.warn(`${i} is likely a misspelling of ${e}`),!0)};function th(n,e,t){if(typeof n!="object")throw new B("options must be an object",B.ERR_BAD_OPTION_VALUE);const i=Object.keys(n);let s=i.length;for(;s-- >0;){const o=i[s],r=e[o];if(r){const a=n[o],l=a===void 0||r(a,o,n);if(l!==!0)throw new B("option "+o+" must be "+l,B.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new B("Unknown option "+o,B.ERR_BAD_OPTION)}}const Yn={assertOptions:th,validators:mi},zt=Yn.validators;class ve{constructor(e){this.defaults=e,this.interceptors={request:new Zs,response:new Zs}}async request(e,t){try{return await this._request(e,t)}catch(i){if(i instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{i.stack?o&&!String(i.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(i.stack+=`
`+o):i.stack=o}catch{}}throw i}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=De(this.defaults,t);const{transitional:i,paramsSerializer:s,headers:o}=t;i!==void 0&&Yn.assertOptions(i,{silentJSONParsing:zt.transitional(zt.boolean),forcedJSONParsing:zt.transitional(zt.boolean),clarifyTimeoutError:zt.transitional(zt.boolean)},!1),s!=null&&(w.isFunction(s)?t.paramsSerializer={serialize:s}:Yn.assertOptions(s,{encode:zt.function,serialize:zt.function},!0)),Yn.assertOptions(t,{baseUrl:zt.spelling("baseURL"),withXsrfToken:zt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let r=o&&w.merge(o.common,o[t.method]);o&&w.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),t.headers=Mt.concat(r,o);const a=[];let l=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(t)===!1||(l=l&&m.synchronous,a.unshift(m.fulfilled,m.rejected))});const c=[];this.interceptors.response.forEach(function(m){c.push(m.fulfilled,m.rejected)});let h,u=0,d;if(!l){const p=[ro.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,c),d=p.length,h=Promise.resolve(t);u<d;)h=h.then(p[u++],p[u++]);return h}d=a.length;let f=t;for(u=0;u<d;){const p=a[u++],m=a[u++];try{f=p(f)}catch(x){m.call(this,x);break}}try{h=ro.call(this,f)}catch(p){return Promise.reject(p)}for(u=0,d=c.length;u<d;)h=h.then(c[u++],c[u++]);return h}getUri(e){e=De(this.defaults,e);const t=Gr(e.baseURL,e.url);return $r(t,e.params,e.paramsSerializer)}}w.forEach(["delete","get","head","options"],function(e){ve.prototype[e]=function(t,i){return this.request(De(i||{},{method:e,url:t,data:(i||{}).data}))}});w.forEach(["post","put","patch"],function(e){function t(i){return function(o,r,a){return this.request(De(a||{},{method:e,headers:i?{"Content-Type":"multipart/form-data"}:{},url:o,data:r}))}}ve.prototype[e]=t(),ve.prototype[e+"Form"]=t(!0)});class vs{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(o){t=o});const i=this;this.promise.then(s=>{if(!i._listeners)return;let o=i._listeners.length;for(;o-- >0;)i._listeners[o](s);i._listeners=null}),this.promise.then=s=>{let o;const r=new Promise(a=>{i.subscribe(a),o=a}).then(s);return r.cancel=function(){i.unsubscribe(o)},r},e(function(o,r,a){i.reason||(i.reason=new Be(o,r,a),t(i.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=i=>{e.abort(i)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new vs(function(s){e=s}),cancel:e}}}function eh(n){return function(t){return n.apply(null,t)}}function nh(n){return w.isObject(n)&&n.isAxiosError===!0}const ts={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ts).forEach(([n,e])=>{ts[e]=n});function ia(n){const e=new ve(n),t=Lr(ve.prototype.request,e);return w.extend(t,ve.prototype,e,{allOwnKeys:!0}),w.extend(t,e,null,{allOwnKeys:!0}),t.create=function(s){return ia(De(n,s))},t}const lt=ia(mn);lt.Axios=ve;lt.CanceledError=Be;lt.CancelToken=vs;lt.isCancel=Kr;lt.VERSION=na;lt.toFormData=gi;lt.AxiosError=B;lt.Cancel=lt.CanceledError;lt.all=function(e){return Promise.all(e)};lt.spread=eh;lt.isAxiosError=nh;lt.mergeConfig=De;lt.AxiosHeaders=Mt;lt.formToJSON=n=>Xr(w.isHTMLForm(n)?new FormData(n):n);lt.getAdapter=ea.getAdapter;lt.HttpStatusCode=ts;lt.default=lt;window.axios=lt;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function bn(n){return n+.5|0}const ne=(n,e,t)=>Math.max(Math.min(n,t),e);function $e(n){return ne(bn(n*2.55),0,255)}function re(n){return ne(bn(n*255),0,255)}function Xt(n){return ne(bn(n/2.55)/100,0,1)}function lo(n){return ne(bn(n*100),0,100)}const At={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},es=[..."0123456789ABCDEF"],ih=n=>es[n&15],sh=n=>es[(n&240)>>4]+es[n&15],kn=n=>(n&240)>>4===(n&15),oh=n=>kn(n.r)&&kn(n.g)&&kn(n.b)&&kn(n.a);function rh(n){var e=n.length,t;return n[0]==="#"&&(e===4||e===5?t={r:255&At[n[1]]*17,g:255&At[n[2]]*17,b:255&At[n[3]]*17,a:e===5?At[n[4]]*17:255}:(e===7||e===9)&&(t={r:At[n[1]]<<4|At[n[2]],g:At[n[3]]<<4|At[n[4]],b:At[n[5]]<<4|At[n[6]],a:e===9?At[n[7]]<<4|At[n[8]]:255})),t}const ah=(n,e)=>n<255?e(n):"";function lh(n){var e=oh(n)?ih:sh;return n?"#"+e(n.r)+e(n.g)+e(n.b)+ah(n.a,e):void 0}const ch=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function sa(n,e,t){const i=e*Math.min(t,1-t),s=(o,r=(o+n/30)%12)=>t-i*Math.max(Math.min(r-3,9-r,1),-1);return[s(0),s(8),s(4)]}function hh(n,e,t){const i=(s,o=(s+n/60)%6)=>t-t*e*Math.max(Math.min(o,4-o,1),0);return[i(5),i(3),i(1)]}function uh(n,e,t){const i=sa(n,1,.5);let s;for(e+t>1&&(s=1/(e+t),e*=s,t*=s),s=0;s<3;s++)i[s]*=1-e-t,i[s]+=e;return i}function dh(n,e,t,i,s){return n===s?(e-t)/i+(e<t?6:0):e===s?(t-n)/i+2:(n-e)/i+4}function ws(n){const t=n.r/255,i=n.g/255,s=n.b/255,o=Math.max(t,i,s),r=Math.min(t,i,s),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=dh(t,i,s,h,o),l=l*60+.5),[l|0,c||0,a]}function Ms(n,e,t,i){return(Array.isArray(e)?n(e[0],e[1],e[2]):n(e,t,i)).map(re)}function Ds(n,e,t){return Ms(sa,n,e,t)}function fh(n,e,t){return Ms(uh,n,e,t)}function gh(n,e,t){return Ms(hh,n,e,t)}function oa(n){return(n%360+360)%360}function ph(n){const e=ch.exec(n);let t=255,i;if(!e)return;e[5]!==i&&(t=e[6]?$e(+e[5]):re(+e[5]));const s=oa(+e[2]),o=+e[3]/100,r=+e[4]/100;return e[1]==="hwb"?i=fh(s,o,r):e[1]==="hsv"?i=gh(s,o,r):i=Ds(s,o,r),{r:i[0],g:i[1],b:i[2],a:t}}function mh(n,e){var t=ws(n);t[0]=oa(t[0]+e),t=Ds(t),n.r=t[0],n.g=t[1],n.b=t[2]}function bh(n){if(!n)return;const e=ws(n),t=e[0],i=lo(e[1]),s=lo(e[2]);return n.a<255?`hsla(${t}, ${i}%, ${s}%, ${Xt(n.a)})`:`hsl(${t}, ${i}%, ${s}%)`}const co={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ho={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function xh(){const n={},e=Object.keys(ho),t=Object.keys(co);let i,s,o,r,a;for(i=0;i<e.length;i++){for(r=a=e[i],s=0;s<t.length;s++)o=t[s],a=a.replace(o,co[o]);o=parseInt(ho[r],16),n[a]=[o>>16&255,o>>8&255,o&255]}return n}let Cn;function yh(n){Cn||(Cn=xh(),Cn.transparent=[0,0,0,0]);const e=Cn[n.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const _h=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function vh(n){const e=_h.exec(n);let t=255,i,s,o;if(e){if(e[7]!==i){const r=+e[7];t=e[8]?$e(r):ne(r*255,0,255)}return i=+e[1],s=+e[3],o=+e[5],i=255&(e[2]?$e(i):ne(i,0,255)),s=255&(e[4]?$e(s):ne(s,0,255)),o=255&(e[6]?$e(o):ne(o,0,255)),{r:i,g:s,b:o,a:t}}}function wh(n){return n&&(n.a<255?`rgba(${n.r}, ${n.g}, ${n.b}, ${Xt(n.a)})`:`rgb(${n.r}, ${n.g}, ${n.b})`)}const Ei=n=>n<=.0031308?n*12.92:Math.pow(n,1/2.4)*1.055-.055,Pe=n=>n<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4);function Mh(n,e,t){const i=Pe(Xt(n.r)),s=Pe(Xt(n.g)),o=Pe(Xt(n.b));return{r:re(Ei(i+t*(Pe(Xt(e.r))-i))),g:re(Ei(s+t*(Pe(Xt(e.g))-s))),b:re(Ei(o+t*(Pe(Xt(e.b))-o))),a:n.a+t*(e.a-n.a)}}function On(n,e,t){if(n){let i=ws(n);i[e]=Math.max(0,Math.min(i[e]+i[e]*t,e===0?360:1)),i=Ds(i),n.r=i[0],n.g=i[1],n.b=i[2]}}function ra(n,e){return n&&Object.assign(e||{},n)}function uo(n){var e={r:0,g:0,b:0,a:255};return Array.isArray(n)?n.length>=3&&(e={r:n[0],g:n[1],b:n[2],a:255},n.length>3&&(e.a=re(n[3]))):(e=ra(n,{r:0,g:0,b:0,a:1}),e.a=re(e.a)),e}function Dh(n){return n.charAt(0)==="r"?vh(n):ph(n)}class rn{constructor(e){if(e instanceof rn)return e;const t=typeof e;let i;t==="object"?i=uo(e):t==="string"&&(i=rh(e)||yh(e)||Dh(e)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var e=ra(this._rgb);return e&&(e.a=Xt(e.a)),e}set rgb(e){this._rgb=uo(e)}rgbString(){return this._valid?wh(this._rgb):void 0}hexString(){return this._valid?lh(this._rgb):void 0}hslString(){return this._valid?bh(this._rgb):void 0}mix(e,t){if(e){const i=this.rgb,s=e.rgb;let o;const r=t===o?.5:t,a=2*r-1,l=i.a-s.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,i.r=255&c*i.r+o*s.r+.5,i.g=255&c*i.g+o*s.g+.5,i.b=255&c*i.b+o*s.b+.5,i.a=r*i.a+(1-r)*s.a,this.rgb=i}return this}interpolate(e,t){return e&&(this._rgb=Mh(this._rgb,e._rgb,t)),this}clone(){return new rn(this.rgb)}alpha(e){return this._rgb.a=re(e),this}clearer(e){const t=this._rgb;return t.a*=1-e,this}greyscale(){const e=this._rgb,t=bn(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=t,this}opaquer(e){const t=this._rgb;return t.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return On(this._rgb,2,e),this}darken(e){return On(this._rgb,2,-e),this}saturate(e){return On(this._rgb,1,e),this}desaturate(e){return On(this._rgb,1,-e),this}rotate(e){return mh(this._rgb,e),this}}/*!
 * Chart.js v4.4.7
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */function Ut(){}const Sh=(()=>{let n=0;return()=>n++})();function U(n){return n==null}function et(n){if(Array.isArray&&Array.isArray(n))return!0;const e=Object.prototype.toString.call(n);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function Y(n){return n!==null&&Object.prototype.toString.call(n)==="[object Object]"}function ot(n){return(typeof n=="number"||n instanceof Number)&&isFinite(+n)}function Ct(n,e){return ot(n)?n:e}function z(n,e){return typeof n>"u"?e:n}const kh=(n,e)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100:+n/e,aa=(n,e)=>typeof n=="string"&&n.endsWith("%")?parseFloat(n)/100*e:+n;function G(n,e,t){if(n&&typeof n.call=="function")return n.apply(t,e)}function X(n,e,t,i){let s,o,r;if(et(n))for(o=n.length,s=0;s<o;s++)e.call(t,n[s],s);else if(Y(n))for(r=Object.keys(n),o=r.length,s=0;s<o;s++)e.call(t,n[r[s]],r[s])}function ni(n,e){let t,i,s,o;if(!n||!e||n.length!==e.length)return!1;for(t=0,i=n.length;t<i;++t)if(s=n[t],o=e[t],s.datasetIndex!==o.datasetIndex||s.index!==o.index)return!1;return!0}function ii(n){if(et(n))return n.map(ii);if(Y(n)){const e=Object.create(null),t=Object.keys(n),i=t.length;let s=0;for(;s<i;++s)e[t[s]]=ii(n[t[s]]);return e}return n}function la(n){return["__proto__","prototype","constructor"].indexOf(n)===-1}function Ch(n,e,t,i){if(!la(n))return;const s=e[n],o=t[n];Y(s)&&Y(o)?an(s,o,i):e[n]=ii(o)}function an(n,e,t){const i=et(e)?e:[e],s=i.length;if(!Y(n))return n;t=t||{};const o=t.merger||Ch;let r;for(let a=0;a<s;++a){if(r=i[a],!Y(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],n,r,t)}return n}function Ze(n,e){return an(n,e,{merger:Oh})}function Oh(n,e,t){if(!la(n))return;const i=e[n],s=t[n];Y(i)&&Y(s)?Ze(i,s):Object.prototype.hasOwnProperty.call(e,n)||(e[n]=ii(s))}const fo={"":n=>n,x:n=>n.x,y:n=>n.y};function Ph(n){const e=n.split("."),t=[];let i="";for(const s of e)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(t.push(i),i="");return t}function Ah(n){const e=Ph(n);return t=>{for(const i of e){if(i==="")break;t=t&&t[i]}return t}}function ae(n,e){return(fo[e]||(fo[e]=Ah(e)))(n)}function Ss(n){return n.charAt(0).toUpperCase()+n.slice(1)}const ln=n=>typeof n<"u",le=n=>typeof n=="function",go=(n,e)=>{if(n.size!==e.size)return!1;for(const t of n)if(!e.has(t))return!1;return!0};function Eh(n){return n.type==="mouseup"||n.type==="click"||n.type==="contextmenu"}const Q=Math.PI,Z=2*Q,Th=Z+Q,si=Number.POSITIVE_INFINITY,Rh=Q/180,rt=Q/2,de=Q/4,po=Q*2/3,ie=Math.log10,Wt=Math.sign;function Qe(n,e,t){return Math.abs(n-e)<t}function mo(n){const e=Math.round(n);n=Qe(n,e,n/1e3)?e:n;const t=Math.pow(10,Math.floor(ie(n))),i=n/t;return(i<=1?1:i<=2?2:i<=5?5:10)*t}function Lh(n){const e=[],t=Math.sqrt(n);let i;for(i=1;i<t;i++)n%i===0&&(e.push(i),e.push(n/i));return t===(t|0)&&e.push(t),e.sort((s,o)=>s-o).pop(),e}function Le(n){return!isNaN(parseFloat(n))&&isFinite(n)}function Fh(n,e){const t=Math.round(n);return t-e<=n&&t+e>=n}function ca(n,e,t){let i,s,o;for(i=0,s=n.length;i<s;i++)o=n[i][t],isNaN(o)||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function Lt(n){return n*(Q/180)}function ks(n){return n*(180/Q)}function bo(n){if(!ot(n))return;let e=1,t=0;for(;Math.round(n*e)/e!==n;)e*=10,t++;return t}function ha(n,e){const t=e.x-n.x,i=e.y-n.y,s=Math.sqrt(t*t+i*i);let o=Math.atan2(i,t);return o<-.5*Q&&(o+=Z),{angle:o,distance:s}}function ns(n,e){return Math.sqrt(Math.pow(e.x-n.x,2)+Math.pow(e.y-n.y,2))}function Ih(n,e){return(n-e+Th)%Z-Q}function Ot(n){return(n%Z+Z)%Z}function cn(n,e,t,i){const s=Ot(n),o=Ot(e),r=Ot(t),a=Ot(o-s),l=Ot(r-s),c=Ot(s-o),h=Ot(s-r);return s===o||s===r||i&&o===r||a>l&&c<h}function ut(n,e,t){return Math.max(e,Math.min(t,n))}function Nh(n){return ut(n,-32768,32767)}function Jt(n,e,t,i=1e-6){return n>=Math.min(e,t)-i&&n<=Math.max(e,t)+i}function Cs(n,e,t){t=t||(r=>n[r]<e);let i=n.length-1,s=0,o;for(;i-s>1;)o=s+i>>1,t(o)?s=o:i=o;return{lo:s,hi:i}}const Gt=(n,e,t,i)=>Cs(n,t,i?s=>{const o=n[s][e];return o<t||o===t&&n[s+1][e]===t}:s=>n[s][e]<t),Bh=(n,e,t)=>Cs(n,t,i=>n[i][e]>=t);function zh(n,e,t){let i=0,s=n.length;for(;i<s&&n[i]<e;)i++;for(;s>i&&n[s-1]>t;)s--;return i>0||s<n.length?n.slice(i,s):n}const ua=["push","pop","shift","splice","unshift"];function Hh(n,e){if(n._chartjs){n._chartjs.listeners.push(e);return}Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),ua.forEach(t=>{const i="_onData"+Ss(t),s=n[t];Object.defineProperty(n,t,{configurable:!0,enumerable:!1,value(...o){const r=s.apply(this,o);return n._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...o)}),r}})})}function xo(n,e){const t=n._chartjs;if(!t)return;const i=t.listeners,s=i.indexOf(e);s!==-1&&i.splice(s,1),!(i.length>0)&&(ua.forEach(o=>{delete n[o]}),delete n._chartjs)}function da(n){const e=new Set(n);return e.size===n.length?n:Array.from(e)}const fa=function(){return typeof window>"u"?function(n){return n()}:window.requestAnimationFrame}();function ga(n,e){let t=[],i=!1;return function(...s){t=s,i||(i=!0,fa.call(window,()=>{i=!1,n.apply(e,t)}))}}function jh(n,e){let t;return function(...i){return e?(clearTimeout(t),t=setTimeout(n,e,i)):n.apply(this,i),e}}const Os=n=>n==="start"?"left":n==="end"?"right":"center",dt=(n,e,t)=>n==="start"?e:n==="end"?t:(e+t)/2,Wh=(n,e,t,i)=>n===(i?"left":"right")?t:n==="center"?(e+t)/2:e;function pa(n,e,t){const i=e.length;let s=0,o=i;if(n._sorted){const{iScale:r,_parsed:a}=n,l=r.axis,{min:c,max:h,minDefined:u,maxDefined:d}=r.getUserBounds();u&&(s=ut(Math.min(Gt(a,l,c).lo,t?i:Gt(e,l,r.getPixelForValue(c)).lo),0,i-1)),d?o=ut(Math.max(Gt(a,r.axis,h,!0).hi+1,t?0:Gt(e,l,r.getPixelForValue(h),!0).hi+1),s,i)-s:o=i-s}return{start:s,count:o}}function ma(n){const{xScale:e,yScale:t,_scaleRanges:i}=n,s={xmin:e.min,xmax:e.max,ymin:t.min,ymax:t.max};if(!i)return n._scaleRanges=s,!0;const o=i.xmin!==e.min||i.xmax!==e.max||i.ymin!==t.min||i.ymax!==t.max;return Object.assign(i,s),o}const Pn=n=>n===0||n===1,yo=(n,e,t)=>-(Math.pow(2,10*(n-=1))*Math.sin((n-e)*Z/t)),_o=(n,e,t)=>Math.pow(2,-10*n)*Math.sin((n-e)*Z/t)+1,tn={linear:n=>n,easeInQuad:n=>n*n,easeOutQuad:n=>-n*(n-2),easeInOutQuad:n=>(n/=.5)<1?.5*n*n:-.5*(--n*(n-2)-1),easeInCubic:n=>n*n*n,easeOutCubic:n=>(n-=1)*n*n+1,easeInOutCubic:n=>(n/=.5)<1?.5*n*n*n:.5*((n-=2)*n*n+2),easeInQuart:n=>n*n*n*n,easeOutQuart:n=>-((n-=1)*n*n*n-1),easeInOutQuart:n=>(n/=.5)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2),easeInQuint:n=>n*n*n*n*n,easeOutQuint:n=>(n-=1)*n*n*n*n+1,easeInOutQuint:n=>(n/=.5)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2),easeInSine:n=>-Math.cos(n*rt)+1,easeOutSine:n=>Math.sin(n*rt),easeInOutSine:n=>-.5*(Math.cos(Q*n)-1),easeInExpo:n=>n===0?0:Math.pow(2,10*(n-1)),easeOutExpo:n=>n===1?1:-Math.pow(2,-10*n)+1,easeInOutExpo:n=>Pn(n)?n:n<.5?.5*Math.pow(2,10*(n*2-1)):.5*(-Math.pow(2,-10*(n*2-1))+2),easeInCirc:n=>n>=1?n:-(Math.sqrt(1-n*n)-1),easeOutCirc:n=>Math.sqrt(1-(n-=1)*n),easeInOutCirc:n=>(n/=.5)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1),easeInElastic:n=>Pn(n)?n:yo(n,.075,.3),easeOutElastic:n=>Pn(n)?n:_o(n,.075,.3),easeInOutElastic(n){return Pn(n)?n:n<.5?.5*yo(n*2,.1125,.45):.5+.5*_o(n*2-1,.1125,.45)},easeInBack(n){return n*n*((1.70158+1)*n-1.70158)},easeOutBack(n){return(n-=1)*n*((1.70158+1)*n********)+1},easeInOutBack(n){let e=1.70158;return(n/=.5)<1?.5*(n*n*(((e*=1.525)+1)*n-e)):.5*((n-=2)*n*(((e*=1.525)+1)*n+e)+2)},easeInBounce:n=>1-tn.easeOutBounce(1-n),easeOutBounce(n){return n<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375},easeInOutBounce:n=>n<.5?tn.easeInBounce(n*2)*.5:tn.easeOutBounce(n*2-1)*.5+.5};function Ps(n){if(n&&typeof n=="object"){const e=n.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function vo(n){return Ps(n)?n:new rn(n)}function Ti(n){return Ps(n)?n:new rn(n).saturate(.5).darken(.1).hexString()}const Vh=["x","y","borderWidth","radius","tension"],Yh=["color","borderColor","backgroundColor"];function Uh(n){n.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),n.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),n.set("animations",{colors:{type:"color",properties:Yh},numbers:{type:"number",properties:Vh}}),n.describe("animations",{_fallback:"animation"}),n.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function $h(n){n.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const wo=new Map;function qh(n,e){e=e||{};const t=n+JSON.stringify(e);let i=wo.get(t);return i||(i=new Intl.NumberFormat(n,e),wo.set(t,i)),i}function xn(n,e,t){return qh(e,t).format(n)}const ba={values(n){return et(n)?n:""+n},numeric(n,e,t){if(n===0)return"0";const i=this.chart.options.locale;let s,o=n;if(t.length>1){const c=Math.max(Math.abs(t[0].value),Math.abs(t[t.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),o=Xh(n,t)}const r=ie(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),xn(n,i,l)},logarithmic(n,e,t){if(n===0)return"0";const i=t[e].significand||n/Math.pow(10,Math.floor(ie(n)));return[1,2,3,5,10,15].includes(i)||e>.8*t.length?ba.numeric.call(this,n,e,t):""}};function Xh(n,e){let t=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(t)>=1&&n!==Math.floor(n)&&(t=n-Math.floor(n)),t}var bi={formatters:ba};function Kh(n){n.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,t)=>t.lineWidth,tickColor:(e,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:bi.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),n.route("scale.ticks","color","","color"),n.route("scale.grid","color","","borderColor"),n.route("scale.border","color","","borderColor"),n.route("scale.title","color","","color"),n.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),n.describe("scales",{_fallback:"scale"}),n.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Se=Object.create(null),is=Object.create(null);function en(n,e){if(!e)return n;const t=e.split(".");for(let i=0,s=t.length;i<s;++i){const o=t[i];n=n[o]||(n[o]=Object.create(null))}return n}function Ri(n,e,t){return typeof e=="string"?an(en(n,e),t):an(en(n,""),e)}class Jh{constructor(e,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>Ti(s.backgroundColor),this.hoverBorderColor=(i,s)=>Ti(s.borderColor),this.hoverColor=(i,s)=>Ti(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(t)}set(e,t){return Ri(this,e,t)}get(e){return en(this,e)}describe(e,t){return Ri(is,e,t)}override(e,t){return Ri(Se,e,t)}route(e,t,i,s){const o=en(this,e),r=en(this,i),a="_"+t;Object.defineProperties(o,{[a]:{value:o[t],writable:!0},[t]:{enumerable:!0,get(){const l=this[a],c=r[s];return Y(l)?Object.assign({},c,l):z(l,c)},set(l){this[a]=l}}})}apply(e){e.forEach(t=>t(this))}}var nt=new Jh({_scriptable:n=>!n.startsWith("on"),_indexable:n=>n!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Uh,$h,Kh]);function Gh(n){return!n||U(n.size)||U(n.family)?null:(n.style?n.style+" ":"")+(n.weight?n.weight+" ":"")+n.size+"px "+n.family}function oi(n,e,t,i,s){let o=e[s];return o||(o=e[s]=n.measureText(s).width,t.push(s)),o>i&&(i=o),i}function Zh(n,e,t,i){i=i||{};let s=i.data=i.data||{},o=i.garbageCollect=i.garbageCollect||[];i.font!==e&&(s=i.data={},o=i.garbageCollect=[],i.font=e),n.save(),n.font=e;let r=0;const a=t.length;let l,c,h,u,d;for(l=0;l<a;l++)if(u=t[l],u!=null&&!et(u))r=oi(n,s,o,r,u);else if(et(u))for(c=0,h=u.length;c<h;c++)d=u[c],d!=null&&!et(d)&&(r=oi(n,s,o,r,d));n.restore();const f=o.length/2;if(f>t.length){for(l=0;l<f;l++)delete s[o[l]];o.splice(0,f)}return r}function fe(n,e,t){const i=n.currentDevicePixelRatio,s=t!==0?Math.max(t/2,.5):0;return Math.round((e-s)*i)/i+s}function Mo(n,e){!e&&!n||(e=e||n.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,n.width,n.height),e.restore())}function ss(n,e,t,i){xa(n,e,t,i,null)}function xa(n,e,t,i,s){let o,r,a,l,c,h,u,d;const f=e.pointStyle,p=e.rotation,m=e.radius;let x=(p||0)*Rh;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){n.save(),n.translate(t,i),n.rotate(x),n.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),n.restore();return}if(!(isNaN(m)||m<=0)){switch(n.beginPath(),f){default:s?n.ellipse(t,i,s/2,m,0,0,Z):n.arc(t,i,m,0,Z),n.closePath();break;case"triangle":h=s?s/2:m,n.moveTo(t+Math.sin(x)*h,i-Math.cos(x)*m),x+=po,n.lineTo(t+Math.sin(x)*h,i-Math.cos(x)*m),x+=po,n.lineTo(t+Math.sin(x)*h,i-Math.cos(x)*m),n.closePath();break;case"rectRounded":c=m*.516,l=m-c,r=Math.cos(x+de)*l,u=Math.cos(x+de)*(s?s/2-c:l),a=Math.sin(x+de)*l,d=Math.sin(x+de)*(s?s/2-c:l),n.arc(t-u,i-a,c,x-Q,x-rt),n.arc(t+d,i-r,c,x-rt,x),n.arc(t+u,i+a,c,x,x+rt),n.arc(t-d,i+r,c,x+rt,x+Q),n.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*m,h=s?s/2:l,n.rect(t-h,i-l,2*h,2*l);break}x+=de;case"rectRot":u=Math.cos(x)*(s?s/2:m),r=Math.cos(x)*m,a=Math.sin(x)*m,d=Math.sin(x)*(s?s/2:m),n.moveTo(t-u,i-a),n.lineTo(t+d,i-r),n.lineTo(t+u,i+a),n.lineTo(t-d,i+r),n.closePath();break;case"crossRot":x+=de;case"cross":u=Math.cos(x)*(s?s/2:m),r=Math.cos(x)*m,a=Math.sin(x)*m,d=Math.sin(x)*(s?s/2:m),n.moveTo(t-u,i-a),n.lineTo(t+u,i+a),n.moveTo(t+d,i-r),n.lineTo(t-d,i+r);break;case"star":u=Math.cos(x)*(s?s/2:m),r=Math.cos(x)*m,a=Math.sin(x)*m,d=Math.sin(x)*(s?s/2:m),n.moveTo(t-u,i-a),n.lineTo(t+u,i+a),n.moveTo(t+d,i-r),n.lineTo(t-d,i+r),x+=de,u=Math.cos(x)*(s?s/2:m),r=Math.cos(x)*m,a=Math.sin(x)*m,d=Math.sin(x)*(s?s/2:m),n.moveTo(t-u,i-a),n.lineTo(t+u,i+a),n.moveTo(t+d,i-r),n.lineTo(t-d,i+r);break;case"line":r=s?s/2:Math.cos(x)*m,a=Math.sin(x)*m,n.moveTo(t-r,i-a),n.lineTo(t+r,i+a);break;case"dash":n.moveTo(t,i),n.lineTo(t+Math.cos(x)*(s?s/2:m),i+Math.sin(x)*m);break;case!1:n.closePath();break}n.fill(),e.borderWidth>0&&n.stroke()}}function Zt(n,e,t){return t=t||.5,!e||n&&n.x>e.left-t&&n.x<e.right+t&&n.y>e.top-t&&n.y<e.bottom+t}function xi(n,e){n.save(),n.beginPath(),n.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),n.clip()}function yi(n){n.restore()}function Qh(n,e,t,i,s){if(!e)return n.lineTo(t.x,t.y);if(s==="middle"){const o=(e.x+t.x)/2;n.lineTo(o,e.y),n.lineTo(o,t.y)}else s==="after"!=!!i?n.lineTo(e.x,t.y):n.lineTo(t.x,e.y);n.lineTo(t.x,t.y)}function tu(n,e,t,i){if(!e)return n.lineTo(t.x,t.y);n.bezierCurveTo(i?e.cp1x:e.cp2x,i?e.cp1y:e.cp2y,i?t.cp2x:t.cp1x,i?t.cp2y:t.cp1y,t.x,t.y)}function eu(n,e){e.translation&&n.translate(e.translation[0],e.translation[1]),U(e.rotation)||n.rotate(e.rotation),e.color&&(n.fillStyle=e.color),e.textAlign&&(n.textAlign=e.textAlign),e.textBaseline&&(n.textBaseline=e.textBaseline)}function nu(n,e,t,i,s){if(s.strikethrough||s.underline){const o=n.measureText(i),r=e-o.actualBoundingBoxLeft,a=e+o.actualBoundingBoxRight,l=t-o.actualBoundingBoxAscent,c=t+o.actualBoundingBoxDescent,h=s.strikethrough?(l+c)/2:c;n.strokeStyle=n.fillStyle,n.beginPath(),n.lineWidth=s.decorationWidth||2,n.moveTo(r,h),n.lineTo(a,h),n.stroke()}}function iu(n,e){const t=n.fillStyle;n.fillStyle=e.color,n.fillRect(e.left,e.top,e.width,e.height),n.fillStyle=t}function ke(n,e,t,i,s,o={}){const r=et(e)?e:[e],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(n.save(),n.font=s.string,eu(n,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&iu(n,o.backdrop),a&&(o.strokeColor&&(n.strokeStyle=o.strokeColor),U(o.strokeWidth)||(n.lineWidth=o.strokeWidth),n.strokeText(c,t,i,o.maxWidth)),n.fillText(c,t,i,o.maxWidth),nu(n,t,i,c,o),i+=Number(s.lineHeight);n.restore()}function hn(n,e){const{x:t,y:i,w:s,h:o,radius:r}=e;n.arc(t+r.topLeft,i+r.topLeft,r.topLeft,1.5*Q,Q,!0),n.lineTo(t,i+o-r.bottomLeft),n.arc(t+r.bottomLeft,i+o-r.bottomLeft,r.bottomLeft,Q,rt,!0),n.lineTo(t+s-r.bottomRight,i+o),n.arc(t+s-r.bottomRight,i+o-r.bottomRight,r.bottomRight,rt,0,!0),n.lineTo(t+s,i+r.topRight),n.arc(t+s-r.topRight,i+r.topRight,r.topRight,0,-rt,!0),n.lineTo(t+r.topLeft,i)}const su=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ou=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function ru(n,e){const t=(""+n).match(su);if(!t||t[1]==="normal")return e*1.2;switch(n=+t[2],t[3]){case"px":return n;case"%":n/=100;break}return e*n}const au=n=>+n||0;function As(n,e){const t={},i=Y(e),s=i?Object.keys(e):e,o=Y(n)?i?r=>z(n[r],n[e[r]]):r=>n[r]:()=>n;for(const r of s)t[r]=au(o(r));return t}function ya(n){return As(n,{top:"y",right:"x",bottom:"y",left:"x"})}function we(n){return As(n,["topLeft","topRight","bottomLeft","bottomRight"])}function mt(n){const e=ya(n);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ht(n,e){n=n||{},e=e||nt.font;let t=z(n.size,e.size);typeof t=="string"&&(t=parseInt(t,10));let i=z(n.style,e.style);i&&!(""+i).match(ou)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:z(n.family,e.family),lineHeight:ru(z(n.lineHeight,e.lineHeight),t),size:t,style:i,weight:z(n.weight,e.weight),string:""};return s.string=Gh(s),s}function qe(n,e,t,i){let s,o,r;for(s=0,o=n.length;s<o;++s)if(r=n[s],r!==void 0&&r!==void 0)return r}function lu(n,e,t){const{min:i,max:s}=n,o=aa(e,(s-i)/2),r=(a,l)=>t&&a===0?0:a+l;return{min:r(i,-Math.abs(o)),max:r(s,o)}}function ce(n,e){return Object.assign(Object.create(n),e)}function Es(n,e=[""],t,i,s=()=>n[0]){const o=t||n;typeof i>"u"&&(i=Ma("_fallback",n));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:n,_rootScopes:o,_fallback:i,_getTarget:s,override:a=>Es([a,...n],e,o,i)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete n[0][l],!0},get(a,l){return va(a,l,()=>mu(l,e,n,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(n[0])},has(a,l){return So(a).includes(l)},ownKeys(a){return So(a)},set(a,l,c){const h=a._storage||(a._storage=s());return a[l]=h[l]=c,delete a._keys,!0}})}function Fe(n,e,t,i){const s={_cacheable:!1,_proxy:n,_context:e,_subProxy:t,_stack:new Set,_descriptors:_a(n,i),setContext:o=>Fe(n,o,t,i),override:o=>Fe(n.override(o),e,t,i)};return new Proxy(s,{deleteProperty(o,r){return delete o[r],delete n[r],!0},get(o,r,a){return va(o,r,()=>hu(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(n,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(n,r)},getPrototypeOf(){return Reflect.getPrototypeOf(n)},has(o,r){return Reflect.has(n,r)},ownKeys(){return Reflect.ownKeys(n)},set(o,r,a){return n[r]=a,delete o[r],!0}})}function _a(n,e={scriptable:!0,indexable:!0}){const{_scriptable:t=e.scriptable,_indexable:i=e.indexable,_allKeys:s=e.allKeys}=n;return{allKeys:s,scriptable:t,indexable:i,isScriptable:le(t)?t:()=>t,isIndexable:le(i)?i:()=>i}}const cu=(n,e)=>n?n+Ss(e):e,Ts=(n,e)=>Y(e)&&n!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function va(n,e,t){if(Object.prototype.hasOwnProperty.call(n,e)||e==="constructor")return n[e];const i=t();return n[e]=i,i}function hu(n,e,t){const{_proxy:i,_context:s,_subProxy:o,_descriptors:r}=n;let a=i[e];return le(a)&&r.isScriptable(e)&&(a=uu(e,a,n,t)),et(a)&&a.length&&(a=du(e,a,n,r.isIndexable)),Ts(e,a)&&(a=Fe(a,s,o&&o[e],r)),a}function uu(n,e,t,i){const{_proxy:s,_context:o,_subProxy:r,_stack:a}=t;if(a.has(n))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+n);a.add(n);let l=e(o,r||i);return a.delete(n),Ts(n,l)&&(l=Rs(s._scopes,s,n,l)),l}function du(n,e,t,i){const{_proxy:s,_context:o,_subProxy:r,_descriptors:a}=t;if(typeof o.index<"u"&&i(n))return e[o.index%e.length];if(Y(e[0])){const l=e,c=s._scopes.filter(h=>h!==l);e=[];for(const h of l){const u=Rs(c,s,n,h);e.push(Fe(u,o,r&&r[n],a))}}return e}function wa(n,e,t){return le(n)?n(e,t):n}const fu=(n,e)=>n===!0?e:typeof n=="string"?ae(e,n):void 0;function gu(n,e,t,i,s){for(const o of e){const r=fu(t,o);if(r){n.add(r);const a=wa(r._fallback,t,s);if(typeof a<"u"&&a!==t&&a!==i)return a}else if(r===!1&&typeof i<"u"&&t!==i)return null}return!1}function Rs(n,e,t,i){const s=e._rootScopes,o=wa(e._fallback,t,i),r=[...n,...s],a=new Set;a.add(i);let l=Do(a,r,t,o||t,i);return l===null||typeof o<"u"&&o!==t&&(l=Do(a,r,o,l,i),l===null)?!1:Es(Array.from(a),[""],s,o,()=>pu(e,t,i))}function Do(n,e,t,i,s){for(;t;)t=gu(n,e,t,i,s);return t}function pu(n,e,t){const i=n._getTarget();e in i||(i[e]={});const s=i[e];return et(s)&&Y(t)?t:s||{}}function mu(n,e,t,i){let s;for(const o of e)if(s=Ma(cu(o,n),t),typeof s<"u")return Ts(n,s)?Rs(t,i,n,s):s}function Ma(n,e){for(const t of e){if(!t)continue;const i=t[n];if(typeof i<"u")return i}}function So(n){let e=n._keys;return e||(e=n._keys=bu(n._scopes)),e}function bu(n){const e=new Set;for(const t of n)for(const i of Object.keys(t).filter(s=>!s.startsWith("_")))e.add(i);return Array.from(e)}function Da(n,e,t,i){const{iScale:s}=n,{key:o="r"}=this._parsing,r=new Array(i);let a,l,c,h;for(a=0,l=i;a<l;++a)c=a+t,h=e[c],r[a]={r:s.parse(ae(h,o),c)};return r}const xu=Number.EPSILON||1e-14,Ie=(n,e)=>e<n.length&&!n[e].skip&&n[e],Sa=n=>n==="x"?"y":"x";function yu(n,e,t,i){const s=n.skip?e:n,o=e,r=t.skip?e:t,a=ns(o,s),l=ns(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const u=i*c,d=i*h;return{previous:{x:o.x-u*(r.x-s.x),y:o.y-u*(r.y-s.y)},next:{x:o.x+d*(r.x-s.x),y:o.y+d*(r.y-s.y)}}}function _u(n,e,t){const i=n.length;let s,o,r,a,l,c=Ie(n,0);for(let h=0;h<i-1;++h)if(l=c,c=Ie(n,h+1),!(!l||!c)){if(Qe(e[h],0,xu)){t[h]=t[h+1]=0;continue}s=t[h]/e[h],o=t[h+1]/e[h],a=Math.pow(s,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),t[h]=s*r*e[h],t[h+1]=o*r*e[h])}}function vu(n,e,t="x"){const i=Sa(t),s=n.length;let o,r,a,l=Ie(n,0);for(let c=0;c<s;++c){if(r=a,a=l,l=Ie(n,c+1),!a)continue;const h=a[t],u=a[i];r&&(o=(h-r[t])/3,a[`cp1${t}`]=h-o,a[`cp1${i}`]=u-o*e[c]),l&&(o=(l[t]-h)/3,a[`cp2${t}`]=h+o,a[`cp2${i}`]=u+o*e[c])}}function wu(n,e="x"){const t=Sa(e),i=n.length,s=Array(i).fill(0),o=Array(i);let r,a,l,c=Ie(n,0);for(r=0;r<i;++r)if(a=l,l=c,c=Ie(n,r+1),!!l){if(c){const h=c[e]-l[e];s[r]=h!==0?(c[t]-l[t])/h:0}o[r]=a?c?Wt(s[r-1])!==Wt(s[r])?0:(s[r-1]+s[r])/2:s[r-1]:s[r]}_u(n,s,o),vu(n,o,e)}function An(n,e,t){return Math.max(Math.min(n,t),e)}function Mu(n,e){let t,i,s,o,r,a=Zt(n[0],e);for(t=0,i=n.length;t<i;++t)r=o,o=a,a=t<i-1&&Zt(n[t+1],e),o&&(s=n[t],r&&(s.cp1x=An(s.cp1x,e.left,e.right),s.cp1y=An(s.cp1y,e.top,e.bottom)),a&&(s.cp2x=An(s.cp2x,e.left,e.right),s.cp2y=An(s.cp2y,e.top,e.bottom)))}function Du(n,e,t,i,s){let o,r,a,l;if(e.spanGaps&&(n=n.filter(c=>!c.skip)),e.cubicInterpolationMode==="monotone")wu(n,s);else{let c=i?n[n.length-1]:n[0];for(o=0,r=n.length;o<r;++o)a=n[o],l=yu(c,a,n[Math.min(o+1,r-(i?0:1))%r],e.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}e.capBezierPoints&&Mu(n,t)}function Ls(){return typeof window<"u"&&typeof document<"u"}function Fs(n){let e=n.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function ri(n,e,t){let i;return typeof n=="string"?(i=parseInt(n,10),n.indexOf("%")!==-1&&(i=i/100*e.parentNode[t])):i=n,i}const _i=n=>n.ownerDocument.defaultView.getComputedStyle(n,null);function Su(n,e){return _i(n).getPropertyValue(e)}const ku=["top","right","bottom","left"];function Me(n,e,t){const i={};t=t?"-"+t:"";for(let s=0;s<4;s++){const o=ku[s];i[o]=parseFloat(n[e+"-"+o+t])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const Cu=(n,e,t)=>(n>0||e>0)&&(!t||!t.shadowRoot);function Ou(n,e){const t=n.touches,i=t&&t.length?t[0]:n,{offsetX:s,offsetY:o}=i;let r=!1,a,l;if(Cu(s,o,n.target))a=s,l=o;else{const c=e.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function me(n,e){if("native"in n)return n;const{canvas:t,currentDevicePixelRatio:i}=e,s=_i(t),o=s.boxSizing==="border-box",r=Me(s,"padding"),a=Me(s,"border","width"),{x:l,y:c,box:h}=Ou(n,t),u=r.left+(h&&a.left),d=r.top+(h&&a.top);let{width:f,height:p}=e;return o&&(f-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-u)/f*t.width/i),y:Math.round((c-d)/p*t.height/i)}}function Pu(n,e,t){let i,s;if(e===void 0||t===void 0){const o=n&&Fs(n);if(!o)e=n.clientWidth,t=n.clientHeight;else{const r=o.getBoundingClientRect(),a=_i(o),l=Me(a,"border","width"),c=Me(a,"padding");e=r.width-c.width-l.width,t=r.height-c.height-l.height,i=ri(a.maxWidth,o,"clientWidth"),s=ri(a.maxHeight,o,"clientHeight")}}return{width:e,height:t,maxWidth:i||si,maxHeight:s||si}}const En=n=>Math.round(n*10)/10;function Au(n,e,t,i){const s=_i(n),o=Me(s,"margin"),r=ri(s.maxWidth,n,"clientWidth")||si,a=ri(s.maxHeight,n,"clientHeight")||si,l=Pu(n,e,t);let{width:c,height:h}=l;if(s.boxSizing==="content-box"){const d=Me(s,"border","width"),f=Me(s,"padding");c-=f.width+d.width,h-=f.height+d.height}return c=Math.max(0,c-o.width),h=Math.max(0,i?c/i:h-o.height),c=En(Math.min(c,r,l.maxWidth)),h=En(Math.min(h,a,l.maxHeight)),c&&!h&&(h=En(c/2)),(e!==void 0||t!==void 0)&&i&&l.height&&h>l.height&&(h=l.height,c=En(Math.floor(h*i))),{width:c,height:h}}function ko(n,e,t){const i=e||1,s=Math.floor(n.height*i),o=Math.floor(n.width*i);n.height=Math.floor(n.height),n.width=Math.floor(n.width);const r=n.canvas;return r.style&&(t||!r.style.height&&!r.style.width)&&(r.style.height=`${n.height}px`,r.style.width=`${n.width}px`),n.currentDevicePixelRatio!==i||r.height!==s||r.width!==o?(n.currentDevicePixelRatio=i,r.height=s,r.width=o,n.ctx.setTransform(i,0,0,i,0,0),!0):!1}const Eu=function(){let n=!1;try{const e={get passive(){return n=!0,!1}};Ls()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return n}();function Co(n,e){const t=Su(n,e),i=t&&t.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function be(n,e,t,i){return{x:n.x+t*(e.x-n.x),y:n.y+t*(e.y-n.y)}}function Tu(n,e,t,i){return{x:n.x+t*(e.x-n.x),y:i==="middle"?t<.5?n.y:e.y:i==="after"?t<1?n.y:e.y:t>0?e.y:n.y}}function Ru(n,e,t,i){const s={x:n.cp2x,y:n.cp2y},o={x:e.cp1x,y:e.cp1y},r=be(n,s,t),a=be(s,o,t),l=be(o,e,t),c=be(r,a,t),h=be(a,l,t);return be(c,h,t)}const Lu=function(n,e){return{x(t){return n+n+e-t},setWidth(t){e=t},textAlign(t){return t==="center"?t:t==="right"?"left":"right"},xPlus(t,i){return t-i},leftForLtr(t,i){return t-i}}},Fu=function(){return{x(n){return n},setWidth(n){},textAlign(n){return n},xPlus(n,e){return n+e},leftForLtr(n,e){return n}}};function Ee(n,e,t){return n?Lu(e,t):Fu()}function ka(n,e){let t,i;(e==="ltr"||e==="rtl")&&(t=n.canvas.style,i=[t.getPropertyValue("direction"),t.getPropertyPriority("direction")],t.setProperty("direction",e,"important"),n.prevTextDirection=i)}function Ca(n,e){e!==void 0&&(delete n.prevTextDirection,n.canvas.style.setProperty("direction",e[0],e[1]))}function Oa(n){return n==="angle"?{between:cn,compare:Ih,normalize:Ot}:{between:Jt,compare:(e,t)=>e-t,normalize:e=>e}}function Oo({start:n,end:e,count:t,loop:i,style:s}){return{start:n%t,end:e%t,loop:i&&(e-n+1)%t===0,style:s}}function Iu(n,e,t){const{property:i,start:s,end:o}=t,{between:r,normalize:a}=Oa(i),l=e.length;let{start:c,end:h,loop:u}=n,d,f;if(u){for(c+=l,h+=l,d=0,f=l;d<f&&r(a(e[c%l][i]),s,o);++d)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:u,style:n.style}}function Pa(n,e,t){if(!t)return[n];const{property:i,start:s,end:o}=t,r=e.length,{compare:a,between:l,normalize:c}=Oa(i),{start:h,end:u,loop:d,style:f}=Iu(n,e,t),p=[];let m=!1,x=null,y,v,S;const k=()=>l(s,S,y)&&a(s,S)!==0,D=()=>a(o,y)===0||l(o,S,y),C=()=>m||k(),O=()=>!m||D();for(let P=h,F=h;P<=u;++P)v=e[P%r],!v.skip&&(y=c(v[i]),y!==S&&(m=l(y,s,o),x===null&&C()&&(x=a(y,s)===0?P:F),x!==null&&O()&&(p.push(Oo({start:x,end:P,loop:d,count:r,style:f})),x=null),F=P,S=y));return x!==null&&p.push(Oo({start:x,end:u,loop:d,count:r,style:f})),p}function Aa(n,e){const t=[],i=n.segments;for(let s=0;s<i.length;s++){const o=Pa(i[s],n.points,e);o.length&&t.push(...o)}return t}function Nu(n,e,t,i){let s=0,o=e-1;if(t&&!i)for(;s<e&&!n[s].skip;)s++;for(;s<e&&n[s].skip;)s++;for(s%=e,t&&(o+=s);o>s&&n[o%e].skip;)o--;return o%=e,{start:s,end:o}}function Bu(n,e,t,i){const s=n.length,o=[];let r=e,a=n[e],l;for(l=e+1;l<=t;++l){const c=n[l%s];c.skip||c.stop?a.skip||(i=!1,o.push({start:e%s,end:(l-1)%s,loop:i}),e=r=c.stop?l:null):(r=l,a.skip&&(e=l)),a=c}return r!==null&&o.push({start:e%s,end:r%s,loop:i}),o}function zu(n,e){const t=n.points,i=n.options.spanGaps,s=t.length;if(!s)return[];const o=!!n._loop,{start:r,end:a}=Nu(t,s,o,i);if(i===!0)return Po(n,[{start:r,end:a,loop:o}],t,e);const l=a<r?a+s:a,c=!!n._fullLoop&&r===0&&a===s-1;return Po(n,Bu(t,r,l,c),t,e)}function Po(n,e,t,i){return!i||!i.setContext||!t?e:Hu(n,e,t,i)}function Hu(n,e,t,i){const s=n._chart.getContext(),o=Ao(n.options),{_datasetIndex:r,options:{spanGaps:a}}=n,l=t.length,c=[];let h=o,u=e[0].start,d=u;function f(p,m,x,y){const v=a?-1:1;if(p!==m){for(p+=l;t[p%l].skip;)p-=v;for(;t[m%l].skip;)m+=v;p%l!==m%l&&(c.push({start:p%l,end:m%l,loop:x,style:y}),h=y,u=m%l)}}for(const p of e){u=a?u:p.start;let m=t[u%l],x;for(d=u+1;d<=p.end;d++){const y=t[d%l];x=Ao(i.setContext(ce(s,{type:"segment",p0:m,p1:y,p0DataIndex:(d-1)%l,p1DataIndex:d%l,datasetIndex:r}))),ju(x,h)&&f(u,d-1,p.loop,h),m=y,h=x}u<d-1&&f(u,d-1,p.loop,h)}return c}function Ao(n){return{backgroundColor:n.backgroundColor,borderCapStyle:n.borderCapStyle,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderJoinStyle:n.borderJoinStyle,borderWidth:n.borderWidth,borderColor:n.borderColor}}function ju(n,e){if(!e)return!1;const t=[],i=function(s,o){return Ps(o)?(t.includes(o)||t.push(o),t.indexOf(o)):o};return JSON.stringify(n,i)!==JSON.stringify(e,i)}/*!
 * Chart.js v4.4.7
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */class Wu{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,t,i,s){const o=t.listeners[s],r=t.duration;o.forEach(a=>a({chart:e,initial:t.initial,numSteps:r,currentStep:Math.min(i-t.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=fa.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let t=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const o=i.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(e),a=!0):(o[r]=o[o.length-1],o.pop());a&&(s.draw(),this._notify(s,i,e,"progress")),o.length||(i.running=!1,this._notify(s,i,e,"complete"),i.initial=!1),t+=o.length}),this._lastDate=e,t===0&&(this._running=!1)}_getAnims(e){const t=this._charts;let i=t.get(e);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(e,i)),i}listen(e,t,i){this._getAnims(e).listeners[t].push(i)}add(e,t){!t||!t.length||this._getAnims(e).items.push(...t)}has(e){return this._getAnims(e).items.length>0}start(e){const t=this._charts.get(e);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(e){if(!this._running)return!1;const t=this._charts.get(e);return!(!t||!t.running||!t.items.length)}stop(e){const t=this._charts.get(e);if(!t||!t.items.length)return;const i=t.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();t.items=[],this._notify(e,t,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var $t=new Wu;const Eo="transparent",Vu={boolean(n,e,t){return t>.5?e:n},color(n,e,t){const i=vo(n||Eo),s=i.valid&&vo(e||Eo);return s&&s.valid?s.mix(i,t).hexString():e},number(n,e,t){return n+(e-n)*t}};class Yu{constructor(e,t,i,s){const o=t[i];s=qe([e.to,s,o,e.from]);const r=qe([e.from,o,s]);this._active=!0,this._fn=e.fn||Vu[e.type||typeof r],this._easing=tn[e.easing]||tn.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=t,this._prop=i,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(e,t,i){if(this._active){this._notify(!1);const s=this._target[this._prop],o=i-this._start,r=this._duration-o;this._start=i,this._duration=Math.floor(Math.max(r,e.duration)),this._total+=o,this._loop=!!e.loop,this._to=qe([e.to,t,s,e.from]),this._from=qe([e.from,s,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const t=e-this._start,i=this._duration,s=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||t<i),!this._active){this._target[s]=a,this._notify(!0);return}if(t<0){this._target[s]=o;return}l=t/i%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(o,a,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((t,i)=>{e.push({res:t,rej:i})})}_notify(e){const t=e?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][t]()}}class Ea{constructor(e,t){this._chart=e,this._properties=new Map,this.configure(t)}configure(e){if(!Y(e))return;const t=Object.keys(nt.animation),i=this._properties;Object.getOwnPropertyNames(e).forEach(s=>{const o=e[s];if(!Y(o))return;const r={};for(const a of t)r[a]=o[a];(et(o.properties)&&o.properties||[s]).forEach(a=>{(a===s||!i.has(a))&&i.set(a,r)})})}_animateOptions(e,t){const i=t.options,s=$u(e,i);if(!s)return[];const o=this._createAnimations(s,i);return i.$shared&&Uu(e.options.$animations,i).then(()=>{e.options=i},()=>{}),o}_createAnimations(e,t){const i=this._properties,s=[],o=e.$animations||(e.$animations={}),r=Object.keys(t),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(e,t));continue}const h=t[c];let u=o[c];const d=i.get(c);if(u)if(d&&u.active()){u.update(d,h,a);continue}else u.cancel();if(!d||!d.duration){e[c]=h;continue}o[c]=u=new Yu(d,e,c,h),s.push(u)}return s}update(e,t){if(this._properties.size===0){Object.assign(e,t);return}const i=this._createAnimations(e,t);if(i.length)return $t.add(this._chart,i),!0}}function Uu(n,e){const t=[],i=Object.keys(e);for(let s=0;s<i.length;s++){const o=n[i[s]];o&&o.active()&&t.push(o.wait())}return Promise.all(t)}function $u(n,e){if(!e)return;let t=n.options;if(!t){n.options=e;return}return t.$shared&&(n.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t}function To(n,e){const t=n&&n.options||{},i=t.reverse,s=t.min===void 0?e:0,o=t.max===void 0?e:0;return{start:i?o:s,end:i?s:o}}function qu(n,e,t){if(t===!1)return!1;const i=To(n,t),s=To(e,t);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function Xu(n){let e,t,i,s;return Y(n)?(e=n.top,t=n.right,i=n.bottom,s=n.left):e=t=i=s=n,{top:e,right:t,bottom:i,left:s,disabled:n===!1}}function Ta(n,e){const t=[],i=n._getSortedDatasetMetas(e);let s,o;for(s=0,o=i.length;s<o;++s)t.push(i[s].index);return t}function Ro(n,e,t,i={}){const s=n.keys,o=i.mode==="single";let r,a,l,c;if(e===null)return;let h=!1;for(r=0,a=s.length;r<a;++r){if(l=+s[r],l===t){if(h=!0,i.all)continue;break}c=n.values[l],ot(c)&&(o||e===0||Wt(e)===Wt(c))&&(e+=c)}return!h&&!i.all?0:e}function Ku(n,e){const{iScale:t,vScale:i}=e,s=t.axis==="x"?"x":"y",o=i.axis==="x"?"x":"y",r=Object.keys(n),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[s]:h,[o]:n[h]};return a}function Li(n,e){const t=n&&n.options.stacked;return t||t===void 0&&e.stack!==void 0}function Ju(n,e,t){return`${n.id}.${e.id}.${t.stack||t.type}`}function Gu(n){const{min:e,max:t,minDefined:i,maxDefined:s}=n.getUserBounds();return{min:i?e:Number.NEGATIVE_INFINITY,max:s?t:Number.POSITIVE_INFINITY}}function Zu(n,e,t){const i=n[e]||(n[e]={});return i[t]||(i[t]={})}function Lo(n,e,t,i){for(const s of e.getMatchingVisibleMetas(i).reverse()){const o=n[s.index];if(t&&o>0||!t&&o<0)return s.index}return null}function Fo(n,e){const{chart:t,_cachedMeta:i}=n,s=t._stacks||(t._stacks={}),{iScale:o,vScale:r,index:a}=i,l=o.axis,c=r.axis,h=Ju(o,r,i),u=e.length;let d;for(let f=0;f<u;++f){const p=e[f],{[l]:m,[c]:x}=p,y=p._stacks||(p._stacks={});d=y[c]=Zu(s,h,m),d[a]=x,d._top=Lo(d,r,!0,i.type),d._bottom=Lo(d,r,!1,i.type);const v=d._visualValues||(d._visualValues={});v[a]=x}}function Fi(n,e){const t=n.scales;return Object.keys(t).filter(i=>t[i].axis===e).shift()}function Qu(n,e){return ce(n,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function td(n,e,t){return ce(n,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:t,index:e,mode:"default",type:"data"})}function je(n,e){const t=n.controller.index,i=n.vScale&&n.vScale.axis;if(i){e=e||n._parsed;for(const s of e){const o=s._stacks;if(!o||o[i]===void 0||o[i][t]===void 0)return;delete o[i][t],o[i]._visualValues!==void 0&&o[i]._visualValues[t]!==void 0&&delete o[i]._visualValues[t]}}}const Ii=n=>n==="reset"||n==="none",Io=(n,e)=>e?n:Object.assign({},n),ed=(n,e,t)=>n&&!e.hidden&&e._stacked&&{keys:Ta(t,!0),values:null};class Ft{constructor(e,t){this.chart=e,this._ctx=e.ctx,this.index=t,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Li(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&je(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,t=this._cachedMeta,i=this.getDataset(),s=(u,d,f,p)=>u==="x"?d:u==="r"?p:f,o=t.xAxisID=z(i.xAxisID,Fi(e,"x")),r=t.yAxisID=z(i.yAxisID,Fi(e,"y")),a=t.rAxisID=z(i.rAxisID,Fi(e,"r")),l=t.indexAxis,c=t.iAxisID=s(l,o,r,a),h=t.vAxisID=s(l,r,o,a);t.xScale=this.getScaleForId(o),t.yScale=this.getScaleForId(r),t.rScale=this.getScaleForId(a),t.iScale=this.getScaleForId(c),t.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const t=this._cachedMeta;return e===t.iScale?t.vScale:t.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&xo(this._data,this),e._stacked&&je(e)}_dataCheck(){const e=this.getDataset(),t=e.data||(e.data=[]),i=this._data;if(Y(t)){const s=this._cachedMeta;this._data=Ku(t,s)}else if(i!==t){if(i){xo(i,this);const s=this._cachedMeta;je(s),s._parsed=[]}t&&Object.isExtensible(t)&&Hh(t,this),this._syncList=[],this._data=t}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const t=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const o=t._stacked;t._stacked=Li(t.vScale,t),t.stack!==i.stack&&(s=!0,je(t),t.stack=i.stack),this._resyncElements(e),(s||o!==t._stacked)&&(Fo(this,t._parsed),t._stacked=Li(t.vScale,t))}configure(){const e=this.chart.config,t=e.datasetScopeKeys(this._type),i=e.getOptionScopes(this.getDataset(),t,!0);this.options=e.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,t){const{_cachedMeta:i,_data:s}=this,{iScale:o,_stacked:r}=i,a=o.axis;let l=e===0&&t===s.length?!0:i._sorted,c=e>0&&i._parsed[e-1],h,u,d;if(this._parsing===!1)i._parsed=s,i._sorted=!0,d=s;else{et(s[e])?d=this.parseArrayData(i,s,e,t):Y(s[e])?d=this.parseObjectData(i,s,e,t):d=this.parsePrimitiveData(i,s,e,t);const f=()=>u[a]===null||c&&u[a]<c[a];for(h=0;h<t;++h)i._parsed[h+e]=u=d[h],l&&(f()&&(l=!1),c=u);i._sorted=l}r&&Fo(this,d)}parsePrimitiveData(e,t,i,s){const{iScale:o,vScale:r}=e,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,u=new Array(s);let d,f,p;for(d=0,f=s;d<f;++d)p=d+i,u[d]={[a]:h||o.parse(c[p],p),[l]:r.parse(t[p],p)};return u}parseArrayData(e,t,i,s){const{xScale:o,yScale:r}=e,a=new Array(s);let l,c,h,u;for(l=0,c=s;l<c;++l)h=l+i,u=t[h],a[l]={x:o.parse(u[0],h),y:r.parse(u[1],h)};return a}parseObjectData(e,t,i,s){const{xScale:o,yScale:r}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s);let h,u,d,f;for(h=0,u=s;h<u;++h)d=h+i,f=t[d],c[h]={x:o.parse(ae(f,a),d),y:r.parse(ae(f,l),d)};return c}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,t,i){const s=this.chart,o=this._cachedMeta,r=t[e.axis],a={keys:Ta(s,!0),values:t._stacks[e.axis]._visualValues};return Ro(a,r,o.index,{mode:i})}updateRangeFromParsed(e,t,i,s){const o=i[t.axis];let r=o===null?NaN:o;const a=s&&i._stacks[t.axis];s&&a&&(s.values=a,r=Ro(s,o,this._cachedMeta.index)),e.min=Math.min(e.min,r),e.max=Math.max(e.max,r)}getMinMax(e,t){const i=this._cachedMeta,s=i._parsed,o=i._sorted&&e===i.iScale,r=s.length,a=this._getOtherScale(e),l=ed(t,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:u}=Gu(a);let d,f;function p(){f=s[d];const m=f[a.axis];return!ot(f[e.axis])||h>m||u<m}for(d=0;d<r&&!(!p()&&(this.updateRangeFromParsed(c,e,f,l),o));++d);if(o){for(d=r-1;d>=0;--d)if(!p()){this.updateRangeFromParsed(c,e,f,l);break}}return c}getAllParsedValues(e){const t=this._cachedMeta._parsed,i=[];let s,o,r;for(s=0,o=t.length;s<o;++s)r=t[s][e.axis],ot(r)&&i.push(r);return i}getMaxOverflow(){return!1}getLabelAndValue(e){const t=this._cachedMeta,i=t.iScale,s=t.vScale,o=this.getParsed(e);return{label:i?""+i.getLabelForValue(o[i.axis]):"",value:s?""+s.getLabelForValue(o[s.axis]):""}}_update(e){const t=this._cachedMeta;this.update(e||"default"),t._clip=Xu(z(this.options.clip,qu(t.xScale,t.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,t=this.chart,i=this._cachedMeta,s=i.data||[],o=t.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop;let h;for(i.dataset&&i.dataset.draw(e,o,a,l),h=a;h<a+l;++h){const u=s[h];u.hidden||(u.active&&c?r.push(u):u.draw(e,o))}for(h=0;h<r.length;++h)r[h].draw(e,o)}getStyle(e,t){const i=t?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(e||0,i)}getContext(e,t,i){const s=this.getDataset();let o;if(e>=0&&e<this._cachedMeta.data.length){const r=this._cachedMeta.data[e];o=r.$context||(r.$context=td(this.getContext(),e,r)),o.parsed=this.getParsed(e),o.raw=s.data[e],o.index=o.dataIndex=e}else o=this.$context||(this.$context=Qu(this.chart.getContext(),this.index)),o.dataset=s,o.index=o.datasetIndex=this.index;return o.active=!!t,o.mode=i,o}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,t){return this._resolveElementOptions(this.dataElementType.id,t,e)}_resolveElementOptions(e,t="default",i){const s=t==="active",o=this._cachedDataOpts,r=e+"-"+t,a=o[r],l=this.enableOptionSharing&&ln(i);if(a)return Io(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,e),u=s?[`${e}Hover`,"hover",e,""]:[e,""],d=c.getOptionScopes(this.getDataset(),h),f=Object.keys(nt.elements[e]),p=()=>this.getContext(i,s,t),m=c.resolveNamedOptions(d,f,p,u);return m.$shared&&(m.$shared=l,o[r]=Object.freeze(Io(m,l))),m}_resolveAnimations(e,t,i){const s=this.chart,o=this._cachedDataOpts,r=`animation-${t}`,a=o[r];if(a)return a;let l;if(s.options.animation!==!1){const h=this.chart.config,u=h.datasetAnimationScopeKeys(this._type,t),d=h.getOptionScopes(this.getDataset(),u);l=h.createResolver(d,this.getContext(e,i,t))}const c=new Ea(s,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,t){return!t||Ii(e)||this.chart._animationsDisabled}_getSharedOptions(e,t){const i=this.resolveDataElementOptions(e,t),s=this._sharedOptions,o=this.getSharedOptions(i),r=this.includeOptions(t,o)||o!==s;return this.updateSharedOptions(o,t,i),{sharedOptions:o,includeOptions:r}}updateElement(e,t,i,s){Ii(s)?Object.assign(e,i):this._resolveAnimations(t,s).update(e,i)}updateSharedOptions(e,t,i){e&&!Ii(t)&&this._resolveAnimations(void 0,t).update(e,i)}_setStyle(e,t,i,s){e.active=s;const o=this.getStyle(t,s);this._resolveAnimations(t,i,s).update(e,{options:!s&&this.getSharedOptions(o)||o})}removeHoverStyle(e,t,i){this._setStyle(e,i,"active",!1)}setHoverStyle(e,t,i){this._setStyle(e,i,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const t=this._data,i=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const s=i.length,o=t.length,r=Math.min(o,s);r&&this.parse(0,r),o>s?this._insertElements(s,o-s,e):o<s&&this._removeElements(o,s-o)}_insertElements(e,t,i=!0){const s=this._cachedMeta,o=s.data,r=e+t;let a;const l=c=>{for(c.length+=t,a=c.length-1;a>=r;a--)c[a]=c[a-t]};for(l(o),a=e;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(e,t),i&&this.updateElements(o,e,t,"reset")}updateElements(e,t,i,s){}_removeElements(e,t){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(e,t);i._stacked&&je(i,s)}i.data.splice(e,t)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[t,i,s]=e;this[t](i,s)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,t){t&&this._sync(["_removeElements",e,t]);const i=arguments.length-2;i&&this._sync(["_insertElements",e,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}E(Ft,"defaults",{}),E(Ft,"datasetElementType",null),E(Ft,"dataElementType",null);function nd(n,e){if(!n._cache.$bar){const t=n.getMatchingVisibleMetas(e);let i=[];for(let s=0,o=t.length;s<o;s++)i=i.concat(t[s].controller.getAllParsedValues(n));n._cache.$bar=da(i.sort((s,o)=>s-o))}return n._cache.$bar}function id(n){const e=n.iScale,t=nd(e,n.type);let i=e._length,s,o,r,a;const l=()=>{r===32767||r===-32768||(ln(a)&&(i=Math.min(i,Math.abs(r-a)||i)),a=r)};for(s=0,o=t.length;s<o;++s)r=e.getPixelForValue(t[s]),l();for(a=void 0,s=0,o=e.ticks.length;s<o;++s)r=e.getPixelForTick(s),l();return i}function sd(n,e,t,i){const s=t.barThickness;let o,r;return U(s)?(o=e.min*t.categoryPercentage,r=t.barPercentage):(o=s*i,r=1),{chunk:o/i,ratio:r,start:e.pixels[n]-o/2}}function od(n,e,t,i){const s=e.pixels,o=s[n];let r=n>0?s[n-1]:null,a=n<s.length-1?s[n+1]:null;const l=t.categoryPercentage;r===null&&(r=o-(a===null?e.end-e.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/i,ratio:t.barPercentage,start:c}}function rd(n,e,t,i){const s=t.parse(n[0],i),o=t.parse(n[1],i),r=Math.min(s,o),a=Math.max(s,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),e[t.axis]=c,e._custom={barStart:l,barEnd:c,start:s,end:o,min:r,max:a}}function Ra(n,e,t,i){return et(n)?rd(n,e,t,i):e[t.axis]=t.parse(n,i),e}function No(n,e,t,i){const s=n.iScale,o=n.vScale,r=s.getLabels(),a=s===o,l=[];let c,h,u,d;for(c=t,h=t+i;c<h;++c)d=e[c],u={},u[s.axis]=a||s.parse(r[c],c),l.push(Ra(d,u,o,c));return l}function Ni(n){return n&&n.barStart!==void 0&&n.barEnd!==void 0}function ad(n,e,t){return n!==0?Wt(n):(e.isHorizontal()?1:-1)*(e.min>=t?1:-1)}function ld(n){let e,t,i,s,o;return n.horizontal?(e=n.base>n.x,t="left",i="right"):(e=n.base<n.y,t="bottom",i="top"),e?(s="end",o="start"):(s="start",o="end"),{start:t,end:i,reverse:e,top:s,bottom:o}}function cd(n,e,t,i){let s=e.borderSkipped;const o={};if(!s){n.borderSkipped=o;return}if(s===!0){n.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=ld(n);s==="middle"&&t&&(n.enableBorderRadius=!0,(t._top||0)===i?s=c:(t._bottom||0)===i?s=h:(o[Bo(h,r,a,l)]=!0,s=c)),o[Bo(s,r,a,l)]=!0,n.borderSkipped=o}function Bo(n,e,t,i){return i?(n=hd(n,e,t),n=zo(n,t,e)):n=zo(n,e,t),n}function hd(n,e,t){return n===e?t:n===t?e:n}function zo(n,e,t){return n==="start"?e:n==="end"?t:n}function ud(n,{inflateAmount:e},t){n.inflateAmount=e==="auto"?t===1?.33:0:e}class Un extends Ft{parsePrimitiveData(e,t,i,s){return No(e,t,i,s)}parseArrayData(e,t,i,s){return No(e,t,i,s)}parseObjectData(e,t,i,s){const{iScale:o,vScale:r}=e,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,u=[];let d,f,p,m;for(d=i,f=i+s;d<f;++d)m=t[d],p={},p[o.axis]=o.parse(ae(m,c),d),u.push(Ra(ae(m,h),p,r,d));return u}updateRangeFromParsed(e,t,i,s){super.updateRangeFromParsed(e,t,i,s);const o=i._custom;o&&t===this._cachedMeta.vScale&&(e.min=Math.min(e.min,o.min),e.max=Math.max(e.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const t=this._cachedMeta,{iScale:i,vScale:s}=t,o=this.getParsed(e),r=o._custom,a=Ni(r)?"["+r.start+", "+r.end+"]":""+s.getLabelForValue(o[s.axis]);return{label:""+i.getLabelForValue(o[i.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const t=this._cachedMeta;this.updateElements(t.data,0,t.data.length,e)}updateElements(e,t,i,s){const o=s==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:u,includeOptions:d}=this._getSharedOptions(t,s);for(let f=t;f<t+i;f++){const p=this.getParsed(f),m=o||U(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),x=this._calculateBarIndexPixels(f,h),y=(p._stacks||{})[a.axis],v={horizontal:c,base:m.base,enableBorderRadius:!y||Ni(p._custom)||r===y._top||r===y._bottom,x:c?m.head:x.center,y:c?x.center:m.head,height:c?x.size:Math.abs(m.size),width:c?Math.abs(m.size):x.size};d&&(v.options=u||this.resolveDataElementOptions(f,e[f].active?"active":s));const S=v.options||e[f].options;cd(v,S,y,r),ud(v,S,h.ratio),this.updateElement(e[f],f,v,s)}}_getStacks(e,t){const{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=i.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(t),l=a&&a[i.axis],c=h=>{const u=h._parsed.find(f=>f[i.axis]===l),d=u&&u[h.vScale.axis];if(U(d)||isNaN(d))return!0};for(const h of s)if(!(t!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===e))break;return r.length||r.push(void 0),r}_getStackCount(e){return this._getStacks(void 0,e).length}_getStackIndex(e,t,i){const s=this._getStacks(e,i),o=t!==void 0?s.indexOf(t):-1;return o===-1?s.length-1:o}_getRuler(){const e=this.options,t=this._cachedMeta,i=t.iScale,s=[];let o,r;for(o=0,r=t.data.length;o<r;++o)s.push(i.getPixelForValue(this.getParsed(o)[i.axis],o));const a=e.barThickness;return{min:a||id(t),pixels:s,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:e.grouped,ratio:a?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:t,_stacked:i,index:s},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(e),c=l._custom,h=Ni(c);let u=l[t.axis],d=0,f=i?this.applyStack(t,l,i):u,p,m;f!==u&&(d=f-u,f=u),h&&(u=c.barStart,f=c.barEnd-c.barStart,u!==0&&Wt(u)!==Wt(c.barEnd)&&(d=0),d+=u);const x=!U(o)&&!h?o:d;let y=t.getPixelForValue(x);if(this.chart.getDataVisibility(e)?p=t.getPixelForValue(d+f):p=y,m=p-y,Math.abs(m)<r){m=ad(m,t,a)*r,u===a&&(y-=m/2);const v=t.getPixelForDecimal(0),S=t.getPixelForDecimal(1),k=Math.min(v,S),D=Math.max(v,S);y=Math.max(Math.min(y,D),k),p=y+m,i&&!h&&(l._stacks[t.axis]._visualValues[s]=t.getValueForPixel(p)-t.getValueForPixel(y))}if(y===t.getPixelForValue(a)){const v=Wt(m)*t.getLineWidthForValue(a)/2;y+=v,m-=v}return{size:m,base:y,head:p,center:p+m/2}}_calculateBarIndexPixels(e,t){const i=t.scale,s=this.options,o=s.skipNull,r=z(s.maxBarThickness,1/0);let a,l;if(t.grouped){const c=o?this._getStackCount(e):t.stackCount,h=s.barThickness==="flex"?od(e,t,s,c):sd(e,t,s,c),u=this._getStackIndex(this.index,this._cachedMeta.stack,o?e:void 0);a=h.start+h.chunk*u+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=i.getPixelForValue(this.getParsed(e)[i.axis],e),l=Math.min(r,t.min*t.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const e=this._cachedMeta,t=e.vScale,i=e.data,s=i.length;let o=0;for(;o<s;++o)this.getParsed(o)[t.axis]!==null&&!i[o].hidden&&i[o].draw(this._ctx)}}E(Un,"id","bar"),E(Un,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),E(Un,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class $n extends Ft{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(e,t,i,s){const o=super.parsePrimitiveData(e,t,i,s);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+i).radius;return o}parseArrayData(e,t,i,s){const o=super.parseArrayData(e,t,i,s);for(let r=0;r<o.length;r++){const a=t[i+r];o[r]._custom=z(a[2],this.resolveDataElementOptions(r+i).radius)}return o}parseObjectData(e,t,i,s){const o=super.parseObjectData(e,t,i,s);for(let r=0;r<o.length;r++){const a=t[i+r];o[r]._custom=z(a&&a.r&&+a.r,this.resolveDataElementOptions(r+i).radius)}return o}getMaxOverflow(){const e=this._cachedMeta.data;let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}getLabelAndValue(e){const t=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:o}=t,r=this.getParsed(e),a=s.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:i[e]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(e){const t=this._cachedMeta.data;this.updateElements(t,0,t.length,e)}updateElements(e,t,i,s){const o=s==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(t,s),h=r.axis,u=a.axis;for(let d=t;d<t+i;d++){const f=e[d],p=!o&&this.getParsed(d),m={},x=m[h]=o?r.getPixelForDecimal(.5):r.getPixelForValue(p[h]),y=m[u]=o?a.getBasePixel():a.getPixelForValue(p[u]);m.skip=isNaN(x)||isNaN(y),c&&(m.options=l||this.resolveDataElementOptions(d,f.active?"active":s),o&&(m.options.radius=0)),this.updateElement(f,d,m,s)}}resolveDataElementOptions(e,t){const i=this.getParsed(e);let s=super.resolveDataElementOptions(e,t);s.$shared&&(s=Object.assign({},s,{$shared:!1}));const o=s.radius;return t!=="active"&&(s.radius=0),s.radius+=z(i&&i._custom,o),s}}E($n,"id","bubble"),E($n,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),E($n,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function dd(n,e,t){let i=1,s=1,o=0,r=0;if(e<Z){const a=n,l=a+e,c=Math.cos(a),h=Math.sin(a),u=Math.cos(l),d=Math.sin(l),f=(S,k,D)=>cn(S,a,l,!0)?1:Math.max(k,k*t,D,D*t),p=(S,k,D)=>cn(S,a,l,!0)?-1:Math.min(k,k*t,D,D*t),m=f(0,c,u),x=f(rt,h,d),y=p(Q,c,u),v=p(Q+rt,h,d);i=(m-y)/2,s=(x-v)/2,o=-(m+y)/2,r=-(x+v)/2}return{ratioX:i,ratioY:s,offsetX:o,offsetY:r}}class _e extends Ft{constructor(e,t){super(e,t),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,t){const i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let o=l=>+i[l];if(Y(i[e])){const{key:l="value"}=this._parsing;o=c=>+ae(i[c],l)}let r,a;for(r=e,a=e+t;r<a;++r)s._parsed[r]=o(r)}}_getRotation(){return Lt(this.options.rotation-90)}_getCircumference(){return Lt(this.options.circumference)}_getRotationExtents(){let e=Z,t=-Z;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,o=s._getRotation(),r=s._getCircumference();e=Math.min(e,o),t=Math.max(t,o+r)}return{rotation:e,circumference:t-e}}update(e){const t=this.chart,{chartArea:i}=t,s=this._cachedMeta,o=s.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-r)/2,0),l=Math.min(kh(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:u}=this._getRotationExtents(),{ratioX:d,ratioY:f,offsetX:p,offsetY:m}=dd(u,h,l),x=(i.width-r)/d,y=(i.height-r)/f,v=Math.max(Math.min(x,y)/2,0),S=aa(this.options.radius,v),k=Math.max(S*l,0),D=(S-k)/this._getVisibleDatasetWeightTotal();this.offsetX=p*S,this.offsetY=m*S,s.total=this.calculateTotal(),this.outerRadius=S-D*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-D*c,0),this.updateElements(o,0,o.length,e)}_circumference(e,t){const i=this.options,s=this._cachedMeta,o=this._getCircumference();return t&&i.animation.animateRotate||!this.chart.getDataVisibility(e)||s._parsed[e]===null||s.data[e].hidden?0:this.calculateCircumference(s._parsed[e]*o/Z)}updateElements(e,t,i,s){const o=s==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,u=(a.top+a.bottom)/2,d=o&&c.animateScale,f=d?0:this.innerRadius,p=d?0:this.outerRadius,{sharedOptions:m,includeOptions:x}=this._getSharedOptions(t,s);let y=this._getRotation(),v;for(v=0;v<t;++v)y+=this._circumference(v,o);for(v=t;v<t+i;++v){const S=this._circumference(v,o),k=e[v],D={x:h+this.offsetX,y:u+this.offsetY,startAngle:y,endAngle:y+S,circumference:S,outerRadius:p,innerRadius:f};x&&(D.options=m||this.resolveDataElementOptions(v,k.active?"active":s)),y+=S,this.updateElement(k,v,D,s)}}calculateTotal(){const e=this._cachedMeta,t=e.data;let i=0,s;for(s=0;s<t.length;s++){const o=e._parsed[s];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(s)&&!t[s].hidden&&(i+=Math.abs(o))}return i}calculateCircumference(e){const t=this._cachedMeta.total;return t>0&&!isNaN(e)?Z*(Math.abs(e)/t):0}getLabelAndValue(e){const t=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=xn(t._parsed[e],i.options.locale);return{label:s[e]||"",value:o}}getMaxBorderWidth(e){let t=0;const i=this.chart;let s,o,r,a,l;if(!e){for(s=0,o=i.data.datasets.length;s<o;++s)if(i.isDatasetVisible(s)){r=i.getDatasetMeta(s),e=r.data,a=r.controller;break}}if(!e)return 0;for(s=0,o=e.length;s<o;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(t=Math.max(t,l.borderWidth||0,l.hoverBorderWidth||0));return t}getMaxOffset(e){let t=0;for(let i=0,s=e.length;i<s;++i){const o=this.resolveDataElementOptions(i);t=Math.max(t,o.offset||0,o.hoverOffset||0)}return t}_getRingWeightOffset(e){let t=0;for(let i=0;i<e;++i)this.chart.isDatasetVisible(i)&&(t+=this._getRingWeight(i));return t}_getRingWeight(e){return Math.max(z(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}E(_e,"id","doughnut"),E(_e,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),E(_e,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),E(_e,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const t=e.data;if(t.labels.length&&t.datasets.length){const{labels:{pointStyle:i,color:s}}=e.legend.options;return t.labels.map((o,r)=>{const l=e.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!e.getDataVisibility(r),index:r}})}return[]}},onClick(e,t,i){i.chart.toggleDataVisibility(t.index),i.chart.update()}}}});class qn extends Ft{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const t=this._cachedMeta,{dataset:i,data:s=[],_dataset:o}=t,r=this.chart._animationsDisabled;let{start:a,count:l}=pa(t,s,r);this._drawStart=a,this._drawCount=l,ma(t)&&(a=0,l=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!o._decimated,i.points=s;const c=this.resolveDatasetElementOptions(e);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(i,void 0,{animated:!r,options:c},e),this.updateElements(s,a,l,e)}updateElements(e,t,i,s){const o=s==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:u}=this._getSharedOptions(t,s),d=r.axis,f=a.axis,{spanGaps:p,segment:m}=this.options,x=Le(p)?p:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||o||s==="none",v=t+i,S=e.length;let k=t>0&&this.getParsed(t-1);for(let D=0;D<S;++D){const C=e[D],O=y?C:{};if(D<t||D>=v){O.skip=!0;continue}const P=this.getParsed(D),F=U(P[f]),N=O[d]=r.getPixelForValue(P[d],D),H=O[f]=o||F?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,P,l):P[f],D);O.skip=isNaN(N)||isNaN(H)||F,O.stop=D>0&&Math.abs(P[d]-k[d])>x,m&&(O.parsed=P,O.raw=c.data[D]),u&&(O.options=h||this.resolveDataElementOptions(D,C.active?"active":s)),y||this.updateElement(C,D,O,s),k=P}}getMaxOverflow(){const e=this._cachedMeta,t=e.dataset,i=t.options&&t.options.borderWidth||0,s=e.data||[];if(!s.length)return i;const o=s[0].size(this.resolveDataElementOptions(0)),r=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,o,r)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}E(qn,"id","line"),E(qn,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),E(qn,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class nn extends Ft{constructor(e,t){super(e,t),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(e){const t=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=xn(t._parsed[e].r,i.options.locale);return{label:s[e]||"",value:o}}parseObjectData(e,t,i,s){return Da.bind(this)(e,t,i,s)}update(e){const t=this._cachedMeta.data;this._updateRadius(),this.updateElements(t,0,t.length,e)}getMinMax(){const e=this._cachedMeta,t={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return e.data.forEach((i,s)=>{const o=this.getParsed(s).r;!isNaN(o)&&this.chart.getDataVisibility(s)&&(o<t.min&&(t.min=o),o>t.max&&(t.max=o))}),t}_updateRadius(){const e=this.chart,t=e.chartArea,i=e.options,s=Math.min(t.right-t.left,t.bottom-t.top),o=Math.max(s/2,0),r=Math.max(i.cutoutPercentage?o/100*i.cutoutPercentage:1,0),a=(o-r)/e.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(e,t,i,s){const o=s==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,u=c.yCenter,d=c.getIndexAngle(0)-.5*Q;let f=d,p;const m=360/this.countVisibleElements();for(p=0;p<t;++p)f+=this._computeAngle(p,s,m);for(p=t;p<t+i;p++){const x=e[p];let y=f,v=f+this._computeAngle(p,s,m),S=r.getDataVisibility(p)?c.getDistanceFromCenterForValue(this.getParsed(p).r):0;f=v,o&&(l.animateScale&&(S=0),l.animateRotate&&(y=v=d));const k={x:h,y:u,innerRadius:0,outerRadius:S,startAngle:y,endAngle:v,options:this.resolveDataElementOptions(p,x.active?"active":s)};this.updateElement(x,p,k,s)}}countVisibleElements(){const e=this._cachedMeta;let t=0;return e.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&t++}),t}_computeAngle(e,t,i){return this.chart.getDataVisibility(e)?Lt(this.resolveDataElementOptions(e,t).angle||i):0}}E(nn,"id","polarArea"),E(nn,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),E(nn,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const t=e.data;if(t.labels.length&&t.datasets.length){const{labels:{pointStyle:i,color:s}}=e.legend.options;return t.labels.map((o,r)=>{const l=e.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!e.getDataVisibility(r),index:r}})}return[]}},onClick(e,t,i){i.chart.toggleDataVisibility(t.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class os extends _e{}E(os,"id","pie"),E(os,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Xn extends Ft{getLabelAndValue(e){const t=this._cachedMeta.vScale,i=this.getParsed(e);return{label:t.getLabels()[e],value:""+t.getLabelForValue(i[t.axis])}}parseObjectData(e,t,i,s){return Da.bind(this)(e,t,i,s)}update(e){const t=this._cachedMeta,i=t.dataset,s=t.data||[],o=t.iScale.getLabels();if(i.points=s,e!=="resize"){const r=this.resolveDatasetElementOptions(e);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===s.length,options:r};this.updateElement(i,void 0,a,e)}this.updateElements(s,0,s.length,e)}updateElements(e,t,i,s){const o=this._cachedMeta.rScale,r=s==="reset";for(let a=t;a<t+i;a++){const l=e[a],c=this.resolveDataElementOptions(a,l.active?"active":s),h=o.getPointPositionForValue(a,this.getParsed(a).r),u=r?o.xCenter:h.x,d=r?o.yCenter:h.y,f={x:u,y:d,angle:h.angle,skip:isNaN(u)||isNaN(d),options:c};this.updateElement(l,a,f,s)}}}E(Xn,"id","radar"),E(Xn,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),E(Xn,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Kn extends Ft{getLabelAndValue(e){const t=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:o}=t,r=this.getParsed(e),a=s.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:i[e]||"",value:"("+a+", "+l+")"}}update(e){const t=this._cachedMeta,{data:i=[]}=t,s=this.chart._animationsDisabled;let{start:o,count:r}=pa(t,i,s);if(this._drawStart=o,this._drawCount=r,ma(t)&&(o=0,r=i.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=t;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=i;const c=this.resolveDatasetElementOptions(e);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!s,options:c},e)}else this.datasetElementType&&(delete t.dataset,this.datasetElementType=!1);this.updateElements(i,o,r,e)}addElements(){const{showLine:e}=this.options;!this.datasetElementType&&e&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(e,t,i,s){const o=s==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(t,s),u=this.getSharedOptions(h),d=this.includeOptions(s,u),f=r.axis,p=a.axis,{spanGaps:m,segment:x}=this.options,y=Le(m)?m:Number.POSITIVE_INFINITY,v=this.chart._animationsDisabled||o||s==="none";let S=t>0&&this.getParsed(t-1);for(let k=t;k<t+i;++k){const D=e[k],C=this.getParsed(k),O=v?D:{},P=U(C[p]),F=O[f]=r.getPixelForValue(C[f],k),N=O[p]=o||P?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,C,l):C[p],k);O.skip=isNaN(F)||isNaN(N)||P,O.stop=k>0&&Math.abs(C[f]-S[f])>y,x&&(O.parsed=C,O.raw=c.data[k]),d&&(O.options=u||this.resolveDataElementOptions(k,D.active?"active":s)),v||this.updateElement(D,k,O,s),S=C}this.updateSharedOptions(u,s,h)}getMaxOverflow(){const e=this._cachedMeta,t=e.data||[];if(!this.options.showLine){let a=0;for(let l=t.length-1;l>=0;--l)a=Math.max(a,t[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const i=e.dataset,s=i.options&&i.options.borderWidth||0;if(!t.length)return s;const o=t[0].size(this.resolveDataElementOptions(0)),r=t[t.length-1].size(this.resolveDataElementOptions(t.length-1));return Math.max(s,o,r)/2}}E(Kn,"id","scatter"),E(Kn,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),E(Kn,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var fd=Object.freeze({__proto__:null,BarController:Un,BubbleController:$n,DoughnutController:_e,LineController:qn,PieController:os,PolarAreaController:nn,RadarController:Xn,ScatterController:Kn});function ge(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Is{constructor(e){E(this,"options");this.options=e||{}}static override(e){Object.assign(Is.prototype,e)}init(){}formats(){return ge()}parse(){return ge()}format(){return ge()}add(){return ge()}diff(){return ge()}startOf(){return ge()}endOf(){return ge()}}var gd={_date:Is};function pd(n,e,t,i){const{controller:s,data:o,_sorted:r}=n,a=s._cachedMeta.iScale;if(a&&e===a.axis&&e!=="r"&&r&&o.length){const l=a._reversePixels?Bh:Gt;if(i){if(s._sharedOptions){const c=o[0],h=typeof c.getRange=="function"&&c.getRange(e);if(h){const u=l(o,e,t-h),d=l(o,e,t+h);return{lo:u.lo,hi:d.hi}}}}else return l(o,e,t)}return{lo:0,hi:o.length-1}}function yn(n,e,t,i,s){const o=n.getSortedVisibleDatasetMetas(),r=t[e];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:u,hi:d}=pd(o[a],e,r,s);for(let f=u;f<=d;++f){const p=h[f];p.skip||i(p,c,f)}}}function md(n){const e=n.indexOf("x")!==-1,t=n.indexOf("y")!==-1;return function(i,s){const o=e?Math.abs(i.x-s.x):0,r=t?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Bi(n,e,t,i,s){const o=[];return!s&&!n.isPointInArea(e)||yn(n,t,e,function(a,l,c){!s&&!Zt(a,n.chartArea,0)||a.inRange(e.x,e.y,i)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function bd(n,e,t,i){let s=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],i),{angle:u}=ha(r,{x:e.x,y:e.y});cn(u,c,h)&&s.push({element:r,datasetIndex:a,index:l})}return yn(n,t,e,o),s}function xd(n,e,t,i,s,o){let r=[];const a=md(t);let l=Number.POSITIVE_INFINITY;function c(h,u,d){const f=h.inRange(e.x,e.y,s);if(i&&!f)return;const p=h.getCenterPoint(s);if(!(!!o||n.isPointInArea(p))&&!f)return;const x=a(e,p);x<l?(r=[{element:h,datasetIndex:u,index:d}],l=x):x===l&&r.push({element:h,datasetIndex:u,index:d})}return yn(n,t,e,c),r}function zi(n,e,t,i,s,o){return!o&&!n.isPointInArea(e)?[]:t==="r"&&!i?bd(n,e,t,s):xd(n,e,t,i,s,o)}function Ho(n,e,t,i,s){const o=[],r=t==="x"?"inXRange":"inYRange";let a=!1;return yn(n,t,e,(l,c,h)=>{l[r]&&l[r](e[t],s)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(e.x,e.y,s))}),i&&!a?[]:o}var yd={evaluateInteractionItems:yn,modes:{index(n,e,t,i){const s=me(e,n),o=t.axis||"x",r=t.includeInvisible||!1,a=t.intersect?Bi(n,s,o,i,r):zi(n,s,o,!1,i,r),l=[];return a.length?(n.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,u=c.data[h];u&&!u.skip&&l.push({element:u,datasetIndex:c.index,index:h})}),l):[]},dataset(n,e,t,i){const s=me(e,n),o=t.axis||"xy",r=t.includeInvisible||!1;let a=t.intersect?Bi(n,s,o,i,r):zi(n,s,o,!1,i,r);if(a.length>0){const l=a[0].datasetIndex,c=n.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(n,e,t,i){const s=me(e,n),o=t.axis||"xy",r=t.includeInvisible||!1;return Bi(n,s,o,i,r)},nearest(n,e,t,i){const s=me(e,n),o=t.axis||"xy",r=t.includeInvisible||!1;return zi(n,s,o,t.intersect,i,r)},x(n,e,t,i){const s=me(e,n);return Ho(n,s,"x",t.intersect,i)},y(n,e,t,i){const s=me(e,n);return Ho(n,s,"y",t.intersect,i)}}};const La=["left","top","right","bottom"];function We(n,e){return n.filter(t=>t.pos===e)}function jo(n,e){return n.filter(t=>La.indexOf(t.pos)===-1&&t.box.axis===e)}function Ve(n,e){return n.sort((t,i)=>{const s=e?i:t,o=e?t:i;return s.weight===o.weight?s.index-o.index:s.weight-o.weight})}function _d(n){const e=[];let t,i,s,o,r,a;for(t=0,i=(n||[]).length;t<i;++t)s=n[t],{position:o,options:{stack:r,stackWeight:a=1}}=s,e.push({index:t,box:s,pos:o,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&o+r,stackWeight:a});return e}function vd(n){const e={};for(const t of n){const{stack:i,pos:s,stackWeight:o}=t;if(!i||!La.includes(s))continue;const r=e[i]||(e[i]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return e}function wd(n,e){const t=vd(n),{vBoxMaxWidth:i,hBoxMaxHeight:s}=e;let o,r,a;for(o=0,r=n.length;o<r;++o){a=n[o];const{fullSize:l}=a.box,c=t[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*i:l&&e.availableWidth,a.height=s):(a.width=i,a.height=h?h*s:l&&e.availableHeight)}return t}function Md(n){const e=_d(n),t=Ve(e.filter(c=>c.box.fullSize),!0),i=Ve(We(e,"left"),!0),s=Ve(We(e,"right")),o=Ve(We(e,"top"),!0),r=Ve(We(e,"bottom")),a=jo(e,"x"),l=jo(e,"y");return{fullSize:t,leftAndTop:i.concat(o),rightAndBottom:s.concat(l).concat(r).concat(a),chartArea:We(e,"chartArea"),vertical:i.concat(s).concat(l),horizontal:o.concat(r).concat(a)}}function Wo(n,e,t,i){return Math.max(n[t],e[t])+Math.max(n[i],e[i])}function Fa(n,e){n.top=Math.max(n.top,e.top),n.left=Math.max(n.left,e.left),n.bottom=Math.max(n.bottom,e.bottom),n.right=Math.max(n.right,e.right)}function Dd(n,e,t,i){const{pos:s,box:o}=t,r=n.maxPadding;if(!Y(s)){t.size&&(n[s]-=t.size);const u=i[t.stack]||{size:0,count:1};u.size=Math.max(u.size,t.horizontal?o.height:o.width),t.size=u.size/u.count,n[s]+=t.size}o.getPadding&&Fa(r,o.getPadding());const a=Math.max(0,e.outerWidth-Wo(r,n,"left","right")),l=Math.max(0,e.outerHeight-Wo(r,n,"top","bottom")),c=a!==n.w,h=l!==n.h;return n.w=a,n.h=l,t.horizontal?{same:c,other:h}:{same:h,other:c}}function Sd(n){const e=n.maxPadding;function t(i){const s=Math.max(e[i]-n[i],0);return n[i]+=s,s}n.y+=t("top"),n.x+=t("left"),t("right"),t("bottom")}function kd(n,e){const t=e.maxPadding;function i(s){const o={left:0,top:0,right:0,bottom:0};return s.forEach(r=>{o[r]=Math.max(e[r],t[r])}),o}return i(n?["left","right"]:["top","bottom"])}function Xe(n,e,t,i){const s=[];let o,r,a,l,c,h;for(o=0,r=n.length,c=0;o<r;++o){a=n[o],l=a.box,l.update(a.width||e.w,a.height||e.h,kd(a.horizontal,e));const{same:u,other:d}=Dd(e,t,a,i);c|=u&&s.length,h=h||d,l.fullSize||s.push(a)}return c&&Xe(s,e,t,i)||h}function Tn(n,e,t,i,s){n.top=t,n.left=e,n.right=e+i,n.bottom=t+s,n.width=i,n.height=s}function Vo(n,e,t,i){const s=t.padding;let{x:o,y:r}=e;for(const a of n){const l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const u=e.w*h,d=c.size||l.height;ln(c.start)&&(r=c.start),l.fullSize?Tn(l,s.left,r,t.outerWidth-s.right-s.left,d):Tn(l,e.left+c.placed,r,u,d),c.start=r,c.placed+=u,r=l.bottom}else{const u=e.h*h,d=c.size||l.width;ln(c.start)&&(o=c.start),l.fullSize?Tn(l,o,s.top,d,t.outerHeight-s.bottom-s.top):Tn(l,o,e.top+c.placed,d,u),c.start=o,c.placed+=u,o=l.right}}e.x=o,e.y=r}var pt={addBox(n,e){n.boxes||(n.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},n.boxes.push(e)},removeBox(n,e){const t=n.boxes?n.boxes.indexOf(e):-1;t!==-1&&n.boxes.splice(t,1)},configure(n,e,t){e.fullSize=t.fullSize,e.position=t.position,e.weight=t.weight},update(n,e,t,i){if(!n)return;const s=mt(n.options.layout.padding),o=Math.max(e-s.width,0),r=Math.max(t-s.height,0),a=Md(n.boxes),l=a.vertical,c=a.horizontal;X(n.boxes,m=>{typeof m.beforeLayout=="function"&&m.beforeLayout()});const h=l.reduce((m,x)=>x.box.options&&x.box.options.display===!1?m:m+1,0)||1,u=Object.freeze({outerWidth:e,outerHeight:t,padding:s,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),d=Object.assign({},s);Fa(d,mt(i));const f=Object.assign({maxPadding:d,w:o,h:r,x:s.left,y:s.top},s),p=wd(l.concat(c),u);Xe(a.fullSize,f,u,p),Xe(l,f,u,p),Xe(c,f,u,p)&&Xe(l,f,u,p),Sd(f),Vo(a.leftAndTop,f,u,p),f.x+=f.w,f.y+=f.h,Vo(a.rightAndBottom,f,u,p),n.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},X(a.chartArea,m=>{const x=m.box;Object.assign(x,n.chartArea),x.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Ia{acquireContext(e,t){}releaseContext(e){return!1}addEventListener(e,t,i){}removeEventListener(e,t,i){}getDevicePixelRatio(){return 1}getMaximumSize(e,t,i,s){return t=Math.max(0,t||e.width),i=i||e.height,{width:t,height:Math.max(0,s?Math.floor(t/s):i)}}isAttached(e){return!0}updateConfig(e){}}class Cd extends Ia{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Jn="$chartjs",Od={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Yo=n=>n===null||n==="";function Pd(n,e){const t=n.style,i=n.getAttribute("height"),s=n.getAttribute("width");if(n[Jn]={initial:{height:i,width:s,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",Yo(s)){const o=Co(n,"width");o!==void 0&&(n.width=o)}if(Yo(i))if(n.style.height==="")n.height=n.width/(e||2);else{const o=Co(n,"height");o!==void 0&&(n.height=o)}return n}const Na=Eu?{passive:!0}:!1;function Ad(n,e,t){n&&n.addEventListener(e,t,Na)}function Ed(n,e,t){n&&n.canvas&&n.canvas.removeEventListener(e,t,Na)}function Td(n,e){const t=Od[n.type]||n.type,{x:i,y:s}=me(n,e);return{type:t,chart:e,native:n,x:i!==void 0?i:null,y:s!==void 0?s:null}}function ai(n,e){for(const t of n)if(t===e||t.contains(e))return!0}function Rd(n,e,t){const i=n.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||ai(a.addedNodes,i),r=r&&!ai(a.removedNodes,i);r&&t()});return s.observe(document,{childList:!0,subtree:!0}),s}function Ld(n,e,t){const i=n.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||ai(a.removedNodes,i),r=r&&!ai(a.addedNodes,i);r&&t()});return s.observe(document,{childList:!0,subtree:!0}),s}const un=new Map;let Uo=0;function Ba(){const n=window.devicePixelRatio;n!==Uo&&(Uo=n,un.forEach((e,t)=>{t.currentDevicePixelRatio!==n&&e()}))}function Fd(n,e){un.size||window.addEventListener("resize",Ba),un.set(n,e)}function Id(n){un.delete(n),un.size||window.removeEventListener("resize",Ba)}function Nd(n,e,t){const i=n.canvas,s=i&&Fs(i);if(!s)return;const o=ga((a,l)=>{const c=s.clientWidth;t(a,l),c<s.clientWidth&&t()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(s),Fd(n,o),r}function Hi(n,e,t){t&&t.disconnect(),e==="resize"&&Id(n)}function Bd(n,e,t){const i=n.canvas,s=ga(o=>{n.ctx!==null&&t(Td(o,n))},n);return Ad(i,e,s),s}class zd extends Ia{acquireContext(e,t){const i=e&&e.getContext&&e.getContext("2d");return i&&i.canvas===e?(Pd(e,t),i):null}releaseContext(e){const t=e.canvas;if(!t[Jn])return!1;const i=t[Jn].initial;["height","width"].forEach(o=>{const r=i[o];U(r)?t.removeAttribute(o):t.setAttribute(o,r)});const s=i.style||{};return Object.keys(s).forEach(o=>{t.style[o]=s[o]}),t.width=t.width,delete t[Jn],!0}addEventListener(e,t,i){this.removeEventListener(e,t);const s=e.$proxies||(e.$proxies={}),r={attach:Rd,detach:Ld,resize:Nd}[t]||Bd;s[t]=r(e,t,i)}removeEventListener(e,t){const i=e.$proxies||(e.$proxies={}),s=i[t];if(!s)return;({attach:Hi,detach:Hi,resize:Hi}[t]||Ed)(e,t,s),i[t]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,t,i,s){return Au(e,t,i,s)}isAttached(e){const t=e&&Fs(e);return!!(t&&t.isConnected)}}function Hd(n){return!Ls()||typeof OffscreenCanvas<"u"&&n instanceof OffscreenCanvas?Cd:zd}class It{constructor(){E(this,"x");E(this,"y");E(this,"active",!1);E(this,"options");E(this,"$animations")}tooltipPosition(e){const{x:t,y:i}=this.getProps(["x","y"],e);return{x:t,y:i}}hasValue(){return Le(this.x)&&Le(this.y)}getProps(e,t){const i=this.$animations;if(!t||!i)return this;const s={};return e.forEach(o=>{s[o]=i[o]&&i[o].active()?i[o]._to:this[o]}),s}}E(It,"defaults",{}),E(It,"defaultRoutes");function jd(n,e){const t=n.options.ticks,i=Wd(n),s=Math.min(t.maxTicksLimit||i,i),o=t.major.enabled?Yd(e):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>s)return Ud(e,c,o,r/s),c;const h=Vd(o,e,s);if(r>0){let u,d;const f=r>1?Math.round((l-a)/(r-1)):null;for(Rn(e,c,h,U(f)?0:a-f,a),u=0,d=r-1;u<d;u++)Rn(e,c,h,o[u],o[u+1]);return Rn(e,c,h,l,U(f)?e.length:l+f),c}return Rn(e,c,h),c}function Wd(n){const e=n.options.offset,t=n._tickSize(),i=n._length/t+(e?0:1),s=n._maxLength/t;return Math.floor(Math.min(i,s))}function Vd(n,e,t){const i=$d(n),s=e.length/t;if(!i)return Math.max(s,1);const o=Lh(i);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>s)return l}return Math.max(s,1)}function Yd(n){const e=[];let t,i;for(t=0,i=n.length;t<i;t++)n[t].major&&e.push(t);return e}function Ud(n,e,t,i){let s=0,o=t[0],r;for(i=Math.ceil(i),r=0;r<n.length;r++)r===o&&(e.push(n[r]),s++,o=t[s*i])}function Rn(n,e,t,i,s){const o=z(i,0),r=Math.min(z(s,n.length),n.length);let a=0,l,c,h;for(t=Math.ceil(t),s&&(l=s-i,t=l/Math.floor(l/t)),h=o;h<0;)a++,h=Math.round(o+a*t);for(c=Math.max(o,0);c<r;c++)c===h&&(e.push(n[c]),a++,h=Math.round(o+a*t))}function $d(n){const e=n.length;let t,i;if(e<2)return!1;for(i=n[0],t=1;t<e;++t)if(n[t]-n[t-1]!==i)return!1;return i}const qd=n=>n==="left"?"right":n==="right"?"left":n,$o=(n,e,t)=>e==="top"||e==="left"?n[e]+t:n[e]-t,qo=(n,e)=>Math.min(e||n,n);function Xo(n,e){const t=[],i=n.length/e,s=n.length;let o=0;for(;o<s;o+=i)t.push(n[Math.floor(o)]);return t}function Xd(n,e,t){const i=n.ticks.length,s=Math.min(e,i-1),o=n._startPixel,r=n._endPixel,a=1e-6;let l=n.getPixelForTick(s),c;if(!(t&&(i===1?c=Math.max(l-o,r-l):e===0?c=(n.getPixelForTick(1)-l)/2:c=(l-n.getPixelForTick(s-1))/2,l+=s<e?c:-c,l<o-a||l>r+a)))return l}function Kd(n,e){X(n,t=>{const i=t.gc,s=i.length/2;let o;if(s>e){for(o=0;o<s;++o)delete t.data[i[o]];i.splice(0,s)}})}function Ye(n){return n.drawTicks?n.tickLength:0}function Ko(n,e){if(!n.display)return 0;const t=ht(n.font,e),i=mt(n.padding);return(et(n.text)?n.text.length:1)*t.lineHeight+i.height}function Jd(n,e){return ce(n,{scale:e,type:"scale"})}function Gd(n,e,t){return ce(n,{tick:t,index:e,type:"tick"})}function Zd(n,e,t){let i=Os(n);return(t&&e!=="right"||!t&&e==="right")&&(i=qd(i)),i}function Qd(n,e,t,i){const{top:s,left:o,bottom:r,right:a,chart:l}=n,{chartArea:c,scales:h}=l;let u=0,d,f,p;const m=r-s,x=a-o;if(n.isHorizontal()){if(f=dt(i,o,a),Y(t)){const y=Object.keys(t)[0],v=t[y];p=h[y].getPixelForValue(v)+m-e}else t==="center"?p=(c.bottom+c.top)/2+m-e:p=$o(n,t,e);d=a-o}else{if(Y(t)){const y=Object.keys(t)[0],v=t[y];f=h[y].getPixelForValue(v)-x+e}else t==="center"?f=(c.left+c.right)/2-x+e:f=$o(n,t,e);p=dt(i,r,s),u=t==="left"?-rt:rt}return{titleX:f,titleY:p,maxWidth:d,rotation:u}}class Ce extends It{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,t){return e}getUserBounds(){let{_userMin:e,_userMax:t,_suggestedMin:i,_suggestedMax:s}=this;return e=Ct(e,Number.POSITIVE_INFINITY),t=Ct(t,Number.NEGATIVE_INFINITY),i=Ct(i,Number.POSITIVE_INFINITY),s=Ct(s,Number.NEGATIVE_INFINITY),{min:Ct(e,i),max:Ct(t,s),minDefined:ot(e),maxDefined:ot(t)}}getMinMax(e){let{min:t,max:i,minDefined:s,maxDefined:o}=this.getUserBounds(),r;if(s&&o)return{min:t,max:i};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,e),s||(t=Math.min(t,r.min)),o||(i=Math.max(i,r.max));return t=o&&t>i?i:t,i=s&&t>i?t:i,{min:Ct(t,Ct(i,t)),max:Ct(i,Ct(t,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){G(this.options.beforeUpdate,[this])}update(e,t,i){const{beginAtZero:s,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=t,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=lu(this,o,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Xo(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=jd(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,t,i;this.isHorizontal()?(t=this.left,i=this.right):(t=this.top,i=this.bottom,e=!e),this._startPixel=t,this._endPixel=i,this._reversePixels=e,this._length=i-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){G(this.options.afterUpdate,[this])}beforeSetDimensions(){G(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){G(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),G(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){G(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const t=this.options.ticks;let i,s,o;for(i=0,s=e.length;i<s;i++)o=e[i],o.label=G(t.callback,[o.value,i,e],this)}afterTickToLabelConversion(){G(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){G(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,t=e.ticks,i=qo(this.ticks.length,e.ticks.maxTicksLimit),s=t.minRotation||0,o=t.maxRotation;let r=s,a,l,c;if(!this._isVisible()||!t.display||s>=o||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const h=this._getLabelSizes(),u=h.widest.width,d=h.highest.height,f=ut(this.chart.width-u,0,this.maxWidth);a=e.offset?this.maxWidth/i:f/(i-1),u+6>a&&(a=f/(i-(e.offset?.5:1)),l=this.maxHeight-Ye(e.grid)-t.padding-Ko(e.title,this.chart.options.font),c=Math.sqrt(u*u+d*d),r=ks(Math.min(Math.asin(ut((h.highest.height+6)/a,-1,1)),Math.asin(ut(l/c,-1,1))-Math.asin(ut(d/c,-1,1)))),r=Math.max(s,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){G(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){G(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:t,options:{ticks:i,title:s,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Ko(s,t.options.font);if(a?(e.width=this.maxWidth,e.height=Ye(o)+l):(e.height=this.maxHeight,e.width=Ye(o)+l),i.display&&this.ticks.length){const{first:c,last:h,widest:u,highest:d}=this._getLabelSizes(),f=i.padding*2,p=Lt(this.labelRotation),m=Math.cos(p),x=Math.sin(p);if(a){const y=i.mirror?0:x*u.width+m*d.height;e.height=Math.min(this.maxHeight,e.height+y+f)}else{const y=i.mirror?0:m*u.width+x*d.height;e.width=Math.min(this.maxWidth,e.width+y+f)}this._calculatePadding(c,h,x,m)}}this._handleMargins(),a?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,t,i,s){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,u=this.right-this.getPixelForTick(this.ticks.length-1);let d=0,f=0;l?c?(d=s*e.width,f=i*t.height):(d=i*e.height,f=s*t.width):o==="start"?f=t.width:o==="end"?d=e.width:o!=="inner"&&(d=e.width/2,f=t.width/2),this.paddingLeft=Math.max((d-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-u+r)*this.width/(this.width-u),0)}else{let h=t.height/2,u=e.height/2;o==="start"?(h=0,u=e.height):o==="end"&&(h=t.height,u=0),this.paddingTop=h+r,this.paddingBottom=u+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){G(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:t}=this.options;return t==="top"||t==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let t,i;for(t=0,i=e.length;t<i;t++)U(e[t].label)&&(e.splice(t,1),i--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const t=this.options.ticks.sampleSize;let i=this.ticks;t<i.length&&(i=Xo(i,t)),this._labelSizes=e=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,t,i){const{ctx:s,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(t/qo(t,i));let c=0,h=0,u,d,f,p,m,x,y,v,S,k,D;for(u=0;u<t;u+=l){if(p=e[u].label,m=this._resolveTickFontOptions(u),s.font=x=m.string,y=o[x]=o[x]||{data:{},gc:[]},v=m.lineHeight,S=k=0,!U(p)&&!et(p))S=oi(s,y.data,y.gc,S,p),k=v;else if(et(p))for(d=0,f=p.length;d<f;++d)D=p[d],!U(D)&&!et(D)&&(S=oi(s,y.data,y.gc,S,D),k+=v);r.push(S),a.push(k),c=Math.max(S,c),h=Math.max(k,h)}Kd(o,t);const C=r.indexOf(c),O=a.indexOf(h),P=F=>({width:r[F]||0,height:a[F]||0});return{first:P(0),last:P(t-1),widest:P(C),highest:P(O),widths:r,heights:a}}getLabelForValue(e){return e}getPixelForValue(e,t){return NaN}getValueForPixel(e){}getPixelForTick(e){const t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const t=this._startPixel+e*this._length;return Nh(this._alignToPixels?fe(this.chart,t,0):t)}getDecimalForPixel(e){const t=(e-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:t}=this;return e<0&&t<0?t:e>0&&t>0?e:0}getContext(e){const t=this.ticks||[];if(e>=0&&e<t.length){const i=t[e];return i.$context||(i.$context=Gd(this.getContext(),e,i))}return this.$context||(this.$context=Jd(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,t=Lt(this.labelRotation),i=Math.abs(Math.cos(t)),s=Math.abs(Math.sin(t)),o=this._getLabelSizes(),r=e.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const t=this.axis,i=this.chart,s=this.options,{grid:o,position:r,border:a}=s,l=o.offset,c=this.isHorizontal(),u=this.ticks.length+(l?1:0),d=Ye(o),f=[],p=a.setContext(this.getContext()),m=p.display?p.width:0,x=m/2,y=function(J){return fe(i,J,m)};let v,S,k,D,C,O,P,F,N,H,W,tt;if(r==="top")v=y(this.bottom),O=this.bottom-d,F=v-x,H=y(e.top)+x,tt=e.bottom;else if(r==="bottom")v=y(this.top),H=e.top,tt=y(e.bottom)-x,O=v+x,F=this.top+d;else if(r==="left")v=y(this.right),C=this.right-d,P=v-x,N=y(e.left)+x,W=e.right;else if(r==="right")v=y(this.left),N=e.left,W=y(e.right)-x,C=v+x,P=this.left+d;else if(t==="x"){if(r==="center")v=y((e.top+e.bottom)/2+.5);else if(Y(r)){const J=Object.keys(r)[0],it=r[J];v=y(this.chart.scales[J].getPixelForValue(it))}H=e.top,tt=e.bottom,O=v+x,F=O+d}else if(t==="y"){if(r==="center")v=y((e.left+e.right)/2);else if(Y(r)){const J=Object.keys(r)[0],it=r[J];v=y(this.chart.scales[J].getPixelForValue(it))}C=v-x,P=C-d,N=e.left,W=e.right}const bt=z(s.ticks.maxTicksLimit,u),$=Math.max(1,Math.ceil(u/bt));for(S=0;S<u;S+=$){const J=this.getContext(S),it=o.setContext(J),yt=a.setContext(J),ct=it.lineWidth,Qt=it.color,te=yt.dash||[],ee=yt.dashOffset,he=it.tickWidth,Vt=it.tickColor,Tt=it.tickBorderDash||[],Rt=it.tickBorderDashOffset;k=Xd(this,S,l),k!==void 0&&(D=fe(i,k,ct),c?C=P=N=W=D:O=F=H=tt=D,f.push({tx1:C,ty1:O,tx2:P,ty2:F,x1:N,y1:H,x2:W,y2:tt,width:ct,color:Qt,borderDash:te,borderDashOffset:ee,tickWidth:he,tickColor:Vt,tickBorderDash:Tt,tickBorderDashOffset:Rt}))}return this._ticksLength=u,this._borderValue=v,f}_computeLabelItems(e){const t=this.axis,i=this.options,{position:s,ticks:o}=i,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:u}=o,d=Ye(i.grid),f=d+h,p=u?-h:f,m=-Lt(this.labelRotation),x=[];let y,v,S,k,D,C,O,P,F,N,H,W,tt="middle";if(s==="top")C=this.bottom-p,O=this._getXAxisLabelAlignment();else if(s==="bottom")C=this.top+p,O=this._getXAxisLabelAlignment();else if(s==="left"){const $=this._getYAxisLabelAlignment(d);O=$.textAlign,D=$.x}else if(s==="right"){const $=this._getYAxisLabelAlignment(d);O=$.textAlign,D=$.x}else if(t==="x"){if(s==="center")C=(e.top+e.bottom)/2+f;else if(Y(s)){const $=Object.keys(s)[0],J=s[$];C=this.chart.scales[$].getPixelForValue(J)+f}O=this._getXAxisLabelAlignment()}else if(t==="y"){if(s==="center")D=(e.left+e.right)/2-f;else if(Y(s)){const $=Object.keys(s)[0],J=s[$];D=this.chart.scales[$].getPixelForValue(J)}O=this._getYAxisLabelAlignment(d).textAlign}t==="y"&&(l==="start"?tt="top":l==="end"&&(tt="bottom"));const bt=this._getLabelSizes();for(y=0,v=a.length;y<v;++y){S=a[y],k=S.label;const $=o.setContext(this.getContext(y));P=this.getPixelForTick(y)+o.labelOffset,F=this._resolveTickFontOptions(y),N=F.lineHeight,H=et(k)?k.length:1;const J=H/2,it=$.color,yt=$.textStrokeColor,ct=$.textStrokeWidth;let Qt=O;r?(D=P,O==="inner"&&(y===v-1?Qt=this.options.reverse?"left":"right":y===0?Qt=this.options.reverse?"right":"left":Qt="center"),s==="top"?c==="near"||m!==0?W=-H*N+N/2:c==="center"?W=-bt.highest.height/2-J*N+N:W=-bt.highest.height+N/2:c==="near"||m!==0?W=N/2:c==="center"?W=bt.highest.height/2-J*N:W=bt.highest.height-H*N,u&&(W*=-1),m!==0&&!$.showLabelBackdrop&&(D+=N/2*Math.sin(m))):(C=P,W=(1-H)*N/2);let te;if($.showLabelBackdrop){const ee=mt($.backdropPadding),he=bt.heights[y],Vt=bt.widths[y];let Tt=W-ee.top,Rt=0-ee.left;switch(tt){case"middle":Tt-=he/2;break;case"bottom":Tt-=he;break}switch(O){case"center":Rt-=Vt/2;break;case"right":Rt-=Vt;break;case"inner":y===v-1?Rt-=Vt:y>0&&(Rt-=Vt/2);break}te={left:Rt,top:Tt,width:Vt+ee.width,height:he+ee.height,color:$.backdropColor}}x.push({label:k,font:F,textOffset:W,options:{rotation:m,color:it,strokeColor:yt,strokeWidth:ct,textAlign:Qt,textBaseline:tt,translation:[D,C],backdrop:te}})}return x}_getXAxisLabelAlignment(){const{position:e,ticks:t}=this.options;if(-Lt(this.labelRotation))return e==="top"?"left":"right";let s="center";return t.align==="start"?s="left":t.align==="end"?s="right":t.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(e){const{position:t,ticks:{crossAlign:i,mirror:s,padding:o}}=this.options,r=this._getLabelSizes(),a=e+o,l=r.widest.width;let c,h;return t==="left"?s?(h=this.right+o,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h=this.left)):t==="right"?s?(h=this.left+o,i==="near"?c="right":i==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,i==="near"?c="left":i==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,t=this.options.position;if(t==="left"||t==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(t==="top"||t==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:t},left:i,top:s,width:o,height:r}=this;t&&(e.save(),e.fillStyle=t,e.fillRect(i,s,o,r),e.restore())}getLineWidthForValue(e){const t=this.options.grid;if(!this._isVisible()||!t.display)return 0;const s=this.ticks.findIndex(o=>o.value===e);return s>=0?t.setContext(this.getContext(s)).lineWidth:0}drawGrid(e){const t=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(i.save(),i.lineWidth=h.width,i.strokeStyle=h.color,i.setLineDash(h.borderDash||[]),i.lineDashOffset=h.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(t.display)for(o=0,r=s.length;o<r;++o){const l=s[o];t.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),t.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:t,options:{border:i,grid:s}}=this,o=i.setContext(this.getContext()),r=i.display?o.width:0;if(!r)return;const a=s.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,u,d;this.isHorizontal()?(c=fe(e,this.left,r)-r/2,h=fe(e,this.right,a)+a/2,u=d=l):(u=fe(e,this.top,r)-r/2,d=fe(e,this.bottom,a)+a/2,c=h=l),t.save(),t.lineWidth=o.width,t.strokeStyle=o.color,t.beginPath(),t.moveTo(c,u),t.lineTo(h,d),t.stroke(),t.restore()}drawLabels(e){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&xi(i,s);const o=this.getLabelItems(e);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;ke(i,c,0,h,l,a)}s&&yi(i)}drawTitle(){const{ctx:e,options:{position:t,title:i,reverse:s}}=this;if(!i.display)return;const o=ht(i.font),r=mt(i.padding),a=i.align;let l=o.lineHeight/2;t==="bottom"||t==="center"||Y(t)?(l+=r.bottom,et(i.text)&&(l+=o.lineHeight*(i.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:u,rotation:d}=Qd(this,l,t,a);ke(e,i.text,0,0,o,{color:i.color,maxWidth:u,rotation:d,textAlign:Zd(a,t,s),textBaseline:"middle",translation:[c,h]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,t=e.ticks&&e.ticks.z||0,i=z(e.grid&&e.grid.z,-1),s=z(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Ce.prototype.draw?[{z:t,draw:o=>{this.draw(o)}}]:[{z:i,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:t,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(e){const t=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let o,r;for(o=0,r=t.length;o<r;++o){const a=t[o];a[i]===this.id&&(!e||a.type===e)&&s.push(a)}return s}_resolveTickFontOptions(e){const t=this.options.ticks.setContext(this.getContext(e));return ht(t.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Ln{constructor(e,t,i){this.type=e,this.scope=t,this.override=i,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const t=Object.getPrototypeOf(e);let i;nf(t)&&(i=this.register(t));const s=this.items,o=e.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+e);return o in s||(s[o]=e,tf(e,r,i),this.override&&nt.override(e.id,e.overrides)),r}get(e){return this.items[e]}unregister(e){const t=this.items,i=e.id,s=this.scope;i in t&&delete t[i],s&&i in nt[s]&&(delete nt[s][i],this.override&&delete Se[i])}}function tf(n,e,t){const i=an(Object.create(null),[t?nt.get(t):{},nt.get(e),n.defaults]);nt.set(e,i),n.defaultRoutes&&ef(e,n.defaultRoutes),n.descriptors&&nt.describe(e,n.descriptors)}function ef(n,e){Object.keys(e).forEach(t=>{const i=t.split("."),s=i.pop(),o=[n].concat(i).join("."),r=e[t].split("."),a=r.pop(),l=r.join(".");nt.route(o,s,l,a)})}function nf(n){return"id"in n&&"defaults"in n}class sf{constructor(){this.controllers=new Ln(Ft,"datasets",!0),this.elements=new Ln(It,"elements"),this.plugins=new Ln(Object,"plugins"),this.scales=new Ln(Ce,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,t,i){[...t].forEach(s=>{const o=i||this._getRegistryForType(s);i||o.isForType(s)||o===this.plugins&&s.id?this._exec(e,o,s):X(s,r=>{const a=i||this._getRegistryForType(r);this._exec(e,a,r)})})}_exec(e,t,i){const s=Ss(e);G(i["before"+s],[],i),t[e](i),G(i["after"+s],[],i)}_getRegistryForType(e){for(let t=0;t<this._typedRegistries.length;t++){const i=this._typedRegistries[t];if(i.isForType(e))return i}return this.plugins}_get(e,t,i){const s=t.get(e);if(s===void 0)throw new Error('"'+e+'" is not a registered '+i+".");return s}}var jt=new sf;class of{constructor(){this._init=[]}notify(e,t,i,s){t==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const o=s?this._descriptors(e).filter(s):this._descriptors(e),r=this._notify(o,e,t,i);return t==="afterDestroy"&&(this._notify(o,e,"stop"),this._notify(this._init,e,"uninstall")),r}_notify(e,t,i,s){s=s||{};for(const o of e){const r=o.plugin,a=r[i],l=[t,s,o.options];if(G(a,l,r)===!1&&s.cancelable)return!1}return!0}invalidate(){U(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const t=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),t}_createDescriptors(e,t){const i=e&&e.config,s=z(i.options&&i.options.plugins,{}),o=rf(i);return s===!1&&!t?[]:lf(e,o,s,t)}_notifyStateChanges(e){const t=this._oldCache||[],i=this._cache,s=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(s(t,i),e,"stop"),this._notify(s(i,t),e,"start")}}function rf(n){const e={},t=[],i=Object.keys(jt.plugins.items);for(let o=0;o<i.length;o++)t.push(jt.getPlugin(i[o]));const s=n.plugins||[];for(let o=0;o<s.length;o++){const r=s[o];t.indexOf(r)===-1&&(t.push(r),e[r.id]=!0)}return{plugins:t,localIds:e}}function af(n,e){return!e&&n===!1?null:n===!0?{}:n}function lf(n,{plugins:e,localIds:t},i,s){const o=[],r=n.getContext();for(const a of e){const l=a.id,c=af(i[l],s);c!==null&&o.push({plugin:a,options:cf(n.config,{plugin:a,local:t[l]},c,r)})}return o}function cf(n,{plugin:e,local:t},i,s){const o=n.pluginScopeKeys(e),r=n.getOptionScopes(i,o);return t&&e.defaults&&r.push(e.defaults),n.createResolver(r,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function rs(n,e){const t=nt.datasets[n]||{};return((e.datasets||{})[n]||{}).indexAxis||e.indexAxis||t.indexAxis||"x"}function hf(n,e){let t=n;return n==="_index_"?t=e:n==="_value_"&&(t=e==="x"?"y":"x"),t}function uf(n,e){return n===e?"_index_":"_value_"}function Jo(n){if(n==="x"||n==="y"||n==="r")return n}function df(n){if(n==="top"||n==="bottom")return"x";if(n==="left"||n==="right")return"y"}function as(n,...e){if(Jo(n))return n;for(const t of e){const i=t.axis||df(t.position)||n.length>1&&Jo(n[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${n}' axis. Please provide 'axis' or 'position' option.`)}function Go(n,e,t){if(t[e+"AxisID"]===n)return{axis:e}}function ff(n,e){if(e.data&&e.data.datasets){const t=e.data.datasets.filter(i=>i.xAxisID===n||i.yAxisID===n);if(t.length)return Go(n,"x",t[0])||Go(n,"y",t[0])}return{}}function gf(n,e){const t=Se[n.type]||{scales:{}},i=e.scales||{},s=rs(n.type,e),o=Object.create(null);return Object.keys(i).forEach(r=>{const a=i[r];if(!Y(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=as(r,a,ff(r,n),nt.scales[a.type]),c=uf(l,s),h=t.scales||{};o[r]=Ze(Object.create(null),[{axis:l},a,h[l],h[c]])}),n.data.datasets.forEach(r=>{const a=r.type||n.type,l=r.indexAxis||rs(a,e),h=(Se[a]||{}).scales||{};Object.keys(h).forEach(u=>{const d=hf(u,l),f=r[d+"AxisID"]||d;o[f]=o[f]||Object.create(null),Ze(o[f],[{axis:d},i[f],h[u]])})}),Object.keys(o).forEach(r=>{const a=o[r];Ze(a,[nt.scales[a.type],nt.scale])}),o}function za(n){const e=n.options||(n.options={});e.plugins=z(e.plugins,{}),e.scales=gf(n,e)}function Ha(n){return n=n||{},n.datasets=n.datasets||[],n.labels=n.labels||[],n}function pf(n){return n=n||{},n.data=Ha(n.data),za(n),n}const Zo=new Map,ja=new Set;function Fn(n,e){let t=Zo.get(n);return t||(t=e(),Zo.set(n,t),ja.add(t)),t}const Ue=(n,e,t)=>{const i=ae(e,t);i!==void 0&&n.add(i)};class mf{constructor(e){this._config=pf(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=Ha(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),za(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Fn(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,t){return Fn(`${e}.transition.${t}`,()=>[[`datasets.${e}.transitions.${t}`,`transitions.${t}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,t){return Fn(`${e}-${t}`,()=>[[`datasets.${e}.elements.${t}`,`datasets.${e}`,`elements.${t}`,""]])}pluginScopeKeys(e){const t=e.id,i=this.type;return Fn(`${i}-plugin-${t}`,()=>[[`plugins.${t}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,t){const i=this._scopeCache;let s=i.get(e);return(!s||t)&&(s=new Map,i.set(e,s)),s}getOptionScopes(e,t,i){const{options:s,type:o}=this,r=this._cachedScopes(e,i),a=r.get(t);if(a)return a;const l=new Set;t.forEach(h=>{e&&(l.add(e),h.forEach(u=>Ue(l,e,u))),h.forEach(u=>Ue(l,s,u)),h.forEach(u=>Ue(l,Se[o]||{},u)),h.forEach(u=>Ue(l,nt,u)),h.forEach(u=>Ue(l,is,u))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),ja.has(t)&&r.set(t,c),c}chartOptionScopes(){const{options:e,type:t}=this;return[e,Se[t]||{},nt.datasets[t]||{},{type:t},nt,is]}resolveNamedOptions(e,t,i,s=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Qo(this._resolverCache,e,s);let l=r;if(xf(r,t)){o.$shared=!1,i=le(i)?i():i;const c=this.createResolver(e,i,a);l=Fe(r,i,c)}for(const c of t)o[c]=l[c];return o}createResolver(e,t,i=[""],s){const{resolver:o}=Qo(this._resolverCache,e,i);return Y(t)?Fe(o,t,void 0,s):o}}function Qo(n,e,t){let i=n.get(e);i||(i=new Map,n.set(e,i));const s=t.join();let o=i.get(s);return o||(o={resolver:Es(e,t),subPrefixes:t.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,o)),o}const bf=n=>Y(n)&&Object.getOwnPropertyNames(n).some(e=>le(n[e]));function xf(n,e){const{isScriptable:t,isIndexable:i}=_a(n);for(const s of e){const o=t(s),r=i(s),a=(r||o)&&n[s];if(o&&(le(a)||bf(a))||r&&et(a))return!0}return!1}var yf="4.4.7";const _f=["top","bottom","left","right","chartArea"];function tr(n,e){return n==="top"||n==="bottom"||_f.indexOf(n)===-1&&e==="x"}function er(n,e){return function(t,i){return t[n]===i[n]?t[e]-i[e]:t[n]-i[n]}}function nr(n){const e=n.chart,t=e.options.animation;e.notifyPlugins("afterRender"),G(t&&t.onComplete,[n],e)}function vf(n){const e=n.chart,t=e.options.animation;G(t&&t.onProgress,[n],e)}function Wa(n){return Ls()&&typeof n=="string"?n=document.getElementById(n):n&&n.length&&(n=n[0]),n&&n.canvas&&(n=n.canvas),n}const Gn={},ir=n=>{const e=Wa(n);return Object.values(Gn).filter(t=>t.canvas===e).pop()};function wf(n,e,t){const i=Object.keys(n);for(const s of i){const o=+s;if(o>=e){const r=n[s];delete n[s],(t>0||o>e)&&(n[o+t]=r)}}}function Mf(n,e,t,i){return!t||n.type==="mouseout"?null:i?e:n}function In(n,e,t){return n.options.clip?n[t]:e[t]}function Df(n,e){const{xScale:t,yScale:i}=n;return t&&i?{left:In(t,e,"left"),right:In(t,e,"right"),top:In(i,e,"top"),bottom:In(i,e,"bottom")}:e}class Kt{static register(...e){jt.add(...e),sr()}static unregister(...e){jt.remove(...e),sr()}constructor(e,t){const i=this.config=new mf(t),s=Wa(e),o=ir(s);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||Hd(s)),this.platform.updateConfig(i);const a=this.platform.acquireContext(s,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Sh(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new of,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=jh(u=>this.update(u),r.resizeDelay||0),this._dataChanges=[],Gn[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}$t.listen(this,"complete",nr),$t.listen(this,"progress",vf),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:t},width:i,height:s,_aspectRatio:o}=this;return U(e)?t&&o?o:s?i/s:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return jt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():ko(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Mo(this.canvas,this.ctx),this}stop(){return $t.stop(this),this}resize(e,t){$t.running(this)?this._resizeBeforeDraw={width:e,height:t}:this._resize(e,t)}_resize(e,t){const i=this.options,s=this.canvas,o=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,e,t,o),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,ko(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),G(i.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const t=this.options.scales||{};X(t,(i,s)=>{i.id=s})}buildOrUpdateScales(){const e=this.options,t=e.scales,i=this.scales,s=Object.keys(i).reduce((r,a)=>(r[a]=!1,r),{});let o=[];t&&(o=o.concat(Object.keys(t).map(r=>{const a=t[r],l=as(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),X(o,r=>{const a=r.options,l=a.id,c=as(l,a),h=z(a.type,r.dtype);(a.position===void 0||tr(a.position,c)!==tr(r.dposition))&&(a.position=r.dposition),s[l]=!0;let u=null;if(l in i&&i[l].type===h)u=i[l];else{const d=jt.getScale(h);u=new d({id:l,type:h,ctx:this.ctx,chart:this}),i[u.id]=u}u.init(a,e)}),X(s,(r,a)=>{r||delete i[a]}),X(i,r=>{pt.configure(this,r,r.options),pt.addBox(this,r)})}_updateMetasets(){const e=this._metasets,t=this.data.datasets.length,i=e.length;if(e.sort((s,o)=>s.index-o.index),i>t){for(let s=t;s<i;++s)this._destroyDatasetMeta(s);e.splice(t,i-t)}this._sortedMetasets=e.slice(0).sort(er("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:t}}=this;e.length>t.length&&delete this._stacks,e.forEach((i,s)=>{t.filter(o=>o===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const e=[],t=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=t.length;i<s;i++){const o=t[i];let r=this.getDatasetMeta(i);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(i),r=this.getDatasetMeta(i)),r.type=a,r.indexAxis=o.indexAxis||rs(a,this.options),r.order=o.order||0,r.index=i,r.label=""+o.label,r.visible=this.isDatasetVisible(i),r.controller)r.controller.updateIndex(i),r.controller.linkScales();else{const l=jt.getController(a),{datasetElementType:c,dataElementType:h}=nt.datasets[a];Object.assign(l,{dataElementType:jt.getElement(h),datasetElementType:c&&jt.getElement(c)}),r.controller=new l(this,i),e.push(r.controller)}}return this._updateMetasets(),e}_resetElements(){X(this.data.datasets,(e,t)=>{this.getDatasetMeta(t).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const t=this.config;t.update();const i=this._options=t.createResolver(t.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:u}=this.getDatasetMeta(c),d=!s&&o.indexOf(u)===-1;u.buildOrUpdateElements(d),r=Math.max(+u.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),s||X(o,c=>{c.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(er("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){X(this.scales,e=>{pt.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,t=new Set(Object.keys(this._listeners)),i=new Set(e.events);(!go(t,i)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,t=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:o}of t){const r=i==="_removeElements"?-o:o;wf(e,s,r)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const t=this.data.datasets.length,i=o=>new Set(e.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),s=i(0);for(let o=1;o<t;o++)if(!go(s,i(o)))return;return Array.from(s).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;pt.update(this,this.width,this.height,e);const t=this.chartArea,i=t.width<=0||t.height<=0;this._layers=[],X(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,o)=>{s._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let t=0,i=this.data.datasets.length;t<i;++t)this.getDatasetMeta(t).controller.configure();for(let t=0,i=this.data.datasets.length;t<i;++t)this._updateDataset(t,le(e)?e({datasetIndex:t}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,t){const i=this.getDatasetMeta(e),s={meta:i,index:e,mode:t,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(t),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&($t.has(this)?this.attached&&!$t.running(this)&&$t.start(this):(this.draw(),nr({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const t=this._layers;for(e=0;e<t.length&&t[e].z<=0;++e)t[e].draw(this.chartArea);for(this._drawDatasets();e<t.length;++e)t[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const t=this._sortedMetasets,i=[];let s,o;for(s=0,o=t.length;s<o;++s){const r=t[s];(!e||r.visible)&&i.push(r)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let t=e.length-1;t>=0;--t)this._drawDataset(e[t]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const t=this.ctx,i=e._clip,s=!i.disabled,o=Df(e,this.chartArea),r={meta:e,index:e.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(s&&xi(t,{left:i.left===!1?0:o.left-i.left,right:i.right===!1?this.width:o.right+i.right,top:i.top===!1?0:o.top-i.top,bottom:i.bottom===!1?this.height:o.bottom+i.bottom}),e.controller.draw(),s&&yi(t),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(e){return Zt(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,t,i,s){const o=yd.modes[t];return typeof o=="function"?o(this,e,i,s):[]}getDatasetMeta(e){const t=this.data.datasets[e],i=this._metasets;let s=i.filter(o=>o&&o._dataset===t).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:t&&t.order||0,index:e,_dataset:t,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=ce(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const t=this.data.datasets[e];if(!t)return!1;const i=this.getDatasetMeta(e);return typeof i.hidden=="boolean"?!i.hidden:!t.hidden}setDatasetVisibility(e,t){const i=this.getDatasetMeta(e);i.hidden=!t}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,t,i){const s=i?"show":"hide",o=this.getDatasetMeta(e),r=o.controller._resolveAnimations(void 0,s);ln(t)?(o.data[t].hidden=!i,this.update()):(this.setDatasetVisibility(e,i),r.update(o,{visible:i}),this.update(a=>a.datasetIndex===e?s:void 0))}hide(e,t){this._updateVisibility(e,t,!1)}show(e,t){this._updateVisibility(e,t,!0)}_destroyDatasetMeta(e){const t=this._metasets[e];t&&t.controller&&t.controller._destroy(),delete this._metasets[e]}_stop(){let e,t;for(this.stop(),$t.remove(this),e=0,t=this.data.datasets.length;e<t;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:t}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Mo(e,t),this.platform.releaseContext(t),this.canvas=null,this.ctx=null),delete Gn[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,t=this.platform,i=(o,r)=>{t.addEventListener(this,o,r),e[o]=r},s=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};X(this.options.events,o=>i(o,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,t=this.platform,i=(l,c)=>{t.addEventListener(this,l,c),e[l]=c},s=(l,c)=>{e[l]&&(t.removeEventListener(this,l,c),delete e[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",o),i("detach",r)};r=()=>{this.attached=!1,s("resize",o),this._stop(),this._resize(0,0),i("attach",a)},t.isAttached(this.canvas)?a():r()}unbindEvents(){X(this._listeners,(e,t)=>{this.platform.removeEventListener(this,t,e)}),this._listeners={},X(this._responsiveListeners,(e,t)=>{this.platform.removeEventListener(this,t,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,t,i){const s=i?"set":"remove";let o,r,a,l;for(t==="dataset"&&(o=this.getDatasetMeta(e[0].datasetIndex),o.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=e.length;a<l;++a){r=e[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[s+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const t=this._active||[],i=e.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!ni(i,t)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,t))}notifyPlugins(e,t,i){return this._plugins.notify(this,e,t,i)}isPluginEnabled(e){return this._plugins._cache.filter(t=>t.plugin.id===e).length===1}_updateHoverStyles(e,t,i){const s=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(u=>h.datasetIndex===u.datasetIndex&&h.index===u.index)),r=o(t,e),a=i?e:o(e,t);r.length&&this.updateHoverStyle(r,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(e,t){const i={event:e,replay:t,cancelable:!0,inChartArea:this.isPointInArea(e)},s=r=>(r.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const o=this._handleEvent(e,t,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(o||i.changed)&&this.render(),this}_handleEvent(e,t,i){const{_active:s=[],options:o}=this,r=t,a=this._getActiveElements(e,s,i,r),l=Eh(e),c=Mf(e,this._lastEvent,i,l);i&&(this._lastEvent=null,G(o.onHover,[e,a,this],this),l&&G(o.onClick,[e,a,this],this));const h=!ni(a,s);return(h||t)&&(this._active=a,this._updateHoverStyles(a,s,t)),this._lastEvent=c,h}_getActiveElements(e,t,i,s){if(e.type==="mouseout")return[];if(!i)return t;const o=this.options.hover;return this.getElementsAtEventForMode(e,o.mode,o,s)}}E(Kt,"defaults",nt),E(Kt,"instances",Gn),E(Kt,"overrides",Se),E(Kt,"registry",jt),E(Kt,"version",yf),E(Kt,"getChart",ir);function sr(){return X(Kt.instances,n=>n._plugins.invalidate())}function Sf(n,e,t){const{startAngle:i,pixelMargin:s,x:o,y:r,outerRadius:a,innerRadius:l}=e;let c=s/a;n.beginPath(),n.arc(o,r,a,i-c,t+c),l>s?(c=s/l,n.arc(o,r,l,t+c,i-c,!0)):n.arc(o,r,s,t+rt,i-rt),n.closePath(),n.clip()}function kf(n){return As(n,["outerStart","outerEnd","innerStart","innerEnd"])}function Cf(n,e,t,i){const s=kf(n.options.borderRadius),o=(t-e)/2,r=Math.min(o,i*e/2),a=l=>{const c=(t-Math.min(o,l))*i/2;return ut(l,0,Math.min(o,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:ut(s.innerStart,0,r),innerEnd:ut(s.innerEnd,0,r)}}function Ae(n,e,t,i){return{x:t+n*Math.cos(e),y:i+n*Math.sin(e)}}function li(n,e,t,i,s,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=e,u=Math.max(e.outerRadius+i+t-c,0),d=h>0?h+i+t+c:0;let f=0;const p=s-l;if(i){const $=h>0?h-i:0,J=u>0?u-i:0,it=($+J)/2,yt=it!==0?p*it/(it+i):p;f=(p-yt)/2}const m=Math.max(.001,p*u-t/Q)/u,x=(p-m)/2,y=l+x+f,v=s-x-f,{outerStart:S,outerEnd:k,innerStart:D,innerEnd:C}=Cf(e,d,u,v-y),O=u-S,P=u-k,F=y+S/O,N=v-k/P,H=d+D,W=d+C,tt=y+D/H,bt=v-C/W;if(n.beginPath(),o){const $=(F+N)/2;if(n.arc(r,a,u,F,$),n.arc(r,a,u,$,N),k>0){const ct=Ae(P,N,r,a);n.arc(ct.x,ct.y,k,N,v+rt)}const J=Ae(W,v,r,a);if(n.lineTo(J.x,J.y),C>0){const ct=Ae(W,bt,r,a);n.arc(ct.x,ct.y,C,v+rt,bt+Math.PI)}const it=(v-C/d+(y+D/d))/2;if(n.arc(r,a,d,v-C/d,it,!0),n.arc(r,a,d,it,y+D/d,!0),D>0){const ct=Ae(H,tt,r,a);n.arc(ct.x,ct.y,D,tt+Math.PI,y-rt)}const yt=Ae(O,y,r,a);if(n.lineTo(yt.x,yt.y),S>0){const ct=Ae(O,F,r,a);n.arc(ct.x,ct.y,S,y-rt,F)}}else{n.moveTo(r,a);const $=Math.cos(F)*u+r,J=Math.sin(F)*u+a;n.lineTo($,J);const it=Math.cos(N)*u+r,yt=Math.sin(N)*u+a;n.lineTo(it,yt)}n.closePath()}function Of(n,e,t,i,s){const{fullCircles:o,startAngle:r,circumference:a}=e;let l=e.endAngle;if(o){li(n,e,t,i,l,s);for(let c=0;c<o;++c)n.fill();isNaN(a)||(l=r+(a%Z||Z))}return li(n,e,t,i,l,s),n.fill(),l}function Pf(n,e,t,i,s){const{fullCircles:o,startAngle:r,circumference:a,options:l}=e,{borderWidth:c,borderJoinStyle:h,borderDash:u,borderDashOffset:d}=l,f=l.borderAlign==="inner";if(!c)return;n.setLineDash(u||[]),n.lineDashOffset=d,f?(n.lineWidth=c*2,n.lineJoin=h||"round"):(n.lineWidth=c,n.lineJoin=h||"bevel");let p=e.endAngle;if(o){li(n,e,t,i,p,s);for(let m=0;m<o;++m)n.stroke();isNaN(a)||(p=r+(a%Z||Z))}f&&Sf(n,e,p),o||(li(n,e,t,i,p,s),n.stroke())}class Ke extends It{constructor(t){super();E(this,"circumference");E(this,"endAngle");E(this,"fullCircles");E(this,"innerRadius");E(this,"outerRadius");E(this,"pixelMargin");E(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,i,s){const o=this.getProps(["x","y"],s),{angle:r,distance:a}=ha(o,{x:t,y:i}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:u,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),f=(this.options.spacing+this.options.borderWidth)/2,p=z(d,c-l),m=cn(r,l,c)&&l!==c,x=p>=Z||m,y=Jt(a,h+f,u+f);return x&&y}getCenterPoint(t){const{x:i,y:s,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:c,spacing:h}=this.options,u=(o+r)/2,d=(a+l+h+c)/2;return{x:i+Math.cos(u)*d,y:s+Math.sin(u)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:i,circumference:s}=this,o=(i.offset||0)/4,r=(i.spacing||0)/2,a=i.circular;if(this.pixelMargin=i.borderAlign==="inner"?.33:0,this.fullCircles=s>Z?Math.floor(s/Z):0,s===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const l=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(Q,s||0)),h=o*c;t.fillStyle=i.backgroundColor,t.strokeStyle=i.borderColor,Of(t,this,h,r,a),Pf(t,this,h,r,a),t.restore()}}E(Ke,"id","arc"),E(Ke,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),E(Ke,"defaultRoutes",{backgroundColor:"backgroundColor"}),E(Ke,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"});function Va(n,e,t=e){n.lineCap=z(t.borderCapStyle,e.borderCapStyle),n.setLineDash(z(t.borderDash,e.borderDash)),n.lineDashOffset=z(t.borderDashOffset,e.borderDashOffset),n.lineJoin=z(t.borderJoinStyle,e.borderJoinStyle),n.lineWidth=z(t.borderWidth,e.borderWidth),n.strokeStyle=z(t.borderColor,e.borderColor)}function Af(n,e,t){n.lineTo(t.x,t.y)}function Ef(n){return n.stepped?Qh:n.tension||n.cubicInterpolationMode==="monotone"?tu:Af}function Ya(n,e,t={}){const i=n.length,{start:s=0,end:o=i-1}=t,{start:r,end:a}=e,l=Math.max(s,r),c=Math.min(o,a),h=s<r&&o<r||s>a&&o>a;return{count:i,start:l,loop:e.loop,ilen:c<l&&!h?i+c-l:c-l}}function Tf(n,e,t,i){const{points:s,options:o}=e,{count:r,start:a,loop:l,ilen:c}=Ya(s,t,i),h=Ef(o);let{move:u=!0,reverse:d}=i||{},f,p,m;for(f=0;f<=c;++f)p=s[(a+(d?c-f:f))%r],!p.skip&&(u?(n.moveTo(p.x,p.y),u=!1):h(n,m,p,d,o.stepped),m=p);return l&&(p=s[(a+(d?c:0))%r],h(n,m,p,d,o.stepped)),!!l}function Rf(n,e,t,i){const s=e.points,{count:o,start:r,ilen:a}=Ya(s,t,i),{move:l=!0,reverse:c}=i||{};let h=0,u=0,d,f,p,m,x,y;const v=k=>(r+(c?a-k:k))%o,S=()=>{m!==x&&(n.lineTo(h,x),n.lineTo(h,m),n.lineTo(h,y))};for(l&&(f=s[v(0)],n.moveTo(f.x,f.y)),d=0;d<=a;++d){if(f=s[v(d)],f.skip)continue;const k=f.x,D=f.y,C=k|0;C===p?(D<m?m=D:D>x&&(x=D),h=(u*h+k)/++u):(S(),n.lineTo(k,D),p=C,u=0,m=x=D),y=D}S()}function ls(n){const e=n.options,t=e.borderDash&&e.borderDash.length;return!n._decimated&&!n._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!t?Rf:Tf}function Lf(n){return n.stepped?Tu:n.tension||n.cubicInterpolationMode==="monotone"?Ru:be}function Ff(n,e,t,i){let s=e._path;s||(s=e._path=new Path2D,e.path(s,t,i)&&s.closePath()),Va(n,e.options),n.stroke(s)}function If(n,e,t,i){const{segments:s,options:o}=e,r=ls(e);for(const a of s)Va(n,o,a.style),n.beginPath(),r(n,e,a,{start:t,end:t+i-1})&&n.closePath(),n.stroke()}const Nf=typeof Path2D=="function";function Bf(n,e,t,i){Nf&&!e.options.segment?Ff(n,e,t,i):If(n,e,t,i)}class se extends It{constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,t){const i=this.options;if((i.tension||i.cubicInterpolationMode==="monotone")&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;Du(this._points,i,e,s,t),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=zu(this,this.options.segment))}first(){const e=this.segments,t=this.points;return e.length&&t[e[0].start]}last(){const e=this.segments,t=this.points,i=e.length;return i&&t[e[i-1].end]}interpolate(e,t){const i=this.options,s=e[t],o=this.points,r=Aa(this,{property:t,start:s,end:s});if(!r.length)return;const a=[],l=Lf(i);let c,h;for(c=0,h=r.length;c<h;++c){const{start:u,end:d}=r[c],f=o[u],p=o[d];if(f===p){a.push(f);continue}const m=Math.abs((s-f[t])/(p[t]-f[t])),x=l(f,p,m,i.stepped);x[t]=e[t],a.push(x)}return a.length===1?a[0]:a}pathSegment(e,t,i){return ls(this)(e,this,t,i)}path(e,t,i){const s=this.segments,o=ls(this);let r=this._loop;t=t||0,i=i||this.points.length-t;for(const a of s)r&=o(e,this,a,{start:t,end:t+i-1});return!!r}draw(e,t,i,s){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(e.save(),Bf(e,this,i,s),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}E(se,"id","line"),E(se,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),E(se,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),E(se,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"&&e!=="fill"});function or(n,e,t,i){const s=n.options,{[t]:o}=n.getProps([t],i);return Math.abs(e-o)<s.radius+s.hitRadius}class Zn extends It{constructor(t){super();E(this,"parsed");E(this,"skip");E(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,i,s){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],s);return Math.pow(t-r,2)+Math.pow(i-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(t,i){return or(this,t,"x",i)}inYRange(t,i){return or(this,t,"y",i)}getCenterPoint(t){const{x:i,y:s}=this.getProps(["x","y"],t);return{x:i,y:s}}size(t){t=t||this.options||{};let i=t.radius||0;i=Math.max(i,i&&t.hoverRadius||0);const s=i&&t.borderWidth||0;return(i+s)*2}draw(t,i){const s=this.options;this.skip||s.radius<.1||!Zt(this,i,this.size(s)/2)||(t.strokeStyle=s.borderColor,t.lineWidth=s.borderWidth,t.fillStyle=s.backgroundColor,ss(t,s,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}E(Zn,"id","point"),E(Zn,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),E(Zn,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Ua(n,e){const{x:t,y:i,base:s,width:o,height:r}=n.getProps(["x","y","base","width","height"],e);let a,l,c,h,u;return n.horizontal?(u=r/2,a=Math.min(t,s),l=Math.max(t,s),c=i-u,h=i+u):(u=o/2,a=t-u,l=t+u,c=Math.min(i,s),h=Math.max(i,s)),{left:a,top:c,right:l,bottom:h}}function oe(n,e,t,i){return n?0:ut(e,t,i)}function zf(n,e,t){const i=n.options.borderWidth,s=n.borderSkipped,o=ya(i);return{t:oe(s.top,o.top,0,t),r:oe(s.right,o.right,0,e),b:oe(s.bottom,o.bottom,0,t),l:oe(s.left,o.left,0,e)}}function Hf(n,e,t){const{enableBorderRadius:i}=n.getProps(["enableBorderRadius"]),s=n.options.borderRadius,o=we(s),r=Math.min(e,t),a=n.borderSkipped,l=i||Y(s);return{topLeft:oe(!l||a.top||a.left,o.topLeft,0,r),topRight:oe(!l||a.top||a.right,o.topRight,0,r),bottomLeft:oe(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:oe(!l||a.bottom||a.right,o.bottomRight,0,r)}}function jf(n){const e=Ua(n),t=e.right-e.left,i=e.bottom-e.top,s=zf(n,t/2,i/2),o=Hf(n,t/2,i/2);return{outer:{x:e.left,y:e.top,w:t,h:i,radius:o},inner:{x:e.left+s.l,y:e.top+s.t,w:t-s.l-s.r,h:i-s.t-s.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,o.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(s.b,s.r))}}}}function ji(n,e,t,i){const s=e===null,o=t===null,a=n&&!(s&&o)&&Ua(n,i);return a&&(s||Jt(e,a.left,a.right))&&(o||Jt(t,a.top,a.bottom))}function Wf(n){return n.topLeft||n.topRight||n.bottomLeft||n.bottomRight}function Vf(n,e){n.rect(e.x,e.y,e.w,e.h)}function Wi(n,e,t={}){const i=n.x!==t.x?-e:0,s=n.y!==t.y?-e:0,o=(n.x+n.w!==t.x+t.w?e:0)-i,r=(n.y+n.h!==t.y+t.h?e:0)-s;return{x:n.x+i,y:n.y+s,w:n.w+o,h:n.h+r,radius:n.radius}}class Qn extends It{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:t,options:{borderColor:i,backgroundColor:s}}=this,{inner:o,outer:r}=jf(this),a=Wf(r.radius)?hn:Vf;e.save(),(r.w!==o.w||r.h!==o.h)&&(e.beginPath(),a(e,Wi(r,t,o)),e.clip(),a(e,Wi(o,-t,r)),e.fillStyle=i,e.fill("evenodd")),e.beginPath(),a(e,Wi(o,t)),e.fillStyle=s,e.fill(),e.restore()}inRange(e,t,i){return ji(this,e,t,i)}inXRange(e,t){return ji(this,e,null,t)}inYRange(e,t){return ji(this,null,e,t)}getCenterPoint(e){const{x:t,y:i,base:s,horizontal:o}=this.getProps(["x","y","base","horizontal"],e);return{x:o?(t+s)/2:t,y:o?i:(i+s)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}E(Qn,"id","bar"),E(Qn,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),E(Qn,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Yf=Object.freeze({__proto__:null,ArcElement:Ke,BarElement:Qn,LineElement:se,PointElement:Zn});const cs=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],rr=cs.map(n=>n.replace("rgb(","rgba(").replace(")",", 0.5)"));function $a(n){return cs[n%cs.length]}function qa(n){return rr[n%rr.length]}function Uf(n,e){return n.borderColor=$a(e),n.backgroundColor=qa(e),++e}function $f(n,e){return n.backgroundColor=n.data.map(()=>$a(e++)),e}function qf(n,e){return n.backgroundColor=n.data.map(()=>qa(e++)),e}function Xf(n){let e=0;return(t,i)=>{const s=n.getDatasetMeta(i).controller;s instanceof _e?e=$f(t,e):s instanceof nn?e=qf(t,e):s&&(e=Uf(t,e))}}function ar(n){let e;for(e in n)if(n[e].borderColor||n[e].backgroundColor)return!0;return!1}function Kf(n){return n&&(n.borderColor||n.backgroundColor)}function Jf(){return nt.borderColor!=="rgba(0,0,0,0.1)"||nt.backgroundColor!=="rgba(0,0,0,0.1)"}var Gf={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(n,e,t){if(!t.enabled)return;const{data:{datasets:i},options:s}=n.config,{elements:o}=s,r=ar(i)||Kf(s)||o&&ar(o)||Jf();if(!t.forceOverride&&r)return;const a=Xf(n);i.forEach(a)}};function Zf(n,e,t,i,s){const o=s.samples||i;if(o>=t)return n.slice(e,e+t);const r=[],a=(t-2)/(o-2);let l=0;const c=e+t-1;let h=e,u,d,f,p,m;for(r[l++]=n[h],u=0;u<o-2;u++){let x=0,y=0,v;const S=Math.floor((u+1)*a)+1+e,k=Math.min(Math.floor((u+2)*a)+1,t)+e,D=k-S;for(v=S;v<k;v++)x+=n[v].x,y+=n[v].y;x/=D,y/=D;const C=Math.floor(u*a)+1+e,O=Math.min(Math.floor((u+1)*a)+1,t)+e,{x:P,y:F}=n[h];for(f=p=-1,v=C;v<O;v++)p=.5*Math.abs((P-x)*(n[v].y-F)-(P-n[v].x)*(y-F)),p>f&&(f=p,d=n[v],m=v);r[l++]=d,h=m}return r[l++]=n[c],r}function Qf(n,e,t,i){let s=0,o=0,r,a,l,c,h,u,d,f,p,m;const x=[],y=e+t-1,v=n[e].x,k=n[y].x-v;for(r=e;r<e+t;++r){a=n[r],l=(a.x-v)/k*i,c=a.y;const D=l|0;if(D===h)c<p?(p=c,u=r):c>m&&(m=c,d=r),s=(o*s+a.x)/++o;else{const C=r-1;if(!U(u)&&!U(d)){const O=Math.min(u,d),P=Math.max(u,d);O!==f&&O!==C&&x.push({...n[O],x:s}),P!==f&&P!==C&&x.push({...n[P],x:s})}r>0&&C!==f&&x.push(n[C]),x.push(a),h=D,o=0,p=m=c,u=d=f=r}}return x}function Xa(n){if(n._decimated){const e=n._data;delete n._decimated,delete n._data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,writable:!0,value:e})}}function lr(n){n.data.datasets.forEach(e=>{Xa(e)})}function tg(n,e){const t=e.length;let i=0,s;const{iScale:o}=n,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(i=ut(Gt(e,o.axis,r).lo,0,t-1)),c?s=ut(Gt(e,o.axis,a).hi+1,i,t)-i:s=t-i,{start:i,count:s}}var eg={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(n,e,t)=>{if(!t.enabled){lr(n);return}const i=n.width;n.data.datasets.forEach((s,o)=>{const{_data:r,indexAxis:a}=s,l=n.getDatasetMeta(o),c=r||s.data;if(qe([a,n.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=n.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||n.options.parsing)return;let{start:u,count:d}=tg(l,c);const f=t.threshold||4*i;if(d<=f){Xa(s);return}U(r)&&(s._data=c,delete s.data,Object.defineProperty(s,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(m){this._data=m}}));let p;switch(t.algorithm){case"lttb":p=Zf(c,u,d,i,t);break;case"min-max":p=Qf(c,u,d,i);break;default:throw new Error(`Unsupported decimation algorithm '${t.algorithm}'`)}s._decimated=p})},destroy(n){lr(n)}};function ng(n,e,t){const i=n.segments,s=n.points,o=e.points,r=[];for(const a of i){let{start:l,end:c}=a;c=Ns(l,c,s);const h=hs(t,s[l],s[c],a.loop);if(!e.segments){r.push({source:a,target:h,start:s[l],end:s[c]});continue}const u=Aa(e,h);for(const d of u){const f=hs(t,o[d.start],o[d.end],d.loop),p=Pa(a,s,f);for(const m of p)r.push({source:m,target:d,start:{[t]:cr(h,f,"start",Math.max)},end:{[t]:cr(h,f,"end",Math.min)}})}}return r}function hs(n,e,t,i){if(i)return;let s=e[n],o=t[n];return n==="angle"&&(s=Ot(s),o=Ot(o)),{property:n,start:s,end:o}}function ig(n,e){const{x:t=null,y:i=null}=n||{},s=e.points,o=[];return e.segments.forEach(({start:r,end:a})=>{a=Ns(r,a,s);const l=s[r],c=s[a];i!==null?(o.push({x:l.x,y:i}),o.push({x:c.x,y:i})):t!==null&&(o.push({x:t,y:l.y}),o.push({x:t,y:c.y}))}),o}function Ns(n,e,t){for(;e>n;e--){const i=t[e];if(!isNaN(i.x)&&!isNaN(i.y))break}return e}function cr(n,e,t,i){return n&&e?i(n[t],e[t]):n?n[t]:e?e[t]:0}function Ka(n,e){let t=[],i=!1;return et(n)?(i=!0,t=n):t=ig(n,e),t.length?new se({points:t,options:{tension:0},_loop:i,_fullLoop:i}):null}function hr(n){return n&&n.fill!==!1}function sg(n,e,t){let s=n[e].fill;const o=[e];let r;if(!t)return s;for(;s!==!1&&o.indexOf(s)===-1;){if(!ot(s))return s;if(r=n[s],!r)return!1;if(r.visible)return s;o.push(s),s=r.fill}return!1}function og(n,e,t){const i=cg(n);if(Y(i))return isNaN(i.value)?!1:i;let s=parseFloat(i);return ot(s)&&Math.floor(s)===s?rg(i[0],e,s,t):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function rg(n,e,t,i){return(n==="-"||n==="+")&&(t=e+t),t===e||t<0||t>=i?!1:t}function ag(n,e){let t=null;return n==="start"?t=e.bottom:n==="end"?t=e.top:Y(n)?t=e.getPixelForValue(n.value):e.getBasePixel&&(t=e.getBasePixel()),t}function lg(n,e,t){let i;return n==="start"?i=t:n==="end"?i=e.options.reverse?e.min:e.max:Y(n)?i=n.value:i=e.getBaseValue(),i}function cg(n){const e=n.options,t=e.fill;let i=z(t&&t.target,t);return i===void 0&&(i=!!e.backgroundColor),i===!1||i===null?!1:i===!0?"origin":i}function hg(n){const{scale:e,index:t,line:i}=n,s=[],o=i.segments,r=i.points,a=ug(e,t);a.push(Ka({x:null,y:e.bottom},i));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)dg(s,r[h],a)}return new se({points:s,options:{}})}function ug(n,e){const t=[],i=n.getMatchingVisibleMetas("line");for(let s=0;s<i.length;s++){const o=i[s];if(o.index===e)break;o.hidden||t.unshift(o.dataset)}return t}function dg(n,e,t){const i=[];for(let s=0;s<t.length;s++){const o=t[s],{first:r,last:a,point:l}=fg(o,e,"x");if(!(!l||r&&a)){if(r)i.unshift(l);else if(n.push(l),!a)break}}n.push(...i)}function fg(n,e,t){const i=n.interpolate(e,t);if(!i)return{};const s=i[t],o=n.segments,r=n.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],u=r[h.start][t],d=r[h.end][t];if(Jt(s,u,d)){a=s===u,l=s===d;break}}return{first:a,last:l,point:i}}class Ja{constructor(e){this.x=e.x,this.y=e.y,this.radius=e.radius}pathSegment(e,t,i){const{x:s,y:o,radius:r}=this;return t=t||{start:0,end:Z},e.arc(s,o,r,t.end,t.start,!0),!i.bounds}interpolate(e){const{x:t,y:i,radius:s}=this,o=e.angle;return{x:t+Math.cos(o)*s,y:i+Math.sin(o)*s,angle:o}}}function gg(n){const{chart:e,fill:t,line:i}=n;if(ot(t))return pg(e,t);if(t==="stack")return hg(n);if(t==="shape")return!0;const s=mg(n);return s instanceof Ja?s:Ka(s,i)}function pg(n,e){const t=n.getDatasetMeta(e);return t&&n.isDatasetVisible(e)?t.dataset:null}function mg(n){return(n.scale||{}).getPointPositionForValue?xg(n):bg(n)}function bg(n){const{scale:e={},fill:t}=n,i=ag(t,e);if(ot(i)){const s=e.isHorizontal();return{x:s?i:null,y:s?null:i}}return null}function xg(n){const{scale:e,fill:t}=n,i=e.options,s=e.getLabels().length,o=i.reverse?e.max:e.min,r=lg(t,e,o),a=[];if(i.grid.circular){const l=e.getPointPositionForValue(0,o);return new Ja({x:l.x,y:l.y,radius:e.getDistanceFromCenterForValue(r)})}for(let l=0;l<s;++l)a.push(e.getPointPositionForValue(l,r));return a}function Vi(n,e,t){const i=gg(e),{line:s,scale:o,axis:r}=e,a=s.options,l=a.fill,c=a.backgroundColor,{above:h=c,below:u=c}=l||{};i&&s.points.length&&(xi(n,t),yg(n,{line:s,target:i,above:h,below:u,area:t,scale:o,axis:r}),yi(n))}function yg(n,e){const{line:t,target:i,above:s,below:o,area:r,scale:a}=e,l=t._loop?"angle":e.axis;n.save(),l==="x"&&o!==s&&(ur(n,i,r.top),dr(n,{line:t,target:i,color:s,scale:a,property:l}),n.restore(),n.save(),ur(n,i,r.bottom)),dr(n,{line:t,target:i,color:o,scale:a,property:l}),n.restore()}function ur(n,e,t){const{segments:i,points:s}=e;let o=!0,r=!1;n.beginPath();for(const a of i){const{start:l,end:c}=a,h=s[l],u=s[Ns(l,c,s)];o?(n.moveTo(h.x,h.y),o=!1):(n.lineTo(h.x,t),n.lineTo(h.x,h.y)),r=!!e.pathSegment(n,a,{move:r}),r?n.closePath():n.lineTo(u.x,t)}n.lineTo(e.first().x,t),n.closePath(),n.clip()}function dr(n,e){const{line:t,target:i,property:s,color:o,scale:r}=e,a=ng(t,i,s);for(const{source:l,target:c,start:h,end:u}of a){const{style:{backgroundColor:d=o}={}}=l,f=i!==!0;n.save(),n.fillStyle=d,_g(n,r,f&&hs(s,h,u)),n.beginPath();const p=!!t.pathSegment(n,l);let m;if(f){p?n.closePath():fr(n,i,u,s);const x=!!i.pathSegment(n,c,{move:p,reverse:!0});m=p&&x,m||fr(n,i,h,s)}n.closePath(),n.fill(m?"evenodd":"nonzero"),n.restore()}}function _g(n,e,t){const{top:i,bottom:s}=e.chart.chartArea,{property:o,start:r,end:a}=t||{};o==="x"&&(n.beginPath(),n.rect(r,i,a-r,s-i),n.clip())}function fr(n,e,t,i){const s=e.interpolate(t,i);s&&n.lineTo(s.x,s.y)}var vg={id:"filler",afterDatasetsUpdate(n,e,t){const i=(n.data.datasets||[]).length,s=[];let o,r,a,l;for(r=0;r<i;++r)o=n.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof se&&(l={visible:n.isDatasetVisible(r),index:r,fill:og(a,r,i),chart:n,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,s.push(l);for(r=0;r<i;++r)l=s[r],!(!l||l.fill===!1)&&(l.fill=sg(s,r,t.propagate))},beforeDraw(n,e,t){const i=t.drawTime==="beforeDraw",s=n.getSortedVisibleDatasetMetas(),o=n.chartArea;for(let r=s.length-1;r>=0;--r){const a=s[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),i&&a.fill&&Vi(n.ctx,a,o))}},beforeDatasetsDraw(n,e,t){if(t.drawTime!=="beforeDatasetsDraw")return;const i=n.getSortedVisibleDatasetMetas();for(let s=i.length-1;s>=0;--s){const o=i[s].$filler;hr(o)&&Vi(n.ctx,o,n.chartArea)}},beforeDatasetDraw(n,e,t){const i=e.meta.$filler;!hr(i)||t.drawTime!=="beforeDatasetDraw"||Vi(n.ctx,i,n.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const gr=(n,e)=>{let{boxHeight:t=e,boxWidth:i=e}=n;return n.usePointStyle&&(t=Math.min(t,e),i=n.pointStyleWidth||Math.min(i,e)),{boxWidth:i,boxHeight:t,itemHeight:Math.max(e,t)}},wg=(n,e)=>n!==null&&e!==null&&n.datasetIndex===e.datasetIndex&&n.index===e.index;class pr extends It{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t,i){this.maxWidth=e,this.maxHeight=t,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let t=G(e.generateLabels,[this.chart],this)||[];e.filter&&(t=t.filter(i=>e.filter(i,this.chart.data))),e.sort&&(t=t.sort((i,s)=>e.sort(i,s,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){const{options:e,ctx:t}=this;if(!e.display){this.width=this.height=0;return}const i=e.labels,s=ht(i.font),o=s.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=gr(i,o);let c,h;t.font=s.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,s,a,l)+10),this.width=Math.min(c,e.maxWidth||this.maxWidth),this.height=Math.min(h,e.maxHeight||this.maxHeight)}_fitRows(e,t,i,s){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=s+a;let u=e;o.textAlign="left",o.textBaseline="middle";let d=-1,f=-h;return this.legendItems.forEach((p,m)=>{const x=i+t/2+o.measureText(p.text).width;(m===0||c[c.length-1]+x+2*a>r)&&(u+=h,c[c.length-(m>0?0:1)]=0,f+=h,d++),l[m]={left:0,top:f,row:d,width:x,height:s},c[c.length-1]+=x+a}),u}_fitCols(e,t,i,s){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-e;let u=a,d=0,f=0,p=0,m=0;return this.legendItems.forEach((x,y)=>{const{itemWidth:v,itemHeight:S}=Mg(i,t,o,x,s);y>0&&f+S+2*a>h&&(u+=d+a,c.push({width:d,height:f}),p+=d+a,m++,d=f=0),l[y]={left:p,top:f,col:m,width:v,height:S},d=Math.max(d,v),f+=S+a}),u+=d,c.push({width:d,height:f}),u}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:i,labels:{padding:s},rtl:o}}=this,r=Ee(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=dt(i,this.left+s,this.right-this.lineWidths[a]);for(const c of t)a!==c.row&&(a=c.row,l=dt(i,this.left+s,this.right-this.lineWidths[a])),c.top+=this.top+e+s,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+s}else{let a=0,l=dt(i,this.top+e+s,this.bottom-this.columnSizes[a].height);for(const c of t)c.col!==a&&(a=c.col,l=dt(i,this.top+e+s,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+s,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+s}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;xi(e,this),this._draw(),yi(e)}}_draw(){const{options:e,columnSizes:t,lineWidths:i,ctx:s}=this,{align:o,labels:r}=e,a=nt.color,l=Ee(e.rtl,this.left,this.width),c=ht(r.font),{padding:h}=r,u=c.size,d=u/2;let f;this.drawTitle(),s.textAlign=l.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=c.string;const{boxWidth:p,boxHeight:m,itemHeight:x}=gr(r,u),y=function(C,O,P){if(isNaN(p)||p<=0||isNaN(m)||m<0)return;s.save();const F=z(P.lineWidth,1);if(s.fillStyle=z(P.fillStyle,a),s.lineCap=z(P.lineCap,"butt"),s.lineDashOffset=z(P.lineDashOffset,0),s.lineJoin=z(P.lineJoin,"miter"),s.lineWidth=F,s.strokeStyle=z(P.strokeStyle,a),s.setLineDash(z(P.lineDash,[])),r.usePointStyle){const N={radius:m*Math.SQRT2/2,pointStyle:P.pointStyle,rotation:P.rotation,borderWidth:F},H=l.xPlus(C,p/2),W=O+d;xa(s,N,H,W,r.pointStyleWidth&&p)}else{const N=O+Math.max((u-m)/2,0),H=l.leftForLtr(C,p),W=we(P.borderRadius);s.beginPath(),Object.values(W).some(tt=>tt!==0)?hn(s,{x:H,y:N,w:p,h:m,radius:W}):s.rect(H,N,p,m),s.fill(),F!==0&&s.stroke()}s.restore()},v=function(C,O,P){ke(s,P.text,C,O+x/2,c,{strikethrough:P.hidden,textAlign:l.textAlign(P.textAlign)})},S=this.isHorizontal(),k=this._computeTitleHeight();S?f={x:dt(o,this.left+h,this.right-i[0]),y:this.top+h+k,line:0}:f={x:this.left+h,y:dt(o,this.top+k+h,this.bottom-t[0].height),line:0},ka(this.ctx,e.textDirection);const D=x+h;this.legendItems.forEach((C,O)=>{s.strokeStyle=C.fontColor,s.fillStyle=C.fontColor;const P=s.measureText(C.text).width,F=l.textAlign(C.textAlign||(C.textAlign=r.textAlign)),N=p+d+P;let H=f.x,W=f.y;l.setWidth(this.width),S?O>0&&H+N+h>this.right&&(W=f.y+=D,f.line++,H=f.x=dt(o,this.left+h,this.right-i[f.line])):O>0&&W+D>this.bottom&&(H=f.x=H+t[f.line].width+h,f.line++,W=f.y=dt(o,this.top+k+h,this.bottom-t[f.line].height));const tt=l.x(H);if(y(tt,W,C),H=Wh(F,H+p+d,S?H+N:this.right,e.rtl),v(l.x(H),W,C),S)f.x+=N+h;else if(typeof C.text!="string"){const bt=c.lineHeight;f.y+=Ga(C,bt)+h}else f.y+=D}),Ca(this.ctx,e.textDirection)}drawTitle(){const e=this.options,t=e.title,i=ht(t.font),s=mt(t.padding);if(!t.display)return;const o=Ee(e.rtl,this.left,this.width),r=this.ctx,a=t.position,l=i.size/2,c=s.top+l;let h,u=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+c,u=dt(e.align,u,this.right-d);else{const p=this.columnSizes.reduce((m,x)=>Math.max(m,x.height),0);h=c+dt(e.align,this.top,this.bottom-p-e.labels.padding-this._computeTitleHeight())}const f=dt(a,u,u+d);r.textAlign=o.textAlign(Os(a)),r.textBaseline="middle",r.strokeStyle=t.color,r.fillStyle=t.color,r.font=i.string,ke(r,t.text,f,h,i)}_computeTitleHeight(){const e=this.options.title,t=ht(e.font),i=mt(e.padding);return e.display?t.lineHeight+i.height:0}_getLegendItemAt(e,t){let i,s,o;if(Jt(e,this.left,this.right)&&Jt(t,this.top,this.bottom)){for(o=this.legendHitBoxes,i=0;i<o.length;++i)if(s=o[i],Jt(e,s.left,s.left+s.width)&&Jt(t,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(e){const t=this.options;if(!kg(e.type,t))return;const i=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const s=this._hoveredItem,o=wg(s,i);s&&!o&&G(t.onLeave,[e,s,this],this),this._hoveredItem=i,i&&!o&&G(t.onHover,[e,i,this],this)}else i&&G(t.onClick,[e,i,this],this)}}function Mg(n,e,t,i,s){const o=Dg(i,n,e,t),r=Sg(s,i,e.lineHeight);return{itemWidth:o,itemHeight:r}}function Dg(n,e,t,i){let s=n.text;return s&&typeof s!="string"&&(s=s.reduce((o,r)=>o.length>r.length?o:r)),e+t.size/2+i.measureText(s).width}function Sg(n,e,t){let i=n;return typeof e.text!="string"&&(i=Ga(e,t)),i}function Ga(n,e){const t=n.text?n.text.length:0;return e*t}function kg(n,e){return!!((n==="mousemove"||n==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(n==="click"||n==="mouseup"))}var Cg={id:"legend",_element:pr,start(n,e,t){const i=n.legend=new pr({ctx:n.ctx,options:t,chart:n});pt.configure(n,i,t),pt.addBox(n,i)},stop(n){pt.removeBox(n,n.legend),delete n.legend},beforeUpdate(n,e,t){const i=n.legend;pt.configure(n,i,t),i.options=t},afterUpdate(n){const e=n.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(n,e){e.replay||n.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(n,e,t){const i=e.datasetIndex,s=t.chart;s.isDatasetVisible(i)?(s.hide(i),e.hidden=!0):(s.show(i),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:n=>n.chart.options.color,boxWidth:40,padding:10,generateLabels(n){const e=n.data.datasets,{labels:{usePointStyle:t,pointStyle:i,textAlign:s,color:o,useBorderRadius:r,borderRadius:a}}=n.legend.options;return n._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(t?0:void 0),h=mt(c.borderWidth);return{text:e[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:i||c.pointStyle,rotation:c.rotation,textAlign:s||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:n=>n.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:n=>!n.startsWith("on"),labels:{_scriptable:n=>!["generateLabels","filter","sort"].includes(n)}}};class Bs extends It{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=t;const s=et(i.text)?i.text.length:1;this._padding=mt(i.padding);const o=s*ht(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:t,left:i,bottom:s,right:o,options:r}=this,a=r.align;let l=0,c,h,u;return this.isHorizontal()?(h=dt(a,i,o),u=t+e,c=o-i):(r.position==="left"?(h=i+e,u=dt(a,s,t),l=Q*-.5):(h=o-e,u=dt(a,t,s),l=Q*.5),c=s-t),{titleX:h,titleY:u,maxWidth:c,rotation:l}}draw(){const e=this.ctx,t=this.options;if(!t.display)return;const i=ht(t.font),o=i.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);ke(e,t.text,0,0,i,{color:t.color,maxWidth:l,rotation:c,textAlign:Os(t.align),textBaseline:"middle",translation:[r,a]})}}function Og(n,e){const t=new Bs({ctx:n.ctx,options:e,chart:n});pt.configure(n,t,e),pt.addBox(n,t),n.titleBlock=t}var Pg={id:"title",_element:Bs,start(n,e,t){Og(n,t)},stop(n){const e=n.titleBlock;pt.removeBox(n,e),delete n.titleBlock},beforeUpdate(n,e,t){const i=n.titleBlock;pt.configure(n,i,t),i.options=t},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Nn=new WeakMap;var Ag={id:"subtitle",start(n,e,t){const i=new Bs({ctx:n.ctx,options:t,chart:n});pt.configure(n,i,t),pt.addBox(n,i),Nn.set(n,i)},stop(n){pt.removeBox(n,Nn.get(n)),Nn.delete(n)},beforeUpdate(n,e,t){const i=Nn.get(n);pt.configure(n,i,t),i.options=t},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Je={average(n){if(!n.length)return!1;let e,t,i=new Set,s=0,o=0;for(e=0,t=n.length;e<t;++e){const a=n[e].element;if(a&&a.hasValue()){const l=a.tooltipPosition();i.add(l.x),s+=l.y,++o}}return o===0||i.size===0?!1:{x:[...i].reduce((a,l)=>a+l)/i.size,y:s/o}},nearest(n,e){if(!n.length)return!1;let t=e.x,i=e.y,s=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=n.length;o<r;++o){const l=n[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=ns(e,c);h<s&&(s=h,a=l)}}if(a){const l=a.tooltipPosition();t=l.x,i=l.y}return{x:t,y:i}}};function Ht(n,e){return e&&(et(e)?Array.prototype.push.apply(n,e):n.push(e)),n}function qt(n){return(typeof n=="string"||n instanceof String)&&n.indexOf(`
`)>-1?n.split(`
`):n}function Eg(n,e){const{element:t,datasetIndex:i,index:s}=e,o=n.getDatasetMeta(i).controller,{label:r,value:a}=o.getLabelAndValue(s);return{chart:n,label:r,parsed:o.getParsed(s),raw:n.data.datasets[i].data[s],formattedValue:a,dataset:o.getDataset(),dataIndex:s,datasetIndex:i,element:t}}function mr(n,e){const t=n.chart.ctx,{body:i,footer:s,title:o}=n,{boxWidth:r,boxHeight:a}=e,l=ht(e.bodyFont),c=ht(e.titleFont),h=ht(e.footerFont),u=o.length,d=s.length,f=i.length,p=mt(e.padding);let m=p.height,x=0,y=i.reduce((k,D)=>k+D.before.length+D.lines.length+D.after.length,0);if(y+=n.beforeBody.length+n.afterBody.length,u&&(m+=u*c.lineHeight+(u-1)*e.titleSpacing+e.titleMarginBottom),y){const k=e.displayColors?Math.max(a,l.lineHeight):l.lineHeight;m+=f*k+(y-f)*l.lineHeight+(y-1)*e.bodySpacing}d&&(m+=e.footerMarginTop+d*h.lineHeight+(d-1)*e.footerSpacing);let v=0;const S=function(k){x=Math.max(x,t.measureText(k).width+v)};return t.save(),t.font=c.string,X(n.title,S),t.font=l.string,X(n.beforeBody.concat(n.afterBody),S),v=e.displayColors?r+2+e.boxPadding:0,X(i,k=>{X(k.before,S),X(k.lines,S),X(k.after,S)}),v=0,t.font=h.string,X(n.footer,S),t.restore(),x+=p.width,{width:x,height:m}}function Tg(n,e){const{y:t,height:i}=e;return t<i/2?"top":t>n.height-i/2?"bottom":"center"}function Rg(n,e,t,i){const{x:s,width:o}=i,r=t.caretSize+t.caretPadding;if(n==="left"&&s+o+r>e.width||n==="right"&&s-o-r<0)return!0}function Lg(n,e,t,i){const{x:s,width:o}=t,{width:r,chartArea:{left:a,right:l}}=n;let c="center";return i==="center"?c=s<=(a+l)/2?"left":"right":s<=o/2?c="left":s>=r-o/2&&(c="right"),Rg(c,n,e,t)&&(c="center"),c}function br(n,e,t){const i=t.yAlign||e.yAlign||Tg(n,t);return{xAlign:t.xAlign||e.xAlign||Lg(n,e,t,i),yAlign:i}}function Fg(n,e){let{x:t,width:i}=n;return e==="right"?t-=i:e==="center"&&(t-=i/2),t}function Ig(n,e,t){let{y:i,height:s}=n;return e==="top"?i+=t:e==="bottom"?i-=s+t:i-=s/2,i}function xr(n,e,t,i){const{caretSize:s,caretPadding:o,cornerRadius:r}=n,{xAlign:a,yAlign:l}=t,c=s+o,{topLeft:h,topRight:u,bottomLeft:d,bottomRight:f}=we(r);let p=Fg(e,a);const m=Ig(e,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(h,d)+s:a==="right"&&(p+=Math.max(u,f)+s),{x:ut(p,0,i.width-e.width),y:ut(m,0,i.height-e.height)}}function Bn(n,e,t){const i=mt(t.padding);return e==="center"?n.x+n.width/2:e==="right"?n.x+n.width-i.right:n.x+i.left}function yr(n){return Ht([],qt(n))}function Ng(n,e,t){return ce(n,{tooltip:e,tooltipItems:t,type:"tooltip"})}function _r(n,e){const t=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return t?n.override(t):n}const Za={beforeTitle:Ut,title(n){if(n.length>0){const e=n[0],t=e.chart.data.labels,i=t?t.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(i>0&&e.dataIndex<i)return t[e.dataIndex]}return""},afterTitle:Ut,beforeBody:Ut,beforeLabel:Ut,label(n){if(this&&this.options&&this.options.mode==="dataset")return n.label+": "+n.formattedValue||n.formattedValue;let e=n.dataset.label||"";e&&(e+=": ");const t=n.formattedValue;return U(t)||(e+=t),e},labelColor(n){const t=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(n){const t=n.chart.getDatasetMeta(n.datasetIndex).controller.getStyle(n.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:Ut,afterBody:Ut,beforeFooter:Ut,footer:Ut,afterFooter:Ut};function _t(n,e,t,i){const s=n[e].call(t,i);return typeof s>"u"?Za[e].call(t,i):s}class us extends It{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const t=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&t.options.animation&&i.animations,o=new Ea(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Ng(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,t){const{callbacks:i}=t,s=_t(i,"beforeTitle",this,e),o=_t(i,"title",this,e),r=_t(i,"afterTitle",this,e);let a=[];return a=Ht(a,qt(s)),a=Ht(a,qt(o)),a=Ht(a,qt(r)),a}getBeforeBody(e,t){return yr(_t(t.callbacks,"beforeBody",this,e))}getBody(e,t){const{callbacks:i}=t,s=[];return X(e,o=>{const r={before:[],lines:[],after:[]},a=_r(i,o);Ht(r.before,qt(_t(a,"beforeLabel",this,o))),Ht(r.lines,_t(a,"label",this,o)),Ht(r.after,qt(_t(a,"afterLabel",this,o))),s.push(r)}),s}getAfterBody(e,t){return yr(_t(t.callbacks,"afterBody",this,e))}getFooter(e,t){const{callbacks:i}=t,s=_t(i,"beforeFooter",this,e),o=_t(i,"footer",this,e),r=_t(i,"afterFooter",this,e);let a=[];return a=Ht(a,qt(s)),a=Ht(a,qt(o)),a=Ht(a,qt(r)),a}_createItems(e){const t=this._active,i=this.chart.data,s=[],o=[],r=[];let a=[],l,c;for(l=0,c=t.length;l<c;++l)a.push(Eg(this.chart,t[l]));return e.filter&&(a=a.filter((h,u,d)=>e.filter(h,u,d,i))),e.itemSort&&(a=a.sort((h,u)=>e.itemSort(h,u,i))),X(a,h=>{const u=_r(e.callbacks,h);s.push(_t(u,"labelColor",this,h)),o.push(_t(u,"labelPointStyle",this,h)),r.push(_t(u,"labelTextColor",this,h))}),this.labelColors=s,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(e,t){const i=this.options.setContext(this.getContext()),s=this._active;let o,r=[];if(!s.length)this.opacity!==0&&(o={opacity:0});else{const a=Je[i.position].call(this,s,this._eventPosition);r=this._createItems(i),this.title=this.getTitle(r,i),this.beforeBody=this.getBeforeBody(r,i),this.body=this.getBody(r,i),this.afterBody=this.getAfterBody(r,i),this.footer=this.getFooter(r,i);const l=this._size=mr(this,i),c=Object.assign({},a,l),h=br(this.chart,i,c),u=xr(i,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:u.x,y:u.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),e&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:t})}drawCaret(e,t,i,s){const o=this.getCaretPosition(e,i,s);t.lineTo(o.x1,o.y1),t.lineTo(o.x2,o.y2),t.lineTo(o.x3,o.y3)}getCaretPosition(e,t,i){const{xAlign:s,yAlign:o}=this,{caretSize:r,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:u}=we(a),{x:d,y:f}=e,{width:p,height:m}=t;let x,y,v,S,k,D;return o==="center"?(k=f+m/2,s==="left"?(x=d,y=x-r,S=k+r,D=k-r):(x=d+p,y=x+r,S=k-r,D=k+r),v=x):(s==="left"?y=d+Math.max(l,h)+r:s==="right"?y=d+p-Math.max(c,u)-r:y=this.caretX,o==="top"?(S=f,k=S-r,x=y-r,v=y+r):(S=f+m,k=S+r,x=y+r,v=y-r),D=S),{x1:x,x2:y,x3:v,y1:S,y2:k,y3:D}}drawTitle(e,t,i){const s=this.title,o=s.length;let r,a,l;if(o){const c=Ee(i.rtl,this.x,this.width);for(e.x=Bn(this,i.titleAlign,i),t.textAlign=c.textAlign(i.titleAlign),t.textBaseline="middle",r=ht(i.titleFont),a=i.titleSpacing,t.fillStyle=i.titleColor,t.font=r.string,l=0;l<o;++l)t.fillText(s[l],c.x(e.x),e.y+r.lineHeight/2),e.y+=r.lineHeight+a,l+1===o&&(e.y+=i.titleMarginBottom-a)}}_drawColorBox(e,t,i,s,o){const r=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c}=o,h=ht(o.bodyFont),u=Bn(this,"left",o),d=s.x(u),f=l<h.lineHeight?(h.lineHeight-l)/2:0,p=t.y+f;if(o.usePointStyle){const m={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},x=s.leftForLtr(d,c)+c/2,y=p+l/2;e.strokeStyle=o.multiKeyBackground,e.fillStyle=o.multiKeyBackground,ss(e,m,x,y),e.strokeStyle=r.borderColor,e.fillStyle=r.backgroundColor,ss(e,m,x,y)}else{e.lineWidth=Y(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,e.strokeStyle=r.borderColor,e.setLineDash(r.borderDash||[]),e.lineDashOffset=r.borderDashOffset||0;const m=s.leftForLtr(d,c),x=s.leftForLtr(s.xPlus(d,1),c-2),y=we(r.borderRadius);Object.values(y).some(v=>v!==0)?(e.beginPath(),e.fillStyle=o.multiKeyBackground,hn(e,{x:m,y:p,w:c,h:l,radius:y}),e.fill(),e.stroke(),e.fillStyle=r.backgroundColor,e.beginPath(),hn(e,{x,y:p+1,w:c-2,h:l-2,radius:y}),e.fill()):(e.fillStyle=o.multiKeyBackground,e.fillRect(m,p,c,l),e.strokeRect(m,p,c,l),e.fillStyle=r.backgroundColor,e.fillRect(x,p+1,c-2,l-2))}e.fillStyle=this.labelTextColors[i]}drawBody(e,t,i){const{body:s}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=i,u=ht(i.bodyFont);let d=u.lineHeight,f=0;const p=Ee(i.rtl,this.x,this.width),m=function(P){t.fillText(P,p.x(e.x+f),e.y+d/2),e.y+=d+o},x=p.textAlign(r);let y,v,S,k,D,C,O;for(t.textAlign=r,t.textBaseline="middle",t.font=u.string,e.x=Bn(this,x,i),t.fillStyle=i.bodyColor,X(this.beforeBody,m),f=a&&x!=="right"?r==="center"?c/2+h:c+2+h:0,k=0,C=s.length;k<C;++k){for(y=s[k],v=this.labelTextColors[k],t.fillStyle=v,X(y.before,m),S=y.lines,a&&S.length&&(this._drawColorBox(t,e,k,p,i),d=Math.max(u.lineHeight,l)),D=0,O=S.length;D<O;++D)m(S[D]),d=u.lineHeight;X(y.after,m)}f=0,d=u.lineHeight,X(this.afterBody,m),e.y-=o}drawFooter(e,t,i){const s=this.footer,o=s.length;let r,a;if(o){const l=Ee(i.rtl,this.x,this.width);for(e.x=Bn(this,i.footerAlign,i),e.y+=i.footerMarginTop,t.textAlign=l.textAlign(i.footerAlign),t.textBaseline="middle",r=ht(i.footerFont),t.fillStyle=i.footerColor,t.font=r.string,a=0;a<o;++a)t.fillText(s[a],l.x(e.x),e.y+r.lineHeight/2),e.y+=r.lineHeight+i.footerSpacing}}drawBackground(e,t,i,s){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=e,{width:c,height:h}=i,{topLeft:u,topRight:d,bottomLeft:f,bottomRight:p}=we(s.cornerRadius);t.fillStyle=s.backgroundColor,t.strokeStyle=s.borderColor,t.lineWidth=s.borderWidth,t.beginPath(),t.moveTo(a+u,l),r==="top"&&this.drawCaret(e,t,i,s),t.lineTo(a+c-d,l),t.quadraticCurveTo(a+c,l,a+c,l+d),r==="center"&&o==="right"&&this.drawCaret(e,t,i,s),t.lineTo(a+c,l+h-p),t.quadraticCurveTo(a+c,l+h,a+c-p,l+h),r==="bottom"&&this.drawCaret(e,t,i,s),t.lineTo(a+f,l+h),t.quadraticCurveTo(a,l+h,a,l+h-f),r==="center"&&o==="left"&&this.drawCaret(e,t,i,s),t.lineTo(a,l+u),t.quadraticCurveTo(a,l,a+u,l),t.closePath(),t.fill(),s.borderWidth>0&&t.stroke()}_updateAnimationTarget(e){const t=this.chart,i=this.$animations,s=i&&i.x,o=i&&i.y;if(s||o){const r=Je[e.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=mr(this,e),l=Object.assign({},r,this._size),c=br(t,e,l),h=xr(e,l,c,t);(s._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(e){const t=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(t);const s={width:this.width,height:this.height},o={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const r=mt(t.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;t.enabled&&a&&(e.save(),e.globalAlpha=i,this.drawBackground(o,e,s,t),ka(e,t.textDirection),o.y+=r.top,this.drawTitle(o,e,t),this.drawBody(o,e,t),this.drawFooter(o,e,t),Ca(e,t.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,t){const i=this._active,s=e.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!ni(i,s),r=this._positionChanged(s,t);(o||r)&&(this._active=s,this._eventPosition=t,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,t,i=!0){if(t&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,o=this._active||[],r=this._getActiveElements(e,o,t,i),a=this._positionChanged(r,e),l=t||!ni(r,o)||a;return l&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,t))),l}_getActiveElements(e,t,i,s){const o=this.options;if(e.type==="mouseout")return[];if(!s)return t.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(e,o.mode,o,i);return o.reverse&&r.reverse(),r}_positionChanged(e,t){const{caretX:i,caretY:s,options:o}=this,r=Je[o.position].call(this,e,t);return r!==!1&&(i!==r.x||s!==r.y)}}E(us,"positioners",Je);var Bg={id:"tooltip",_element:us,positioners:Je,afterInit(n,e,t){t&&(n.tooltip=new us({chart:n,options:t}))},beforeUpdate(n,e,t){n.tooltip&&n.tooltip.initialize(t)},reset(n,e,t){n.tooltip&&n.tooltip.initialize(t)},afterDraw(n){const e=n.tooltip;if(e&&e._willRender()){const t={tooltip:e};if(n.notifyPlugins("beforeTooltipDraw",{...t,cancelable:!0})===!1)return;e.draw(n.ctx),n.notifyPlugins("afterTooltipDraw",t)}},afterEvent(n,e){if(n.tooltip){const t=e.replay;n.tooltip.handleEvent(e.event,t,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(n,e)=>e.bodyFont.size,boxWidth:(n,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Za},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:n=>n!=="filter"&&n!=="itemSort"&&n!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},zg=Object.freeze({__proto__:null,Colors:Gf,Decimation:eg,Filler:vg,Legend:Cg,SubTitle:Ag,Title:Pg,Tooltip:Bg});const Hg=(n,e,t,i)=>(typeof e=="string"?(t=n.push(e)-1,i.unshift({index:t,label:e})):isNaN(e)&&(t=null),t);function jg(n,e,t,i){const s=n.indexOf(e);if(s===-1)return Hg(n,e,t,i);const o=n.lastIndexOf(e);return s!==o?t:s}const Wg=(n,e)=>n===null?null:ut(Math.round(n),0,e);function vr(n){const e=this.getLabels();return n>=0&&n<e.length?e[n]:n}class ds extends Ce{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const t=this._addedLabels;if(t.length){const i=this.getLabels();for(const{index:s,label:o}of t)i[s]===o&&i.splice(s,1);this._addedLabels=[]}super.init(e)}parse(e,t){if(U(e))return null;const i=this.getLabels();return t=isFinite(t)&&i[t]===e?t:jg(i,e,z(t,e),this._addedLabels),Wg(t,i.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:t}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(i=0),t||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const e=this.min,t=this.max,i=this.options.offset,s=[];let o=this.getLabels();o=e===0&&t===o.length-1?o:o.slice(e,t+1),this._valueRange=Math.max(o.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let r=e;r<=t;r++)s.push({value:r});return s}getLabelForValue(e){return vr.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}E(ds,"id","category"),E(ds,"defaults",{ticks:{callback:vr}});function Vg(n,e){const t=[],{bounds:s,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:u,includeBounds:d}=n,f=o||1,p=h-1,{min:m,max:x}=e,y=!U(r),v=!U(a),S=!U(c),k=(x-m)/(u+1);let D=mo((x-m)/p/f)*f,C,O,P,F;if(D<1e-14&&!y&&!v)return[{value:m},{value:x}];F=Math.ceil(x/D)-Math.floor(m/D),F>p&&(D=mo(F*D/p/f)*f),U(l)||(C=Math.pow(10,l),D=Math.ceil(D*C)/C),s==="ticks"?(O=Math.floor(m/D)*D,P=Math.ceil(x/D)*D):(O=m,P=x),y&&v&&o&&Fh((a-r)/o,D/1e3)?(F=Math.round(Math.min((a-r)/D,h)),D=(a-r)/F,O=r,P=a):S?(O=y?r:O,P=v?a:P,F=c-1,D=(P-O)/F):(F=(P-O)/D,Qe(F,Math.round(F),D/1e3)?F=Math.round(F):F=Math.ceil(F));const N=Math.max(bo(D),bo(O));C=Math.pow(10,U(l)?N:l),O=Math.round(O*C)/C,P=Math.round(P*C)/C;let H=0;for(y&&(d&&O!==r?(t.push({value:r}),O<r&&H++,Qe(Math.round((O+H*D)*C)/C,r,wr(r,k,n))&&H++):O<r&&H++);H<F;++H){const W=Math.round((O+H*D)*C)/C;if(v&&W>a)break;t.push({value:W})}return v&&d&&P!==a?t.length&&Qe(t[t.length-1].value,a,wr(a,k,n))?t[t.length-1].value=a:t.push({value:a}):(!v||P===a)&&t.push({value:P}),t}function wr(n,e,{horizontal:t,minRotation:i}){const s=Lt(i),o=(t?Math.sin(s):Math.cos(s))||.001,r=.75*e*(""+n).length;return Math.min(e/o,r)}class ci extends Ce{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,t){return U(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:t,maxDefined:i}=this.getUserBounds();let{min:s,max:o}=this;const r=l=>s=t?s:l,a=l=>o=i?o:l;if(e){const l=Wt(s),c=Wt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(s===o){let l=o===0?1:Math.abs(o*.05);a(o+l),e||r(s-l)}this.min=s,this.max=o}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:t,stepSize:i}=e,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),t=t||11),t&&(s=Math.min(t,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,t=e.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:e.bounds,min:e.min,max:e.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:t.includeBounds!==!1},o=this._range||this,r=Vg(s,o);return e.bounds==="ticks"&&ca(r,this,"value"),e.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const e=this.ticks;let t=this.min,i=this.max;if(super.configure(),this.options.offset&&e.length){const s=(i-t)/Math.max(e.length-1,1)/2;t-=s,i+=s}this._startValue=t,this._endValue=i,this._valueRange=i-t}getLabelForValue(e){return xn(e,this.chart.options.locale,this.options.ticks.format)}}class fs extends ci{determineDataLimits(){const{min:e,max:t}=this.getMinMax(!0);this.min=ot(e)?e:0,this.max=ot(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),t=e?this.width:this.height,i=Lt(this.options.ticks.minRotation),s=(e?Math.sin(i):Math.cos(i))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,o.lineHeight/s))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}E(fs,"id","linear"),E(fs,"defaults",{ticks:{callback:bi.formatters.numeric}});const dn=n=>Math.floor(ie(n)),pe=(n,e)=>Math.pow(10,dn(n)+e);function Mr(n){return n/Math.pow(10,dn(n))===1}function Dr(n,e,t){const i=Math.pow(10,t),s=Math.floor(n/i);return Math.ceil(e/i)-s}function Yg(n,e){const t=e-n;let i=dn(t);for(;Dr(n,e,i)>10;)i++;for(;Dr(n,e,i)<10;)i--;return Math.min(i,dn(n))}function Ug(n,{min:e,max:t}){e=Ct(n.min,e);const i=[],s=dn(e);let o=Yg(e,t),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=s>o?Math.pow(10,s):0,c=Math.round((e-l)*r)/r,h=Math.floor((e-l)/a/10)*a*10;let u=Math.floor((c-h)/Math.pow(10,o)),d=Ct(n.min,Math.round((l+h+u*Math.pow(10,o))*r)/r);for(;d<t;)i.push({value:d,major:Mr(d),significand:u}),u>=10?u=u<15?15:20:u++,u>=20&&(o++,u=2,r=o>=0?1:r),d=Math.round((l+h+u*Math.pow(10,o))*r)/r;const f=Ct(n.max,d);return i.push({value:f,major:Mr(f),significand:u}),i}class gs extends Ce{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(e,t){const i=ci.prototype.parse.apply(this,[e,t]);if(i===0){this._zero=!0;return}return ot(i)&&i>0?i:null}determineDataLimits(){const{min:e,max:t}=this.getMinMax(!0);this.min=ot(e)?Math.max(0,e):null,this.max=ot(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!ot(this._userMin)&&(this.min=e===pe(this.min,0)?pe(this.min,-1):pe(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:e,maxDefined:t}=this.getUserBounds();let i=this.min,s=this.max;const o=a=>i=e?i:a,r=a=>s=t?s:a;i===s&&(i<=0?(o(1),r(10)):(o(pe(i,-1)),r(pe(s,1)))),i<=0&&o(pe(s,-1)),s<=0&&r(pe(i,1)),this.min=i,this.max=s}buildTicks(){const e=this.options,t={min:this._userMin,max:this._userMax},i=Ug(t,this);return e.bounds==="ticks"&&ca(i,this,"value"),e.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(e){return e===void 0?"0":xn(e,this.chart.options.locale,this.options.ticks.format)}configure(){const e=this.min;super.configure(),this._startValue=ie(e),this._valueRange=ie(this.max)-ie(e)}getPixelForValue(e){return(e===void 0||e===0)&&(e=this.min),e===null||isNaN(e)?NaN:this.getPixelForDecimal(e===this.min?0:(ie(e)-this._startValue)/this._valueRange)}getValueForPixel(e){const t=this.getDecimalForPixel(e);return Math.pow(10,this._startValue+t*this._valueRange)}}E(gs,"id","logarithmic"),E(gs,"defaults",{ticks:{callback:bi.formatters.logarithmic,major:{enabled:!0}}});function ps(n){const e=n.ticks;if(e.display&&n.display){const t=mt(e.backdropPadding);return z(e.font&&e.font.size,nt.font.size)+t.height}return 0}function $g(n,e,t){return t=et(t)?t:[t],{w:Zh(n,e.string,t),h:t.length*e.lineHeight}}function Sr(n,e,t,i,s){return n===i||n===s?{start:e-t/2,end:e+t/2}:n<i||n>s?{start:e-t,end:e}:{start:e,end:e+t}}function qg(n){const e={l:n.left+n._padding.left,r:n.right-n._padding.right,t:n.top+n._padding.top,b:n.bottom-n._padding.bottom},t=Object.assign({},e),i=[],s=[],o=n._pointLabels.length,r=n.options.pointLabels,a=r.centerPointLabels?Q/o:0;for(let l=0;l<o;l++){const c=r.setContext(n.getPointLabelContext(l));s[l]=c.padding;const h=n.getPointPosition(l,n.drawingArea+s[l],a),u=ht(c.font),d=$g(n.ctx,u,n._pointLabels[l]);i[l]=d;const f=Ot(n.getIndexAngle(l)+a),p=Math.round(ks(f)),m=Sr(p,h.x,d.w,0,180),x=Sr(p,h.y,d.h,90,270);Xg(t,e,f,m,x)}n.setCenterPoint(e.l-t.l,t.r-e.r,e.t-t.t,t.b-e.b),n._pointLabelItems=Gg(n,i,s)}function Xg(n,e,t,i,s){const o=Math.abs(Math.sin(t)),r=Math.abs(Math.cos(t));let a=0,l=0;i.start<e.l?(a=(e.l-i.start)/o,n.l=Math.min(n.l,e.l-a)):i.end>e.r&&(a=(i.end-e.r)/o,n.r=Math.max(n.r,e.r+a)),s.start<e.t?(l=(e.t-s.start)/r,n.t=Math.min(n.t,e.t-l)):s.end>e.b&&(l=(s.end-e.b)/r,n.b=Math.max(n.b,e.b+l))}function Kg(n,e,t){const i=n.drawingArea,{extra:s,additionalAngle:o,padding:r,size:a}=t,l=n.getPointPosition(e,i+s+r,o),c=Math.round(ks(Ot(l.angle+rt))),h=tp(l.y,a.h,c),u=Zg(c),d=Qg(l.x,a.w,u);return{visible:!0,x:l.x,y:h,textAlign:u,left:d,top:h,right:d+a.w,bottom:h+a.h}}function Jg(n,e){if(!e)return!0;const{left:t,top:i,right:s,bottom:o}=n;return!(Zt({x:t,y:i},e)||Zt({x:t,y:o},e)||Zt({x:s,y:i},e)||Zt({x:s,y:o},e))}function Gg(n,e,t){const i=[],s=n._pointLabels.length,o=n.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:ps(o)/2,additionalAngle:r?Q/s:0};let c;for(let h=0;h<s;h++){l.padding=t[h],l.size=e[h];const u=Kg(n,h,l);i.push(u),a==="auto"&&(u.visible=Jg(u,c),u.visible&&(c=u))}return i}function Zg(n){return n===0||n===180?"center":n<180?"left":"right"}function Qg(n,e,t){return t==="right"?n-=e:t==="center"&&(n-=e/2),n}function tp(n,e,t){return t===90||t===270?n-=e/2:(t>270||t<90)&&(n-=e),n}function ep(n,e,t){const{left:i,top:s,right:o,bottom:r}=t,{backdropColor:a}=e;if(!U(a)){const l=we(e.borderRadius),c=mt(e.backdropPadding);n.fillStyle=a;const h=i-c.left,u=s-c.top,d=o-i+c.width,f=r-s+c.height;Object.values(l).some(p=>p!==0)?(n.beginPath(),hn(n,{x:h,y:u,w:d,h:f,radius:l}),n.fill()):n.fillRect(h,u,d,f)}}function np(n,e){const{ctx:t,options:{pointLabels:i}}=n;for(let s=e-1;s>=0;s--){const o=n._pointLabelItems[s];if(!o.visible)continue;const r=i.setContext(n.getPointLabelContext(s));ep(t,r,o);const a=ht(r.font),{x:l,y:c,textAlign:h}=o;ke(t,n._pointLabels[s],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function Qa(n,e,t,i){const{ctx:s}=n;if(t)s.arc(n.xCenter,n.yCenter,e,0,Z);else{let o=n.getPointPosition(0,e);s.moveTo(o.x,o.y);for(let r=1;r<i;r++)o=n.getPointPosition(r,e),s.lineTo(o.x,o.y)}}function ip(n,e,t,i,s){const o=n.ctx,r=e.circular,{color:a,lineWidth:l}=e;!r&&!i||!a||!l||t<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(s.dash||[]),o.lineDashOffset=s.dashOffset,o.beginPath(),Qa(n,t,r,i),o.closePath(),o.stroke(),o.restore())}function sp(n,e,t){return ce(n,{label:t,index:e,type:"pointLabel"})}class Ge extends ci{constructor(e){super(e),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const e=this._padding=mt(ps(this.options)/2),t=this.width=this.maxWidth-e.width,i=this.height=this.maxHeight-e.height;this.xCenter=Math.floor(this.left+t/2+e.left),this.yCenter=Math.floor(this.top+i/2+e.top),this.drawingArea=Math.floor(Math.min(t,i)/2)}determineDataLimits(){const{min:e,max:t}=this.getMinMax(!1);this.min=ot(e)&&!isNaN(e)?e:0,this.max=ot(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/ps(this.options))}generateTickLabels(e){ci.prototype.generateTickLabels.call(this,e),this._pointLabels=this.getLabels().map((t,i)=>{const s=G(this.options.pointLabels.callback,[t,i],this);return s||s===0?s:""}).filter((t,i)=>this.chart.getDataVisibility(i))}fit(){const e=this.options;e.display&&e.pointLabels.display?qg(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(e,t,i,s){this.xCenter+=Math.floor((e-t)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(e,t,i,s))}getIndexAngle(e){const t=Z/(this._pointLabels.length||1),i=this.options.startAngle||0;return Ot(e*t+Lt(i))}getDistanceFromCenterForValue(e){if(U(e))return NaN;const t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-e)*t:(e-this.min)*t}getValueForDistanceFromCenter(e){if(U(e))return NaN;const t=e/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(e){const t=this._pointLabels||[];if(e>=0&&e<t.length){const i=t[e];return sp(this.getContext(),e,i)}}getPointPosition(e,t,i=0){const s=this.getIndexAngle(e)-rt+i;return{x:Math.cos(s)*t+this.xCenter,y:Math.sin(s)*t+this.yCenter,angle:s}}getPointPositionForValue(e,t){return this.getPointPosition(e,this.getDistanceFromCenterForValue(t))}getBasePosition(e){return this.getPointPositionForValue(e||0,this.getBaseValue())}getPointLabelPosition(e){const{left:t,top:i,right:s,bottom:o}=this._pointLabelItems[e];return{left:t,top:i,right:s,bottom:o}}drawBackground(){const{backgroundColor:e,grid:{circular:t}}=this.options;if(e){const i=this.ctx;i.save(),i.beginPath(),Qa(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),i.closePath(),i.fillStyle=e,i.fill(),i.restore()}}drawGrid(){const e=this.ctx,t=this.options,{angleLines:i,grid:s,border:o}=t,r=this._pointLabels.length;let a,l,c;if(t.pointLabels.display&&np(this,r),s.display&&this.ticks.forEach((h,u)=>{if(u!==0||u===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const d=this.getContext(u),f=s.setContext(d),p=o.setContext(d);ip(this,f,l,r,p)}}),i.display){for(e.save(),a=r-1;a>=0;a--){const h=i.setContext(this.getPointLabelContext(a)),{color:u,lineWidth:d}=h;!d||!u||(e.lineWidth=d,e.strokeStyle=u,e.setLineDash(h.borderDash),e.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),c=this.getPointPosition(a,l),e.beginPath(),e.moveTo(this.xCenter,this.yCenter),e.lineTo(c.x,c.y),e.stroke())}e.restore()}}drawBorder(){}drawLabels(){const e=this.ctx,t=this.options,i=t.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let o,r;e.save(),e.translate(this.xCenter,this.yCenter),e.rotate(s),e.textAlign="center",e.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!t.reverse)return;const c=i.setContext(this.getContext(l)),h=ht(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){e.font=h.string,r=e.measureText(a.label).width,e.fillStyle=c.backdropColor;const u=mt(c.backdropPadding);e.fillRect(-r/2-u.left,-o-h.size/2-u.top,r+u.width,h.size+u.height)}ke(e,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),e.restore()}drawTitle(){}}E(Ge,"id","radialLinear"),E(Ge,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:bi.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(e){return e},padding:5,centerPointLabels:!1}}),E(Ge,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),E(Ge,"descriptors",{angleLines:{_fallback:"grid"}});const vi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},wt=Object.keys(vi);function kr(n,e){return n-e}function Cr(n,e){if(U(e))return null;const t=n._adapter,{parser:i,round:s,isoWeekday:o}=n._parseOpts;let r=e;return typeof i=="function"&&(r=i(r)),ot(r)||(r=typeof i=="string"?t.parse(r,i):t.parse(r)),r===null?null:(s&&(r=s==="week"&&(Le(o)||o===!0)?t.startOf(r,"isoWeek",o):t.startOf(r,s)),+r)}function Or(n,e,t,i){const s=wt.length;for(let o=wt.indexOf(n);o<s-1;++o){const r=vi[wt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((t-e)/(a*r.size))<=i)return wt[o]}return wt[s-1]}function op(n,e,t,i,s){for(let o=wt.length-1;o>=wt.indexOf(t);o--){const r=wt[o];if(vi[r].common&&n._adapter.diff(s,i,r)>=e-1)return r}return wt[t?wt.indexOf(t):0]}function rp(n){for(let e=wt.indexOf(n)+1,t=wt.length;e<t;++e)if(vi[wt[e]].common)return wt[e]}function Pr(n,e,t){if(!t)n[e]=!0;else if(t.length){const{lo:i,hi:s}=Cs(t,e),o=t[i]>=e?t[i]:t[s];n[o]=!0}}function ap(n,e,t,i){const s=n._adapter,o=+s.startOf(e[0].value,i),r=e[e.length-1].value;let a,l;for(a=o;a<=r;a=+s.add(a,1,i))l=t[a],l>=0&&(e[l].major=!0);return e}function Ar(n,e,t){const i=[],s={},o=e.length;let r,a;for(r=0;r<o;++r)a=e[r],s[a]=r,i.push({value:a,major:!1});return o===0||!t?i:ap(n,i,s,t)}class fn extends Ce{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,t={}){const i=e.time||(e.time={}),s=this._adapter=new gd._date(e.adapters.date);s.init(t),Ze(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(e),this._normalized=t.normalized}parse(e,t){return e===void 0?null:Cr(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,t=this._adapter,i=e.time.unit||"day";let{min:s,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=ot(s)&&!isNaN(s)?s:+t.startOf(Date.now(),i),o=ot(o)&&!isNaN(o)?o:+t.endOf(Date.now(),i)+1,this.min=Math.min(s,o-1),this.max=Math.max(s+1,o)}_getLabelBounds(){const e=this.getLabelTimestamps();let t=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return e.length&&(t=e[0],i=e[e.length-1]),{min:t,max:i}}buildTicks(){const e=this.options,t=e.time,i=e.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const o=this.min,r=this.max,a=zh(s,o,r);return this._unit=t.unit||(i.autoSkip?Or(t.minUnit,this.min,this.max,this._getLabelCapacity(o)):op(this,a.length,t.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:rp(this._unit),this.initOffsets(s),e.reverse&&a.reverse(),Ar(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let t=0,i=0,s,o;this.options.offset&&e.length&&(s=this.getDecimalForValue(e[0]),e.length===1?t=1-s:t=(this.getDecimalForValue(e[1])-s)/2,o=this.getDecimalForValue(e[e.length-1]),e.length===1?i=o:i=(o-this.getDecimalForValue(e[e.length-2]))/2);const r=e.length<3?.5:.25;t=ut(t,0,r),i=ut(i,0,r),this._offsets={start:t,end:i,factor:1/(t+1+i)}}_generate(){const e=this._adapter,t=this.min,i=this.max,s=this.options,o=s.time,r=o.unit||Or(o.minUnit,t,i,this._getLabelCapacity(t)),a=z(s.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=Le(l)||l===!0,h={};let u=t,d,f;if(c&&(u=+e.startOf(u,"isoWeek",l)),u=+e.startOf(u,c?"day":r),e.diff(i,t,r)>1e5*a)throw new Error(t+" and "+i+" are too far apart with stepSize of "+a+" "+r);const p=s.ticks.source==="data"&&this.getDataTimestamps();for(d=u,f=0;d<i;d=+e.add(d,a,r),f++)Pr(h,d,p);return(d===i||s.bounds==="ticks"||f===1)&&Pr(h,d,p),Object.keys(h).sort(kr).map(m=>+m)}getLabelForValue(e){const t=this._adapter,i=this.options.time;return i.tooltipFormat?t.format(e,i.tooltipFormat):t.format(e,i.displayFormats.datetime)}format(e,t){const s=this.options.time.displayFormats,o=this._unit,r=t||s[o];return this._adapter.format(e,r)}_tickFormatFunction(e,t,i,s){const o=this.options,r=o.ticks.callback;if(r)return G(r,[e,t,i],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],u=c&&a[c],d=i[t],f=c&&u&&d&&d.major;return this._adapter.format(e,s||(f?u:h))}generateTickLabels(e){let t,i,s;for(t=0,i=e.length;t<i;++t)s=e[t],s.label=this._tickFormatFunction(s.value,t,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const t=this._offsets,i=this.getDecimalForValue(e);return this.getPixelForDecimal((t.start+i)*t.factor)}getValueForPixel(e){const t=this._offsets,i=this.getDecimalForPixel(e)/t.factor-t.end;return this.min+i*(this.max-this.min)}_getLabelSize(e){const t=this.options.ticks,i=this.ctx.measureText(e).width,s=Lt(this.isHorizontal()?t.maxRotation:t.minRotation),o=Math.cos(s),r=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*o+a*r,h:i*r+a*o}}_getLabelCapacity(e){const t=this.options.time,i=t.displayFormats,s=i[t.unit]||i.millisecond,o=this._tickFormatFunction(e,0,Ar(this,[e],this._majorUnit),s),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let e=this._cache.data||[],t,i;if(e.length)return e;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,i=s.length;t<i;++t)e=e.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let t,i;if(e.length)return e;const s=this.getLabels();for(t=0,i=s.length;t<i;++t)e.push(Cr(this,s[t]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return da(e.sort(kr))}}E(fn,"id","time"),E(fn,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function zn(n,e,t){let i=0,s=n.length-1,o,r,a,l;t?(e>=n[i].pos&&e<=n[s].pos&&({lo:i,hi:s}=Gt(n,"pos",e)),{pos:o,time:a}=n[i],{pos:r,time:l}=n[s]):(e>=n[i].time&&e<=n[s].time&&({lo:i,hi:s}=Gt(n,"time",e)),{time:o,pos:a}=n[i],{time:r,pos:l}=n[s]);const c=r-o;return c?a+(l-a)*(e-o)/c:a}class ms extends fn{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(e);this._minPos=zn(t,this.min),this._tableRange=zn(t,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:t,max:i}=this,s=[],o=[];let r,a,l,c,h;for(r=0,a=e.length;r<a;++r)c=e[r],c>=t&&c<=i&&s.push(c);if(s.length<2)return[{time:t,pos:0},{time:i,pos:1}];for(r=0,a=s.length;r<a;++r)h=s[r+1],l=s[r-1],c=s[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const e=this.min,t=this.max;let i=super.getDataTimestamps();return(!i.includes(e)||!i.length)&&i.splice(0,0,e),(!i.includes(t)||i.length===1)&&i.push(t),i.sort((s,o)=>s-o)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const t=this.getDataTimestamps(),i=this.getLabelTimestamps();return t.length&&i.length?e=this.normalize(t.concat(i)):e=t.length?t:i,e=this._cache.all=e,e}getDecimalForValue(e){return(zn(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const t=this._offsets,i=this.getDecimalForPixel(e)/t.factor-t.end;return zn(this._table,i*this._tableRange+this._minPos,!0)}}E(ms,"id","timeseries"),E(ms,"defaults",fn.defaults);var lp=Object.freeze({__proto__:null,CategoryScale:ds,LinearScale:fs,LogarithmicScale:gs,RadialLinearScale:Ge,TimeScale:fn,TimeSeriesScale:ms});const cp=[fd,Yf,zg,lp];Kt.register(...cp);var Yi=["onChange","onClose","onDayCreate","onDestroy","onKeyDown","onMonthChange","onOpen","onParseConfig","onReady","onValueUpdate","onYearChange","onPreCalendarPosition"],Te={_disable:[],allowInput:!1,allowInvalidPreload:!1,altFormat:"F j, Y",altInput:!1,altInputClass:"form-control input",animate:typeof window=="object"&&window.navigator.userAgent.indexOf("MSIE")===-1,ariaDateFormat:"F j, Y",autoFillDefaultTime:!0,clickOpens:!0,closeOnSelect:!0,conjunction:", ",dateFormat:"Y-m-d",defaultHour:12,defaultMinute:0,defaultSeconds:0,disable:[],disableMobile:!1,enableSeconds:!1,enableTime:!1,errorHandler:function(n){return typeof console<"u"&&console.warn(n)},getWeek:function(n){var e=new Date(n.getTime());e.setHours(0,0,0,0),e.setDate(e.getDate()+3-(e.getDay()+6)%7);var t=new Date(e.getFullYear(),0,4);return 1+Math.round(((e.getTime()-t.getTime())/864e5-3+(t.getDay()+6)%7)/7)},hourIncrement:1,ignoredFocusElements:[],inline:!1,locale:"default",minuteIncrement:5,mode:"single",monthSelectorType:"dropdown",nextArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M13.207 8.472l-7.854 7.854-0.707-0.707 7.146-7.146-7.146-7.148 0.707-0.707 7.854 7.854z' /></svg>",noCalendar:!1,now:new Date,onChange:[],onClose:[],onDayCreate:[],onDestroy:[],onKeyDown:[],onMonthChange:[],onOpen:[],onParseConfig:[],onReady:[],onValueUpdate:[],onYearChange:[],onPreCalendarPosition:[],plugins:[],position:"auto",positionElement:void 0,prevArrow:"<svg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 17 17'><g></g><path d='M5.207 8.471l7.146 7.147-0.707 0.707-7.853-7.854 7.854-7.853 0.707 0.707-7.147 7.146z' /></svg>",shorthandCurrentMonth:!1,showMonths:1,static:!1,time_24hr:!1,weekNumbers:!1,wrap:!1},gn={weekdays:{shorthand:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],longhand:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},months:{shorthand:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],longhand:["January","February","March","April","May","June","July","August","September","October","November","December"]},daysInMonth:[31,28,31,30,31,30,31,31,30,31,30,31],firstDayOfWeek:0,ordinal:function(n){var e=n%100;if(e>3&&e<21)return"th";switch(e%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},rangeSeparator:" to ",weekAbbreviation:"Wk",scrollTitle:"Scroll to increment",toggleTitle:"Click to toggle",amPM:["AM","PM"],yearAriaLabel:"Year",monthAriaLabel:"Month",hourAriaLabel:"Hour",minuteAriaLabel:"Minute",time_24hr:!1},vt=function(n,e){return e===void 0&&(e=2),("000"+n).slice(e*-1)},Et=function(n){return n===!0?1:0};function Er(n,e){var t;return function(){var i=this,s=arguments;clearTimeout(t),t=setTimeout(function(){return n.apply(i,s)},e)}}var Ui=function(n){return n instanceof Array?n:[n]};function xt(n,e,t){if(t===!0)return n.classList.add(e);n.classList.remove(e)}function q(n,e,t){var i=window.document.createElement(n);return e=e||"",t=t||"",i.className=e,t!==void 0&&(i.textContent=t),i}function Hn(n){for(;n.firstChild;)n.removeChild(n.firstChild)}function tl(n,e){if(e(n))return n;if(n.parentNode)return tl(n.parentNode,e)}function jn(n,e){var t=q("div","numInputWrapper"),i=q("input","numInput "+n),s=q("span","arrowUp"),o=q("span","arrowDown");if(navigator.userAgent.indexOf("MSIE 9.0")===-1?i.type="number":(i.type="text",i.pattern="\\d*"),e!==void 0)for(var r in e)i.setAttribute(r,e[r]);return t.appendChild(i),t.appendChild(s),t.appendChild(o),t}function St(n){try{if(typeof n.composedPath=="function"){var e=n.composedPath();return e[0]}return n.target}catch{return n.target}}var $i=function(){},hi=function(n,e,t){return t.months[e?"shorthand":"longhand"][n]},hp={D:$i,F:function(n,e,t){n.setMonth(t.months.longhand.indexOf(e))},G:function(n,e){n.setHours((n.getHours()>=12?12:0)+parseFloat(e))},H:function(n,e){n.setHours(parseFloat(e))},J:function(n,e){n.setDate(parseFloat(e))},K:function(n,e,t){n.setHours(n.getHours()%12+12*Et(new RegExp(t.amPM[1],"i").test(e)))},M:function(n,e,t){n.setMonth(t.months.shorthand.indexOf(e))},S:function(n,e){n.setSeconds(parseFloat(e))},U:function(n,e){return new Date(parseFloat(e)*1e3)},W:function(n,e,t){var i=parseInt(e),s=new Date(n.getFullYear(),0,2+(i-1)*7,0,0,0,0);return s.setDate(s.getDate()-s.getDay()+t.firstDayOfWeek),s},Y:function(n,e){n.setFullYear(parseFloat(e))},Z:function(n,e){return new Date(e)},d:function(n,e){n.setDate(parseFloat(e))},h:function(n,e){n.setHours((n.getHours()>=12?12:0)+parseFloat(e))},i:function(n,e){n.setMinutes(parseFloat(e))},j:function(n,e){n.setDate(parseFloat(e))},l:$i,m:function(n,e){n.setMonth(parseFloat(e)-1)},n:function(n,e){n.setMonth(parseFloat(e)-1)},s:function(n,e){n.setSeconds(parseFloat(e))},u:function(n,e){return new Date(parseFloat(e))},w:$i,y:function(n,e){n.setFullYear(2e3+parseFloat(e))}},xe={D:"",F:"",G:"(\\d\\d|\\d)",H:"(\\d\\d|\\d)",J:"(\\d\\d|\\d)\\w+",K:"",M:"",S:"(\\d\\d|\\d)",U:"(.+)",W:"(\\d\\d|\\d)",Y:"(\\d{4})",Z:"(.+)",d:"(\\d\\d|\\d)",h:"(\\d\\d|\\d)",i:"(\\d\\d|\\d)",j:"(\\d\\d|\\d)",l:"",m:"(\\d\\d|\\d)",n:"(\\d\\d|\\d)",s:"(\\d\\d|\\d)",u:"(.+)",w:"(\\d\\d|\\d)",y:"(\\d{2})"},sn={Z:function(n){return n.toISOString()},D:function(n,e,t){return e.weekdays.shorthand[sn.w(n,e,t)]},F:function(n,e,t){return hi(sn.n(n,e,t)-1,!1,e)},G:function(n,e,t){return vt(sn.h(n,e,t))},H:function(n){return vt(n.getHours())},J:function(n,e){return e.ordinal!==void 0?n.getDate()+e.ordinal(n.getDate()):n.getDate()},K:function(n,e){return e.amPM[Et(n.getHours()>11)]},M:function(n,e){return hi(n.getMonth(),!0,e)},S:function(n){return vt(n.getSeconds())},U:function(n){return n.getTime()/1e3},W:function(n,e,t){return t.getWeek(n)},Y:function(n){return vt(n.getFullYear(),4)},d:function(n){return vt(n.getDate())},h:function(n){return n.getHours()%12?n.getHours()%12:12},i:function(n){return vt(n.getMinutes())},j:function(n){return n.getDate()},l:function(n,e){return e.weekdays.longhand[n.getDay()]},m:function(n){return vt(n.getMonth()+1)},n:function(n){return n.getMonth()+1},s:function(n){return n.getSeconds()},u:function(n){return n.getTime()},w:function(n){return n.getDay()},y:function(n){return String(n.getFullYear()).substring(2)}},el=function(n){var e=n.config,t=e===void 0?Te:e,i=n.l10n,s=i===void 0?gn:i,o=n.isMobile,r=o===void 0?!1:o;return function(a,l,c){var h=c||s;return t.formatDate!==void 0&&!r?t.formatDate(a,l,h):l.split("").map(function(u,d,f){return sn[u]&&f[d-1]!=="\\"?sn[u](a,h,t):u!=="\\"?u:""}).join("")}},bs=function(n){var e=n.config,t=e===void 0?Te:e,i=n.l10n,s=i===void 0?gn:i;return function(o,r,a,l){if(!(o!==0&&!o)){var c=l||s,h,u=o;if(o instanceof Date)h=new Date(o.getTime());else if(typeof o!="string"&&o.toFixed!==void 0)h=new Date(o);else if(typeof o=="string"){var d=r||(t||Te).dateFormat,f=String(o).trim();if(f==="today")h=new Date,a=!0;else if(t&&t.parseDate)h=t.parseDate(o,d);else if(/Z$/.test(f)||/GMT$/.test(f))h=new Date(o);else{for(var p=void 0,m=[],x=0,y=0,v="";x<d.length;x++){var S=d[x],k=S==="\\",D=d[x-1]==="\\"||k;if(xe[S]&&!D){v+=xe[S];var C=new RegExp(v).exec(o);C&&(p=!0)&&m[S!=="Y"?"push":"unshift"]({fn:hp[S],val:C[++y]})}else k||(v+=".")}h=!t||!t.noCalendar?new Date(new Date().getFullYear(),0,1,0,0,0,0):new Date(new Date().setHours(0,0,0,0)),m.forEach(function(O){var P=O.fn,F=O.val;return h=P(h,F,c)||h}),h=p?h:void 0}}if(!(h instanceof Date&&!isNaN(h.getTime()))){t.errorHandler(new Error("Invalid date provided: "+u));return}return a===!0&&h.setHours(0,0,0,0),h}}};function kt(n,e,t){return t===void 0&&(t=!0),t!==!1?new Date(n.getTime()).setHours(0,0,0,0)-new Date(e.getTime()).setHours(0,0,0,0):n.getTime()-e.getTime()}var up=function(n,e,t){return n>Math.min(e,t)&&n<Math.max(e,t)},qi=function(n,e,t){return n*3600+e*60+t},dp=function(n){var e=Math.floor(n/3600),t=(n-e*3600)/60;return[e,t,n-e*3600-t*60]},fp={DAY:864e5};function Xi(n){var e=n.defaultHour,t=n.defaultMinute,i=n.defaultSeconds;if(n.minDate!==void 0){var s=n.minDate.getHours(),o=n.minDate.getMinutes(),r=n.minDate.getSeconds();e<s&&(e=s),e===s&&t<o&&(t=o),e===s&&t===o&&i<r&&(i=n.minDate.getSeconds())}if(n.maxDate!==void 0){var a=n.maxDate.getHours(),l=n.maxDate.getMinutes();e=Math.min(e,a),e===a&&(t=Math.min(l,t)),e===a&&t===l&&(i=n.maxDate.getSeconds())}return{hours:e,minutes:t,seconds:i}}typeof Object.assign!="function"&&(Object.assign=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];if(!n)throw TypeError("Cannot convert undefined or null to object");for(var i=function(a){a&&Object.keys(a).forEach(function(l){return n[l]=a[l]})},s=0,o=e;s<o.length;s++){var r=o[s];i(r)}return n});var ft=function(){return ft=Object.assign||function(n){for(var e,t=1,i=arguments.length;t<i;t++){e=arguments[t];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(n[s]=e[s])}return n},ft.apply(this,arguments)},Tr=function(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var i=Array(n),s=0,e=0;e<t;e++)for(var o=arguments[e],r=0,a=o.length;r<a;r++,s++)i[s]=o[r];return i},gp=300;function pp(n,e){var t={config:ft(ft({},Te),at.defaultConfig),l10n:gn};t.parseDate=bs({config:t.config,l10n:t.l10n}),t._handlers=[],t.pluginElements=[],t.loadedPlugins=[],t._bind=m,t._setHoursFromDate=d,t._positionCalendar=Mn,t.changeMonth=te,t.changeYear=_n,t.clear=ee,t.close=he,t.onMouseOver=wn,t._createElement=q,t.createDay=C,t.destroy=Vt,t.isEnabled=ue,t.jumpToDate=v,t.updateValue=Yt,t.open=sl,t.redraw=Vs,t.set=ll,t.setDate=cl,t.toggle=fl;function i(){t.utils={getDaysInMonth:function(g,b){return g===void 0&&(g=t.currentMonth),b===void 0&&(b=t.currentYear),g===1&&(b%4===0&&b%100!==0||b%400===0)?29:t.l10n.daysInMonth[g]}}}function s(){t.element=t.input=n,t.isOpen=!1,ol(),Ws(),ul(),hl(),i(),t.isMobile||D(),y(),(t.selectedDates.length||t.config.noCalendar)&&(t.config.enableTime&&d(t.config.noCalendar?t.latestSelectedDateObj:void 0),Yt(!1)),a();var g=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);!t.isMobile&&g&&Mn(),st("onReady")}function o(){var g;return((g=t.calendarContainer)===null||g===void 0?void 0:g.getRootNode()).activeElement||document.activeElement}function r(g){return g.bind(t)}function a(){var g=t.config;g.weekNumbers===!1&&g.showMonths===1||g.noCalendar!==!0&&window.requestAnimationFrame(function(){if(t.calendarContainer!==void 0&&(t.calendarContainer.style.visibility="hidden",t.calendarContainer.style.display="block"),t.daysContainer!==void 0){var b=(t.days.offsetWidth+1)*g.showMonths;t.daysContainer.style.width=b+"px",t.calendarContainer.style.width=b+(t.weekWrapper!==void 0?t.weekWrapper.offsetWidth:0)+"px",t.calendarContainer.style.removeProperty("visibility"),t.calendarContainer.style.removeProperty("display")}})}function l(g){if(t.selectedDates.length===0){var b=t.config.minDate===void 0||kt(new Date,t.config.minDate)>=0?new Date:new Date(t.config.minDate.getTime()),_=Xi(t.config);b.setHours(_.hours,_.minutes,_.seconds,b.getMilliseconds()),t.selectedDates=[b],t.latestSelectedDateObj=b}g!==void 0&&g.type!=="blur"&&ml(g);var M=t._input.value;u(),Yt(),t._input.value!==M&&t._debouncedChange()}function c(g,b){return g%12+12*Et(b===t.l10n.amPM[1])}function h(g){switch(g%24){case 0:case 12:return 12;default:return g%12}}function u(){if(!(t.hourElement===void 0||t.minuteElement===void 0)){var g=(parseInt(t.hourElement.value.slice(-2),10)||0)%24,b=(parseInt(t.minuteElement.value,10)||0)%60,_=t.secondElement!==void 0?(parseInt(t.secondElement.value,10)||0)%60:0;t.amPM!==void 0&&(g=c(g,t.amPM.textContent));var M=t.config.minTime!==void 0||t.config.minDate&&t.minDateHasTime&&t.latestSelectedDateObj&&kt(t.latestSelectedDateObj,t.config.minDate,!0)===0,A=t.config.maxTime!==void 0||t.config.maxDate&&t.maxDateHasTime&&t.latestSelectedDateObj&&kt(t.latestSelectedDateObj,t.config.maxDate,!0)===0;if(t.config.maxTime!==void 0&&t.config.minTime!==void 0&&t.config.minTime>t.config.maxTime){var T=qi(t.config.minTime.getHours(),t.config.minTime.getMinutes(),t.config.minTime.getSeconds()),j=qi(t.config.maxTime.getHours(),t.config.maxTime.getMinutes(),t.config.maxTime.getSeconds()),L=qi(g,b,_);if(L>j&&L<T){var V=dp(T);g=V[0],b=V[1],_=V[2]}}else{if(A){var R=t.config.maxTime!==void 0?t.config.maxTime:t.config.maxDate;g=Math.min(g,R.getHours()),g===R.getHours()&&(b=Math.min(b,R.getMinutes())),b===R.getMinutes()&&(_=Math.min(_,R.getSeconds()))}if(M){var I=t.config.minTime!==void 0?t.config.minTime:t.config.minDate;g=Math.max(g,I.getHours()),g===I.getHours()&&b<I.getMinutes()&&(b=I.getMinutes()),b===I.getMinutes()&&(_=Math.max(_,I.getSeconds()))}}f(g,b,_)}}function d(g){var b=g||t.latestSelectedDateObj;b&&b instanceof Date&&f(b.getHours(),b.getMinutes(),b.getSeconds())}function f(g,b,_){t.latestSelectedDateObj!==void 0&&t.latestSelectedDateObj.setHours(g%24,b,_||0,0),!(!t.hourElement||!t.minuteElement||t.isMobile)&&(t.hourElement.value=vt(t.config.time_24hr?g:(12+g)%12+12*Et(g%12===0)),t.minuteElement.value=vt(b),t.amPM!==void 0&&(t.amPM.textContent=t.l10n.amPM[Et(g>=12)]),t.secondElement!==void 0&&(t.secondElement.value=vt(_)))}function p(g){var b=St(g),_=parseInt(b.value)+(g.delta||0);(_/1e3>1||g.key==="Enter"&&!/[^\d]/.test(_.toString()))&&_n(_)}function m(g,b,_,M){if(b instanceof Array)return b.forEach(function(A){return m(g,A,_,M)});if(g instanceof Array)return g.forEach(function(A){return m(A,b,_,M)});g.addEventListener(b,_,M),t._handlers.push({remove:function(){return g.removeEventListener(b,_,M)}})}function x(){st("onChange")}function y(){if(t.config.wrap&&["open","close","toggle","clear"].forEach(function(_){Array.prototype.forEach.call(t.element.querySelectorAll("[data-"+_+"]"),function(M){return m(M,"click",t[_])})}),t.isMobile){dl();return}var g=Er(il,50);if(t._debouncedChange=Er(x,gp),t.daysContainer&&!/iPhone|iPad|iPod/i.test(navigator.userAgent)&&m(t.daysContainer,"mouseover",function(_){t.config.mode==="range"&&wn(St(_))}),m(t._input,"keydown",zs),t.calendarContainer!==void 0&&m(t.calendarContainer,"keydown",zs),!t.config.inline&&!t.config.static&&m(window,"resize",g),window.ontouchstart!==void 0?m(window.document,"touchstart",Rt):m(window.document,"mousedown",Rt),m(window.document,"focus",Rt,{capture:!0}),t.config.clickOpens===!0&&(m(t._input,"focus",t.open),m(t._input,"click",t.open)),t.daysContainer!==void 0&&(m(t.monthNav,"click",pl),m(t.monthNav,["keyup","increment"],p),m(t.daysContainer,"click",Ys)),t.timeContainer!==void 0&&t.minuteElement!==void 0&&t.hourElement!==void 0){var b=function(_){return St(_).select()};m(t.timeContainer,["increment"],l),m(t.timeContainer,"blur",l,{capture:!0}),m(t.timeContainer,"click",S),m([t.hourElement,t.minuteElement],["focus","click"],b),t.secondElement!==void 0&&m(t.secondElement,"focus",function(){return t.secondElement&&t.secondElement.select()}),t.amPM!==void 0&&m(t.amPM,"click",function(_){l(_)})}t.config.allowInput&&m(t._input,"blur",nl)}function v(g,b){var _=g!==void 0?t.parseDate(g):t.latestSelectedDateObj||(t.config.minDate&&t.config.minDate>t.now?t.config.minDate:t.config.maxDate&&t.config.maxDate<t.now?t.config.maxDate:t.now),M=t.currentYear,A=t.currentMonth;try{_!==void 0&&(t.currentYear=_.getFullYear(),t.currentMonth=_.getMonth())}catch(T){T.message="Invalid date supplied: "+_,t.config.errorHandler(T)}b&&t.currentYear!==M&&(st("onYearChange"),tt()),b&&(t.currentYear!==M||t.currentMonth!==A)&&st("onMonthChange"),t.redraw()}function S(g){var b=St(g);~b.className.indexOf("arrow")&&k(g,b.classList.contains("arrowUp")?1:-1)}function k(g,b,_){var M=g&&St(g),A=_||M&&M.parentNode&&M.parentNode.firstChild,T=Mi("increment");T.delta=b,A&&A.dispatchEvent(T)}function D(){var g=window.document.createDocumentFragment();if(t.calendarContainer=q("div","flatpickr-calendar"),t.calendarContainer.tabIndex=-1,!t.config.noCalendar){if(g.appendChild(J()),t.innerContainer=q("div","flatpickr-innerContainer"),t.config.weekNumbers){var b=Qt(),_=b.weekWrapper,M=b.weekNumbers;t.innerContainer.appendChild(_),t.weekNumbers=M,t.weekWrapper=_}t.rContainer=q("div","flatpickr-rContainer"),t.rContainer.appendChild(yt()),t.daysContainer||(t.daysContainer=q("div","flatpickr-days"),t.daysContainer.tabIndex=-1),W(),t.rContainer.appendChild(t.daysContainer),t.innerContainer.appendChild(t.rContainer),g.appendChild(t.innerContainer)}t.config.enableTime&&g.appendChild(it()),xt(t.calendarContainer,"rangeMode",t.config.mode==="range"),xt(t.calendarContainer,"animate",t.config.animate===!0),xt(t.calendarContainer,"multiMonth",t.config.showMonths>1),t.calendarContainer.appendChild(g);var A=t.config.appendTo!==void 0&&t.config.appendTo.nodeType!==void 0;if((t.config.inline||t.config.static)&&(t.calendarContainer.classList.add(t.config.inline?"inline":"static"),t.config.inline&&(!A&&t.element.parentNode?t.element.parentNode.insertBefore(t.calendarContainer,t._input.nextSibling):t.config.appendTo!==void 0&&t.config.appendTo.appendChild(t.calendarContainer)),t.config.static)){var T=q("div","flatpickr-wrapper");t.element.parentNode&&t.element.parentNode.insertBefore(T,t.element),T.appendChild(t.element),t.altInput&&T.appendChild(t.altInput),T.appendChild(t.calendarContainer)}!t.config.static&&!t.config.inline&&(t.config.appendTo!==void 0?t.config.appendTo:window.document.body).appendChild(t.calendarContainer)}function C(g,b,_,M){var A=ue(b,!0),T=q("span",g,b.getDate().toString());return T.dateObj=b,T.$i=M,T.setAttribute("aria-label",t.formatDate(b,t.config.ariaDateFormat)),g.indexOf("hidden")===-1&&kt(b,t.now)===0&&(t.todayDateElem=T,T.classList.add("today"),T.setAttribute("aria-current","date")),A?(T.tabIndex=-1,Di(b)&&(T.classList.add("selected"),t.selectedDateElem=T,t.config.mode==="range"&&(xt(T,"startRange",t.selectedDates[0]&&kt(b,t.selectedDates[0],!0)===0),xt(T,"endRange",t.selectedDates[1]&&kt(b,t.selectedDates[1],!0)===0),g==="nextMonthDay"&&T.classList.add("inRange")))):T.classList.add("flatpickr-disabled"),t.config.mode==="range"&&gl(b)&&!Di(b)&&T.classList.add("inRange"),t.weekNumbers&&t.config.showMonths===1&&g!=="prevMonthDay"&&M%7===6&&t.weekNumbers.insertAdjacentHTML("beforeend","<span class='flatpickr-day'>"+t.config.getWeek(b)+"</span>"),st("onDayCreate",T),T}function O(g){g.focus(),t.config.mode==="range"&&wn(g)}function P(g){for(var b=g>0?0:t.config.showMonths-1,_=g>0?t.config.showMonths:-1,M=b;M!=_;M+=g)for(var A=t.daysContainer.children[M],T=g>0?0:A.children.length-1,j=g>0?A.children.length:-1,L=T;L!=j;L+=g){var V=A.children[L];if(V.className.indexOf("hidden")===-1&&ue(V.dateObj))return V}}function F(g,b){for(var _=g.className.indexOf("Month")===-1?g.dateObj.getMonth():t.currentMonth,M=b>0?t.config.showMonths:-1,A=b>0?1:-1,T=_-t.currentMonth;T!=M;T+=A)for(var j=t.daysContainer.children[T],L=_-t.currentMonth===T?g.$i+b:b<0?j.children.length-1:0,V=j.children.length,R=L;R>=0&&R<V&&R!=(b>0?V:-1);R+=A){var I=j.children[R];if(I.className.indexOf("hidden")===-1&&ue(I.dateObj)&&Math.abs(g.$i-R)>=Math.abs(b))return O(I)}t.changeMonth(A),N(P(A),0)}function N(g,b){var _=o(),M=vn(_||document.body),A=g!==void 0?g:M?_:t.selectedDateElem!==void 0&&vn(t.selectedDateElem)?t.selectedDateElem:t.todayDateElem!==void 0&&vn(t.todayDateElem)?t.todayDateElem:P(b>0?1:-1);A===void 0?t._input.focus():M?F(A,b):O(A)}function H(g,b){for(var _=(new Date(g,b,1).getDay()-t.l10n.firstDayOfWeek+7)%7,M=t.utils.getDaysInMonth((b-1+12)%12,g),A=t.utils.getDaysInMonth(b,g),T=window.document.createDocumentFragment(),j=t.config.showMonths>1,L=j?"prevMonthDay hidden":"prevMonthDay",V=j?"nextMonthDay hidden":"nextMonthDay",R=M+1-_,I=0;R<=M;R++,I++)T.appendChild(C("flatpickr-day "+L,new Date(g,b-1,R),R,I));for(R=1;R<=A;R++,I++)T.appendChild(C("flatpickr-day",new Date(g,b,R),R,I));for(var K=A+1;K<=42-_&&(t.config.showMonths===1||I%7!==0);K++,I++)T.appendChild(C("flatpickr-day "+V,new Date(g,b+1,K%A),K,I));var Bt=q("div","dayContainer");return Bt.appendChild(T),Bt}function W(){if(t.daysContainer!==void 0){Hn(t.daysContainer),t.weekNumbers&&Hn(t.weekNumbers);for(var g=document.createDocumentFragment(),b=0;b<t.config.showMonths;b++){var _=new Date(t.currentYear,t.currentMonth,1);_.setMonth(t.currentMonth+b),g.appendChild(H(_.getFullYear(),_.getMonth()))}t.daysContainer.appendChild(g),t.days=t.daysContainer.firstChild,t.config.mode==="range"&&t.selectedDates.length===1&&wn()}}function tt(){if(!(t.config.showMonths>1||t.config.monthSelectorType!=="dropdown")){var g=function(M){return t.config.minDate!==void 0&&t.currentYear===t.config.minDate.getFullYear()&&M<t.config.minDate.getMonth()?!1:!(t.config.maxDate!==void 0&&t.currentYear===t.config.maxDate.getFullYear()&&M>t.config.maxDate.getMonth())};t.monthsDropdownContainer.tabIndex=-1,t.monthsDropdownContainer.innerHTML="";for(var b=0;b<12;b++)if(g(b)){var _=q("option","flatpickr-monthDropdown-month");_.value=new Date(t.currentYear,b).getMonth().toString(),_.textContent=hi(b,t.config.shorthandCurrentMonth,t.l10n),_.tabIndex=-1,t.currentMonth===b&&(_.selected=!0),t.monthsDropdownContainer.appendChild(_)}}}function bt(){var g=q("div","flatpickr-month"),b=window.document.createDocumentFragment(),_;t.config.showMonths>1||t.config.monthSelectorType==="static"?_=q("span","cur-month"):(t.monthsDropdownContainer=q("select","flatpickr-monthDropdown-months"),t.monthsDropdownContainer.setAttribute("aria-label",t.l10n.monthAriaLabel),m(t.monthsDropdownContainer,"change",function(j){var L=St(j),V=parseInt(L.value,10);t.changeMonth(V-t.currentMonth),st("onMonthChange")}),tt(),_=t.monthsDropdownContainer);var M=jn("cur-year",{tabindex:"-1"}),A=M.getElementsByTagName("input")[0];A.setAttribute("aria-label",t.l10n.yearAriaLabel),t.config.minDate&&A.setAttribute("min",t.config.minDate.getFullYear().toString()),t.config.maxDate&&(A.setAttribute("max",t.config.maxDate.getFullYear().toString()),A.disabled=!!t.config.minDate&&t.config.minDate.getFullYear()===t.config.maxDate.getFullYear());var T=q("div","flatpickr-current-month");return T.appendChild(_),T.appendChild(M),b.appendChild(T),g.appendChild(b),{container:g,yearElement:A,monthElement:_}}function $(){Hn(t.monthNav),t.monthNav.appendChild(t.prevMonthNav),t.config.showMonths&&(t.yearElements=[],t.monthElements=[]);for(var g=t.config.showMonths;g--;){var b=bt();t.yearElements.push(b.yearElement),t.monthElements.push(b.monthElement),t.monthNav.appendChild(b.container)}t.monthNav.appendChild(t.nextMonthNav)}function J(){return t.monthNav=q("div","flatpickr-months"),t.yearElements=[],t.monthElements=[],t.prevMonthNav=q("span","flatpickr-prev-month"),t.prevMonthNav.innerHTML=t.config.prevArrow,t.nextMonthNav=q("span","flatpickr-next-month"),t.nextMonthNav.innerHTML=t.config.nextArrow,$(),Object.defineProperty(t,"_hidePrevMonthArrow",{get:function(){return t.__hidePrevMonthArrow},set:function(g){t.__hidePrevMonthArrow!==g&&(xt(t.prevMonthNav,"flatpickr-disabled",g),t.__hidePrevMonthArrow=g)}}),Object.defineProperty(t,"_hideNextMonthArrow",{get:function(){return t.__hideNextMonthArrow},set:function(g){t.__hideNextMonthArrow!==g&&(xt(t.nextMonthNav,"flatpickr-disabled",g),t.__hideNextMonthArrow=g)}}),t.currentYearElement=t.yearElements[0],Sn(),t.monthNav}function it(){t.calendarContainer.classList.add("hasTime"),t.config.noCalendar&&t.calendarContainer.classList.add("noCalendar");var g=Xi(t.config);t.timeContainer=q("div","flatpickr-time"),t.timeContainer.tabIndex=-1;var b=q("span","flatpickr-time-separator",":"),_=jn("flatpickr-hour",{"aria-label":t.l10n.hourAriaLabel});t.hourElement=_.getElementsByTagName("input")[0];var M=jn("flatpickr-minute",{"aria-label":t.l10n.minuteAriaLabel});if(t.minuteElement=M.getElementsByTagName("input")[0],t.hourElement.tabIndex=t.minuteElement.tabIndex=-1,t.hourElement.value=vt(t.latestSelectedDateObj?t.latestSelectedDateObj.getHours():t.config.time_24hr?g.hours:h(g.hours)),t.minuteElement.value=vt(t.latestSelectedDateObj?t.latestSelectedDateObj.getMinutes():g.minutes),t.hourElement.setAttribute("step",t.config.hourIncrement.toString()),t.minuteElement.setAttribute("step",t.config.minuteIncrement.toString()),t.hourElement.setAttribute("min",t.config.time_24hr?"0":"1"),t.hourElement.setAttribute("max",t.config.time_24hr?"23":"12"),t.hourElement.setAttribute("maxlength","2"),t.minuteElement.setAttribute("min","0"),t.minuteElement.setAttribute("max","59"),t.minuteElement.setAttribute("maxlength","2"),t.timeContainer.appendChild(_),t.timeContainer.appendChild(b),t.timeContainer.appendChild(M),t.config.time_24hr&&t.timeContainer.classList.add("time24hr"),t.config.enableSeconds){t.timeContainer.classList.add("hasSeconds");var A=jn("flatpickr-second");t.secondElement=A.getElementsByTagName("input")[0],t.secondElement.value=vt(t.latestSelectedDateObj?t.latestSelectedDateObj.getSeconds():g.seconds),t.secondElement.setAttribute("step",t.minuteElement.getAttribute("step")),t.secondElement.setAttribute("min","0"),t.secondElement.setAttribute("max","59"),t.secondElement.setAttribute("maxlength","2"),t.timeContainer.appendChild(q("span","flatpickr-time-separator",":")),t.timeContainer.appendChild(A)}return t.config.time_24hr||(t.amPM=q("span","flatpickr-am-pm",t.l10n.amPM[Et((t.latestSelectedDateObj?t.hourElement.value:t.config.defaultHour)>11)]),t.amPM.title=t.l10n.toggleTitle,t.amPM.tabIndex=-1,t.timeContainer.appendChild(t.amPM)),t.timeContainer}function yt(){t.weekdayContainer?Hn(t.weekdayContainer):t.weekdayContainer=q("div","flatpickr-weekdays");for(var g=t.config.showMonths;g--;){var b=q("div","flatpickr-weekdaycontainer");t.weekdayContainer.appendChild(b)}return ct(),t.weekdayContainer}function ct(){if(t.weekdayContainer){var g=t.l10n.firstDayOfWeek,b=Tr(t.l10n.weekdays.shorthand);g>0&&g<b.length&&(b=Tr(b.splice(g,b.length),b.splice(0,g)));for(var _=t.config.showMonths;_--;)t.weekdayContainer.children[_].innerHTML=`
      <span class='flatpickr-weekday'>
        `+b.join("</span><span class='flatpickr-weekday'>")+`
      </span>
      `}}function Qt(){t.calendarContainer.classList.add("hasWeeks");var g=q("div","flatpickr-weekwrapper");g.appendChild(q("span","flatpickr-weekday",t.l10n.weekAbbreviation));var b=q("div","flatpickr-weeks");return g.appendChild(b),{weekWrapper:g,weekNumbers:b}}function te(g,b){b===void 0&&(b=!0);var _=b?g:g-t.currentMonth;_<0&&t._hidePrevMonthArrow===!0||_>0&&t._hideNextMonthArrow===!0||(t.currentMonth+=_,(t.currentMonth<0||t.currentMonth>11)&&(t.currentYear+=t.currentMonth>11?1:-1,t.currentMonth=(t.currentMonth+12)%12,st("onYearChange"),tt()),W(),st("onMonthChange"),Sn())}function ee(g,b){if(g===void 0&&(g=!0),b===void 0&&(b=!0),t.input.value="",t.altInput!==void 0&&(t.altInput.value=""),t.mobileInput!==void 0&&(t.mobileInput.value=""),t.selectedDates=[],t.latestSelectedDateObj=void 0,b===!0&&(t.currentYear=t._initialDate.getFullYear(),t.currentMonth=t._initialDate.getMonth()),t.config.enableTime===!0){var _=Xi(t.config),M=_.hours,A=_.minutes,T=_.seconds;f(M,A,T)}t.redraw(),g&&st("onChange")}function he(){t.isOpen=!1,t.isMobile||(t.calendarContainer!==void 0&&t.calendarContainer.classList.remove("open"),t._input!==void 0&&t._input.classList.remove("active")),st("onClose")}function Vt(){t.config!==void 0&&st("onDestroy");for(var g=t._handlers.length;g--;)t._handlers[g].remove();if(t._handlers=[],t.mobileInput)t.mobileInput.parentNode&&t.mobileInput.parentNode.removeChild(t.mobileInput),t.mobileInput=void 0;else if(t.calendarContainer&&t.calendarContainer.parentNode)if(t.config.static&&t.calendarContainer.parentNode){var b=t.calendarContainer.parentNode;if(b.lastChild&&b.removeChild(b.lastChild),b.parentNode){for(;b.firstChild;)b.parentNode.insertBefore(b.firstChild,b);b.parentNode.removeChild(b)}}else t.calendarContainer.parentNode.removeChild(t.calendarContainer);t.altInput&&(t.input.type="text",t.altInput.parentNode&&t.altInput.parentNode.removeChild(t.altInput),delete t.altInput),t.input&&(t.input.type=t.input._type,t.input.classList.remove("flatpickr-input"),t.input.removeAttribute("readonly")),["_showTimeInput","latestSelectedDateObj","_hideNextMonthArrow","_hidePrevMonthArrow","__hideNextMonthArrow","__hidePrevMonthArrow","isMobile","isOpen","selectedDateElem","minDateHasTime","maxDateHasTime","days","daysContainer","_input","_positionElement","innerContainer","rContainer","monthNav","todayDateElem","calendarContainer","weekdayContainer","prevMonthNav","nextMonthNav","monthsDropdownContainer","currentMonthElement","currentYearElement","navigationCurrentMonth","selectedDateElem","config"].forEach(function(_){try{delete t[_]}catch{}})}function Tt(g){return t.calendarContainer.contains(g)}function Rt(g){if(t.isOpen&&!t.config.inline){var b=St(g),_=Tt(b),M=b===t.input||b===t.altInput||t.element.contains(b)||g.path&&g.path.indexOf&&(~g.path.indexOf(t.input)||~g.path.indexOf(t.altInput)),A=!M&&!_&&!Tt(g.relatedTarget),T=!t.config.ignoredFocusElements.some(function(j){return j.contains(b)});A&&T&&(t.config.allowInput&&t.setDate(t._input.value,!1,t.config.altInput?t.config.altFormat:t.config.dateFormat),t.timeContainer!==void 0&&t.minuteElement!==void 0&&t.hourElement!==void 0&&t.input.value!==""&&t.input.value!==void 0&&l(),t.close(),t.config&&t.config.mode==="range"&&t.selectedDates.length===1&&t.clear(!1))}}function _n(g){if(!(!g||t.config.minDate&&g<t.config.minDate.getFullYear()||t.config.maxDate&&g>t.config.maxDate.getFullYear())){var b=g,_=t.currentYear!==b;t.currentYear=b||t.currentYear,t.config.maxDate&&t.currentYear===t.config.maxDate.getFullYear()?t.currentMonth=Math.min(t.config.maxDate.getMonth(),t.currentMonth):t.config.minDate&&t.currentYear===t.config.minDate.getFullYear()&&(t.currentMonth=Math.max(t.config.minDate.getMonth(),t.currentMonth)),_&&(t.redraw(),st("onYearChange"),tt())}}function ue(g,b){var _;b===void 0&&(b=!0);var M=t.parseDate(g,void 0,b);if(t.config.minDate&&M&&kt(M,t.config.minDate,b!==void 0?b:!t.minDateHasTime)<0||t.config.maxDate&&M&&kt(M,t.config.maxDate,b!==void 0?b:!t.maxDateHasTime)>0)return!1;if(!t.config.enable&&t.config.disable.length===0)return!0;if(M===void 0)return!1;for(var A=!!t.config.enable,T=(_=t.config.enable)!==null&&_!==void 0?_:t.config.disable,j=0,L=void 0;j<T.length;j++){if(L=T[j],typeof L=="function"&&L(M))return A;if(L instanceof Date&&M!==void 0&&L.getTime()===M.getTime())return A;if(typeof L=="string"){var V=t.parseDate(L,void 0,!0);return V&&V.getTime()===M.getTime()?A:!A}else if(typeof L=="object"&&M!==void 0&&L.from&&L.to&&M.getTime()>=L.from.getTime()&&M.getTime()<=L.to.getTime())return A}return!A}function vn(g){return t.daysContainer!==void 0?g.className.indexOf("hidden")===-1&&g.className.indexOf("flatpickr-disabled")===-1&&t.daysContainer.contains(g):!1}function nl(g){var b=g.target===t._input,_=t._input.value.trimEnd()!==Si();b&&_&&!(g.relatedTarget&&Tt(g.relatedTarget))&&t.setDate(t._input.value,!0,g.target===t.altInput?t.config.altFormat:t.config.dateFormat)}function zs(g){var b=St(g),_=t.config.wrap?n.contains(b):b===t._input,M=t.config.allowInput,A=t.isOpen&&(!M||!_),T=t.config.inline&&_&&!M;if(g.keyCode===13&&_){if(M)return t.setDate(t._input.value,!0,b===t.altInput?t.config.altFormat:t.config.dateFormat),t.close(),b.blur();t.open()}else if(Tt(b)||A||T){var j=!!t.timeContainer&&t.timeContainer.contains(b);switch(g.keyCode){case 13:j?(g.preventDefault(),l(),wi()):Ys(g);break;case 27:g.preventDefault(),wi();break;case 8:case 46:_&&!t.config.allowInput&&(g.preventDefault(),t.clear());break;case 37:case 39:if(!j&&!_){g.preventDefault();var L=o();if(t.daysContainer!==void 0&&(M===!1||L&&vn(L))){var V=g.keyCode===39?1:-1;g.ctrlKey?(g.stopPropagation(),te(V),N(P(1),0)):N(void 0,V)}}else t.hourElement&&t.hourElement.focus();break;case 38:case 40:g.preventDefault();var R=g.keyCode===40?1:-1;t.daysContainer&&b.$i!==void 0||b===t.input||b===t.altInput?g.ctrlKey?(g.stopPropagation(),_n(t.currentYear-R),N(P(1),0)):j||N(void 0,R*7):b===t.currentYearElement?_n(t.currentYear-R):t.config.enableTime&&(!j&&t.hourElement&&t.hourElement.focus(),l(g),t._debouncedChange());break;case 9:if(j){var I=[t.hourElement,t.minuteElement,t.secondElement,t.amPM].concat(t.pluginElements).filter(function(Dt){return Dt}),K=I.indexOf(b);if(K!==-1){var Bt=I[K+(g.shiftKey?-1:1)];g.preventDefault(),(Bt||t._input).focus()}}else!t.config.noCalendar&&t.daysContainer&&t.daysContainer.contains(b)&&g.shiftKey&&(g.preventDefault(),t._input.focus());break}}if(t.amPM!==void 0&&b===t.amPM)switch(g.key){case t.l10n.amPM[0].charAt(0):case t.l10n.amPM[0].charAt(0).toLowerCase():t.amPM.textContent=t.l10n.amPM[0],u(),Yt();break;case t.l10n.amPM[1].charAt(0):case t.l10n.amPM[1].charAt(0).toLowerCase():t.amPM.textContent=t.l10n.amPM[1],u(),Yt();break}(_||Tt(b))&&st("onKeyDown",g)}function wn(g,b){if(b===void 0&&(b="flatpickr-day"),!(t.selectedDates.length!==1||g&&(!g.classList.contains(b)||g.classList.contains("flatpickr-disabled")))){for(var _=g?g.dateObj.getTime():t.days.firstElementChild.dateObj.getTime(),M=t.parseDate(t.selectedDates[0],void 0,!0).getTime(),A=Math.min(_,t.selectedDates[0].getTime()),T=Math.max(_,t.selectedDates[0].getTime()),j=!1,L=0,V=0,R=A;R<T;R+=fp.DAY)ue(new Date(R),!0)||(j=j||R>A&&R<T,R<M&&(!L||R>L)?L=R:R>M&&(!V||R<V)&&(V=R));var I=Array.from(t.rContainer.querySelectorAll("*:nth-child(-n+"+t.config.showMonths+") > ."+b));I.forEach(function(K){var Bt=K.dateObj,Dt=Bt.getTime(),ze=L>0&&Dt<L||V>0&&Dt>V;if(ze){K.classList.add("notAllowed"),["inRange","startRange","endRange"].forEach(function(Oe){K.classList.remove(Oe)});return}else if(j&&!ze)return;["startRange","inRange","endRange","notAllowed"].forEach(function(Oe){K.classList.remove(Oe)}),g!==void 0&&(g.classList.add(_<=t.selectedDates[0].getTime()?"startRange":"endRange"),M<_&&Dt===M?K.classList.add("startRange"):M>_&&Dt===M&&K.classList.add("endRange"),Dt>=L&&(V===0||Dt<=V)&&up(Dt,M,_)&&K.classList.add("inRange"))})}}function il(){t.isOpen&&!t.config.static&&!t.config.inline&&Mn()}function sl(g,b){if(b===void 0&&(b=t._positionElement),t.isMobile===!0){if(g){g.preventDefault();var _=St(g);_&&_.blur()}t.mobileInput!==void 0&&(t.mobileInput.focus(),t.mobileInput.click()),st("onOpen");return}else if(t._input.disabled||t.config.inline)return;var M=t.isOpen;t.isOpen=!0,M||(t.calendarContainer.classList.add("open"),t._input.classList.add("active"),st("onOpen"),Mn(b)),t.config.enableTime===!0&&t.config.noCalendar===!0&&t.config.allowInput===!1&&(g===void 0||!t.timeContainer.contains(g.relatedTarget))&&setTimeout(function(){return t.hourElement.select()},50)}function Hs(g){return function(b){var _=t.config["_"+g+"Date"]=t.parseDate(b,t.config.dateFormat),M=t.config["_"+(g==="min"?"max":"min")+"Date"];_!==void 0&&(t[g==="min"?"minDateHasTime":"maxDateHasTime"]=_.getHours()>0||_.getMinutes()>0||_.getSeconds()>0),t.selectedDates&&(t.selectedDates=t.selectedDates.filter(function(A){return ue(A)}),!t.selectedDates.length&&g==="min"&&d(_),Yt()),t.daysContainer&&(Vs(),_!==void 0?t.currentYearElement[g]=_.getFullYear().toString():t.currentYearElement.removeAttribute(g),t.currentYearElement.disabled=!!M&&_!==void 0&&M.getFullYear()===_.getFullYear())}}function ol(){var g=["wrap","weekNumbers","allowInput","allowInvalidPreload","clickOpens","time_24hr","enableTime","noCalendar","altInput","shorthandCurrentMonth","inline","static","enableSeconds","disableMobile"],b=ft(ft({},JSON.parse(JSON.stringify(n.dataset||{}))),e),_={};t.config.parseDate=b.parseDate,t.config.formatDate=b.formatDate,Object.defineProperty(t.config,"enable",{get:function(){return t.config._enable},set:function(I){t.config._enable=$s(I)}}),Object.defineProperty(t.config,"disable",{get:function(){return t.config._disable},set:function(I){t.config._disable=$s(I)}});var M=b.mode==="time";if(!b.dateFormat&&(b.enableTime||M)){var A=at.defaultConfig.dateFormat||Te.dateFormat;_.dateFormat=b.noCalendar||M?"H:i"+(b.enableSeconds?":S":""):A+" H:i"+(b.enableSeconds?":S":"")}if(b.altInput&&(b.enableTime||M)&&!b.altFormat){var T=at.defaultConfig.altFormat||Te.altFormat;_.altFormat=b.noCalendar||M?"h:i"+(b.enableSeconds?":S K":" K"):T+(" h:i"+(b.enableSeconds?":S":"")+" K")}Object.defineProperty(t.config,"minDate",{get:function(){return t.config._minDate},set:Hs("min")}),Object.defineProperty(t.config,"maxDate",{get:function(){return t.config._maxDate},set:Hs("max")});var j=function(I){return function(K){t.config[I==="min"?"_minTime":"_maxTime"]=t.parseDate(K,"H:i:S")}};Object.defineProperty(t.config,"minTime",{get:function(){return t.config._minTime},set:j("min")}),Object.defineProperty(t.config,"maxTime",{get:function(){return t.config._maxTime},set:j("max")}),b.mode==="time"&&(t.config.noCalendar=!0,t.config.enableTime=!0),Object.assign(t.config,_,b);for(var L=0;L<g.length;L++)t.config[g[L]]=t.config[g[L]]===!0||t.config[g[L]]==="true";Yi.filter(function(I){return t.config[I]!==void 0}).forEach(function(I){t.config[I]=Ui(t.config[I]||[]).map(r)}),t.isMobile=!t.config.disableMobile&&!t.config.inline&&t.config.mode==="single"&&!t.config.disable.length&&!t.config.enable&&!t.config.weekNumbers&&/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);for(var L=0;L<t.config.plugins.length;L++){var V=t.config.plugins[L](t)||{};for(var R in V)Yi.indexOf(R)>-1?t.config[R]=Ui(V[R]).map(r).concat(t.config[R]):typeof b[R]>"u"&&(t.config[R]=V[R])}b.altInputClass||(t.config.altInputClass=js().className+" "+t.config.altInputClass),st("onParseConfig")}function js(){return t.config.wrap?n.querySelector("[data-input]"):n}function Ws(){typeof t.config.locale!="object"&&typeof at.l10ns[t.config.locale]>"u"&&t.config.errorHandler(new Error("flatpickr: invalid locale "+t.config.locale)),t.l10n=ft(ft({},at.l10ns.default),typeof t.config.locale=="object"?t.config.locale:t.config.locale!=="default"?at.l10ns[t.config.locale]:void 0),xe.D="("+t.l10n.weekdays.shorthand.join("|")+")",xe.l="("+t.l10n.weekdays.longhand.join("|")+")",xe.M="("+t.l10n.months.shorthand.join("|")+")",xe.F="("+t.l10n.months.longhand.join("|")+")",xe.K="("+t.l10n.amPM[0]+"|"+t.l10n.amPM[1]+"|"+t.l10n.amPM[0].toLowerCase()+"|"+t.l10n.amPM[1].toLowerCase()+")";var g=ft(ft({},e),JSON.parse(JSON.stringify(n.dataset||{})));g.time_24hr===void 0&&at.defaultConfig.time_24hr===void 0&&(t.config.time_24hr=t.l10n.time_24hr),t.formatDate=el(t),t.parseDate=bs({config:t.config,l10n:t.l10n})}function Mn(g){if(typeof t.config.position=="function")return void t.config.position(t,g);if(t.calendarContainer!==void 0){st("onPreCalendarPosition");var b=g||t._positionElement,_=Array.prototype.reduce.call(t.calendarContainer.children,function(Dl,Sl){return Dl+Sl.offsetHeight},0),M=t.calendarContainer.offsetWidth,A=t.config.position.split(" "),T=A[0],j=A.length>1?A[1]:null,L=b.getBoundingClientRect(),V=window.innerHeight-L.bottom,R=T==="above"||T!=="below"&&V<_&&L.top>_,I=window.pageYOffset+L.top+(R?-_-2:b.offsetHeight+2);if(xt(t.calendarContainer,"arrowTop",!R),xt(t.calendarContainer,"arrowBottom",R),!t.config.inline){var K=window.pageXOffset+L.left,Bt=!1,Dt=!1;j==="center"?(K-=(M-L.width)/2,Bt=!0):j==="right"&&(K-=M-L.width,Dt=!0),xt(t.calendarContainer,"arrowLeft",!Bt&&!Dt),xt(t.calendarContainer,"arrowCenter",Bt),xt(t.calendarContainer,"arrowRight",Dt);var ze=window.document.body.offsetWidth-(window.pageXOffset+L.right),Oe=K+M>window.document.body.offsetWidth,bl=ze+M>window.document.body.offsetWidth;if(xt(t.calendarContainer,"rightMost",Oe),!t.config.static)if(t.calendarContainer.style.top=I+"px",!Oe)t.calendarContainer.style.left=K+"px",t.calendarContainer.style.right="auto";else if(!bl)t.calendarContainer.style.left="auto",t.calendarContainer.style.right=ze+"px";else{var ki=rl();if(ki===void 0)return;var xl=window.document.body.offsetWidth,yl=Math.max(0,xl/2-M/2),_l=".flatpickr-calendar.centerMost:before",vl=".flatpickr-calendar.centerMost:after",wl=ki.cssRules.length,Ml="{left:"+L.left+"px;right:auto;}";xt(t.calendarContainer,"rightMost",!1),xt(t.calendarContainer,"centerMost",!0),ki.insertRule(_l+","+vl+Ml,wl),t.calendarContainer.style.left=yl+"px",t.calendarContainer.style.right="auto"}}}}function rl(){for(var g=null,b=0;b<document.styleSheets.length;b++){var _=document.styleSheets[b];if(_.cssRules){try{_.cssRules}catch{continue}g=_;break}}return g??al()}function al(){var g=document.createElement("style");return document.head.appendChild(g),g.sheet}function Vs(){t.config.noCalendar||t.isMobile||(tt(),Sn(),W())}function wi(){t._input.focus(),window.navigator.userAgent.indexOf("MSIE")!==-1||navigator.msMaxTouchPoints!==void 0?setTimeout(t.close,0):t.close()}function Ys(g){g.preventDefault(),g.stopPropagation();var b=function(I){return I.classList&&I.classList.contains("flatpickr-day")&&!I.classList.contains("flatpickr-disabled")&&!I.classList.contains("notAllowed")},_=tl(St(g),b);if(_!==void 0){var M=_,A=t.latestSelectedDateObj=new Date(M.dateObj.getTime()),T=(A.getMonth()<t.currentMonth||A.getMonth()>t.currentMonth+t.config.showMonths-1)&&t.config.mode!=="range";if(t.selectedDateElem=M,t.config.mode==="single")t.selectedDates=[A];else if(t.config.mode==="multiple"){var j=Di(A);j?t.selectedDates.splice(parseInt(j),1):t.selectedDates.push(A)}else t.config.mode==="range"&&(t.selectedDates.length===2&&t.clear(!1,!1),t.latestSelectedDateObj=A,t.selectedDates.push(A),kt(A,t.selectedDates[0],!0)!==0&&t.selectedDates.sort(function(I,K){return I.getTime()-K.getTime()}));if(u(),T){var L=t.currentYear!==A.getFullYear();t.currentYear=A.getFullYear(),t.currentMonth=A.getMonth(),L&&(st("onYearChange"),tt()),st("onMonthChange")}if(Sn(),W(),Yt(),!T&&t.config.mode!=="range"&&t.config.showMonths===1?O(M):t.selectedDateElem!==void 0&&t.hourElement===void 0&&t.selectedDateElem&&t.selectedDateElem.focus(),t.hourElement!==void 0&&t.hourElement!==void 0&&t.hourElement.focus(),t.config.closeOnSelect){var V=t.config.mode==="single"&&!t.config.enableTime,R=t.config.mode==="range"&&t.selectedDates.length===2&&!t.config.enableTime;(V||R)&&wi()}x()}}var Dn={locale:[Ws,ct],showMonths:[$,a,yt],minDate:[v],maxDate:[v],positionElement:[qs],clickOpens:[function(){t.config.clickOpens===!0?(m(t._input,"focus",t.open),m(t._input,"click",t.open)):(t._input.removeEventListener("focus",t.open),t._input.removeEventListener("click",t.open))}]};function ll(g,b){if(g!==null&&typeof g=="object"){Object.assign(t.config,g);for(var _ in g)Dn[_]!==void 0&&Dn[_].forEach(function(M){return M()})}else t.config[g]=b,Dn[g]!==void 0?Dn[g].forEach(function(M){return M()}):Yi.indexOf(g)>-1&&(t.config[g]=Ui(b));t.redraw(),Yt(!0)}function Us(g,b){var _=[];if(g instanceof Array)_=g.map(function(M){return t.parseDate(M,b)});else if(g instanceof Date||typeof g=="number")_=[t.parseDate(g,b)];else if(typeof g=="string")switch(t.config.mode){case"single":case"time":_=[t.parseDate(g,b)];break;case"multiple":_=g.split(t.config.conjunction).map(function(M){return t.parseDate(M,b)});break;case"range":_=g.split(t.l10n.rangeSeparator).map(function(M){return t.parseDate(M,b)});break}else t.config.errorHandler(new Error("Invalid date supplied: "+JSON.stringify(g)));t.selectedDates=t.config.allowInvalidPreload?_:_.filter(function(M){return M instanceof Date&&ue(M,!1)}),t.config.mode==="range"&&t.selectedDates.sort(function(M,A){return M.getTime()-A.getTime()})}function cl(g,b,_){if(b===void 0&&(b=!1),_===void 0&&(_=t.config.dateFormat),g!==0&&!g||g instanceof Array&&g.length===0)return t.clear(b);Us(g,_),t.latestSelectedDateObj=t.selectedDates[t.selectedDates.length-1],t.redraw(),v(void 0,b),d(),t.selectedDates.length===0&&t.clear(!1),Yt(b),b&&st("onChange")}function $s(g){return g.slice().map(function(b){return typeof b=="string"||typeof b=="number"||b instanceof Date?t.parseDate(b,void 0,!0):b&&typeof b=="object"&&b.from&&b.to?{from:t.parseDate(b.from,void 0),to:t.parseDate(b.to,void 0)}:b}).filter(function(b){return b})}function hl(){t.selectedDates=[],t.now=t.parseDate(t.config.now)||new Date;var g=t.config.defaultDate||((t.input.nodeName==="INPUT"||t.input.nodeName==="TEXTAREA")&&t.input.placeholder&&t.input.value===t.input.placeholder?null:t.input.value);g&&Us(g,t.config.dateFormat),t._initialDate=t.selectedDates.length>0?t.selectedDates[0]:t.config.minDate&&t.config.minDate.getTime()>t.now.getTime()?t.config.minDate:t.config.maxDate&&t.config.maxDate.getTime()<t.now.getTime()?t.config.maxDate:t.now,t.currentYear=t._initialDate.getFullYear(),t.currentMonth=t._initialDate.getMonth(),t.selectedDates.length>0&&(t.latestSelectedDateObj=t.selectedDates[0]),t.config.minTime!==void 0&&(t.config.minTime=t.parseDate(t.config.minTime,"H:i")),t.config.maxTime!==void 0&&(t.config.maxTime=t.parseDate(t.config.maxTime,"H:i")),t.minDateHasTime=!!t.config.minDate&&(t.config.minDate.getHours()>0||t.config.minDate.getMinutes()>0||t.config.minDate.getSeconds()>0),t.maxDateHasTime=!!t.config.maxDate&&(t.config.maxDate.getHours()>0||t.config.maxDate.getMinutes()>0||t.config.maxDate.getSeconds()>0)}function ul(){if(t.input=js(),!t.input){t.config.errorHandler(new Error("Invalid input element specified"));return}t.input._type=t.input.type,t.input.type="text",t.input.classList.add("flatpickr-input"),t._input=t.input,t.config.altInput&&(t.altInput=q(t.input.nodeName,t.config.altInputClass),t._input=t.altInput,t.altInput.placeholder=t.input.placeholder,t.altInput.disabled=t.input.disabled,t.altInput.required=t.input.required,t.altInput.tabIndex=t.input.tabIndex,t.altInput.type="text",t.input.setAttribute("type","hidden"),!t.config.static&&t.input.parentNode&&t.input.parentNode.insertBefore(t.altInput,t.input.nextSibling)),t.config.allowInput||t._input.setAttribute("readonly","readonly"),qs()}function qs(){t._positionElement=t.config.positionElement||t._input}function dl(){var g=t.config.enableTime?t.config.noCalendar?"time":"datetime-local":"date";t.mobileInput=q("input",t.input.className+" flatpickr-mobile"),t.mobileInput.tabIndex=1,t.mobileInput.type=g,t.mobileInput.disabled=t.input.disabled,t.mobileInput.required=t.input.required,t.mobileInput.placeholder=t.input.placeholder,t.mobileFormatStr=g==="datetime-local"?"Y-m-d\\TH:i:S":g==="date"?"Y-m-d":"H:i:S",t.selectedDates.length>0&&(t.mobileInput.defaultValue=t.mobileInput.value=t.formatDate(t.selectedDates[0],t.mobileFormatStr)),t.config.minDate&&(t.mobileInput.min=t.formatDate(t.config.minDate,"Y-m-d")),t.config.maxDate&&(t.mobileInput.max=t.formatDate(t.config.maxDate,"Y-m-d")),t.input.getAttribute("step")&&(t.mobileInput.step=String(t.input.getAttribute("step"))),t.input.type="hidden",t.altInput!==void 0&&(t.altInput.type="hidden");try{t.input.parentNode&&t.input.parentNode.insertBefore(t.mobileInput,t.input.nextSibling)}catch{}m(t.mobileInput,"change",function(b){t.setDate(St(b).value,!1,t.mobileFormatStr),st("onChange"),st("onClose")})}function fl(g){if(t.isOpen===!0)return t.close();t.open(g)}function st(g,b){if(t.config!==void 0){var _=t.config[g];if(_!==void 0&&_.length>0)for(var M=0;_[M]&&M<_.length;M++)_[M](t.selectedDates,t.input.value,t,b);g==="onChange"&&(t.input.dispatchEvent(Mi("change")),t.input.dispatchEvent(Mi("input")))}}function Mi(g){var b=document.createEvent("Event");return b.initEvent(g,!0,!0),b}function Di(g){for(var b=0;b<t.selectedDates.length;b++){var _=t.selectedDates[b];if(_ instanceof Date&&kt(_,g)===0)return""+b}return!1}function gl(g){return t.config.mode!=="range"||t.selectedDates.length<2?!1:kt(g,t.selectedDates[0])>=0&&kt(g,t.selectedDates[1])<=0}function Sn(){t.config.noCalendar||t.isMobile||!t.monthNav||(t.yearElements.forEach(function(g,b){var _=new Date(t.currentYear,t.currentMonth,1);_.setMonth(t.currentMonth+b),t.config.showMonths>1||t.config.monthSelectorType==="static"?t.monthElements[b].textContent=hi(_.getMonth(),t.config.shorthandCurrentMonth,t.l10n)+" ":t.monthsDropdownContainer.value=_.getMonth().toString(),g.value=_.getFullYear().toString()}),t._hidePrevMonthArrow=t.config.minDate!==void 0&&(t.currentYear===t.config.minDate.getFullYear()?t.currentMonth<=t.config.minDate.getMonth():t.currentYear<t.config.minDate.getFullYear()),t._hideNextMonthArrow=t.config.maxDate!==void 0&&(t.currentYear===t.config.maxDate.getFullYear()?t.currentMonth+1>t.config.maxDate.getMonth():t.currentYear>t.config.maxDate.getFullYear()))}function Si(g){var b=g||(t.config.altInput?t.config.altFormat:t.config.dateFormat);return t.selectedDates.map(function(_){return t.formatDate(_,b)}).filter(function(_,M,A){return t.config.mode!=="range"||t.config.enableTime||A.indexOf(_)===M}).join(t.config.mode!=="range"?t.config.conjunction:t.l10n.rangeSeparator)}function Yt(g){g===void 0&&(g=!0),t.mobileInput!==void 0&&t.mobileFormatStr&&(t.mobileInput.value=t.latestSelectedDateObj!==void 0?t.formatDate(t.latestSelectedDateObj,t.mobileFormatStr):""),t.input.value=Si(t.config.dateFormat),t.altInput!==void 0&&(t.altInput.value=Si(t.config.altFormat)),g!==!1&&st("onValueUpdate")}function pl(g){var b=St(g),_=t.prevMonthNav.contains(b),M=t.nextMonthNav.contains(b);_||M?te(_?-1:1):t.yearElements.indexOf(b)>=0?b.select():b.classList.contains("arrowUp")?t.changeYear(t.currentYear+1):b.classList.contains("arrowDown")&&t.changeYear(t.currentYear-1)}function ml(g){g.preventDefault();var b=g.type==="keydown",_=St(g),M=_;t.amPM!==void 0&&_===t.amPM&&(t.amPM.textContent=t.l10n.amPM[Et(t.amPM.textContent===t.l10n.amPM[0])]);var A=parseFloat(M.getAttribute("min")),T=parseFloat(M.getAttribute("max")),j=parseFloat(M.getAttribute("step")),L=parseInt(M.value,10),V=g.delta||(b?g.which===38?1:-1:0),R=L+j*V;if(typeof M.value<"u"&&M.value.length===2){var I=M===t.hourElement,K=M===t.minuteElement;R<A?(R=T+R+Et(!I)+(Et(I)&&Et(!t.amPM)),K&&k(void 0,-1,t.hourElement)):R>T&&(R=M===t.hourElement?R-T-Et(!t.amPM):A,K&&k(void 0,1,t.hourElement)),t.amPM&&I&&(j===1?R+L===23:Math.abs(R-L)>j)&&(t.amPM.textContent=t.l10n.amPM[Et(t.amPM.textContent===t.l10n.amPM[0])]),M.value=vt(R)}}return s(),t}function Re(n,e){for(var t=Array.prototype.slice.call(n).filter(function(r){return r instanceof HTMLElement}),i=[],s=0;s<t.length;s++){var o=t[s];try{if(o.getAttribute("data-fp-omit")!==null)continue;o._flatpickr!==void 0&&(o._flatpickr.destroy(),o._flatpickr=void 0),o._flatpickr=pp(o,e||{}),i.push(o._flatpickr)}catch(r){console.error(r)}}return i.length===1?i[0]:i}typeof HTMLElement<"u"&&typeof HTMLCollection<"u"&&typeof NodeList<"u"&&(HTMLCollection.prototype.flatpickr=NodeList.prototype.flatpickr=function(n){return Re(this,n)},HTMLElement.prototype.flatpickr=function(n){return Re([this],n)});var at=function(n,e){return typeof n=="string"?Re(window.document.querySelectorAll(n),e):n instanceof Node?Re([n],e):Re(n,e)};at.defaultConfig={};at.l10ns={en:ft({},gn),default:ft({},gn)};at.localize=function(n){at.l10ns.default=ft(ft({},at.l10ns.default),n)};at.setDefaults=function(n){at.defaultConfig=ft(ft({},at.defaultConfig),n)};at.parseDate=bs({});at.formatDate=el({});at.compareDates=kt;typeof jQuery<"u"&&typeof jQuery.fn<"u"&&(jQuery.fn.flatpickr=function(n){return Re(this,n)});Date.prototype.fp_incr=function(n){return new Date(this.getFullYear(),this.getMonth(),this.getDate()+(typeof n=="string"?parseInt(n,10):n))};typeof window<"u"&&(window.flatpickr=at);var mp=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Rr={exports:{}};(function(n,e){(function(t,i){i(e)})(mp,function(t){var i=typeof window<"u"&&window.flatpickr!==void 0?window.flatpickr:{l10ns:{}},s={weekdays:{shorthand:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],longhand:["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"]},months:{shorthand:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agu","Sep","Okt","Nov","Des"],longhand:["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"]},firstDayOfWeek:1,ordinal:function(){return""},time_24hr:!0,rangeSeparator:" - "};i.l10ns.id=s;var o=i.l10ns;t.Indonesian=s,t.default=o,Object.defineProperty(t,"__esModule",{value:!0})})})(Rr,Rr.exports);window.Chart=Kt;window.flatpickr=at;
