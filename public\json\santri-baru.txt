saya punya data json, saya juga sudah punya model baru, buatkan seed untuk input ke table santris, nama model <PERSON><PERSON>

struktur untuk model <PERSON><PERSON> seperti ini

TABLE `santris` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `nama` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tanggal_lahir` date NOT NULL,
  `informasi_tambahan` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `nomor_induk` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nomor_induk_baru` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tempat_lahir` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `jenis_kelamin` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `foto` text COLLATE utf8mb4_unicode_ci,
  `hp` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `alamat` text COLLATE utf8mb4_unicode_ci,
  `kabupaten` text COLLATE utf8mb4_unicode_ci,
  `provinsi` text COLLATE utf8mb4_unicode_ci,
  `nik` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `kk` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `foto_ktp` text COLLATE utf8mb4_unicode_ci,
  `foto_kk` text COLLATE utf8mb4_unicode_ci,
  `nama_ayah` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nama_ibu` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pekerjaan_ayah` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pekerjaan_ibu` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hp_orang_tua` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `alamat_orang_tua` text COLLATE utf8mb4_unicode_ci,
  `anak_ke` int DEFAULT NULL,
  `jumlah_saudara` int DEFAULT NULL,
  `pendidikan_terakhir` text COLLATE utf8mb4_unicode_ci,
  `pendidikan_formal` json DEFAULT NULL,
  `pendidikan_non_formal` json DEFAULT NULL,
  `nomor_peserta` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `id_komplek` bigint unsigned DEFAULT NULL,
  `id_admin` bigint unsigned NOT NULL,
  `id_kamar` bigint unsigned DEFAULT NULL,
  `diterima` tinyint(1) DEFAULT NULL,
  `diterima_at` datetime DEFAULT NULL,
  `tahun_masuk` year DEFAULT NULL,
  `alasan` text COLLATE utf8mb4_unicode_ci,
  `is_aktif` tinyint(1) NOT NULL,
  `aktif_at` datetime DEFAULT NULL,
  `is_nonaktif` tinyint(1) NOT NULL,
  `nonaktif_at` datetime DEFAULT NULL,
  `is_alumni` tinyint(1) NOT NULL,
  `alumni_at` datetime DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

Sumber json seperti ini, berada di folder public/json, nama file almunaww_siap2_table_santri.json, struktur file json seperti ini

{
    "type": "table",
    "name": "santri",
    "database": "almunaww_siap2",
    "data": [
        {
            "id_santri": "1656",
            "unik": "60beefc8ebbda5.35683689",
            "nomor_induk": "19111800656",
            "alamat": "Jl. R.A Basoeni , Desa jampirogo, Kec Sooko, kab Mojokerto",
            "hp": "089676958536",
            "email": "

{
    "type": "table",
    "name": "santri",
    "database": "almunaww_siap2",
    "data": [
        {
            "id_santri": "1656",
            "unik": "60beefc8ebbda5.35683689",
            "nomor_induk": "19111800656",
            "alamat": "Jl. R.A Basoeni , Desa jampirogo, Kec Sooko, kab Mojokerto",
            "hp": "089676958536",
            "email": "<EMAIL>",
            "lahir": "KABUPATEN MOJOKERTO",
            "kelamin": "laki-laki",
            "ortu": "",
            "tgl": "06",
            "bln": "04",
            "thn": "2000",
            "pend_terakhir": "SMA\/MA",
            "jamdaftar": "11:19:20",
            "status": "1",
            "anak_ke": "1",
            "jmlsaudara": "3",
            "jmlmahrom": "",
            "mahrom": "",
            "pernah_mondok": "",
            "pondok_sebelumnya": "",
            "tujuan_mondok": "",
            "pilihan_program": "",
            "ayah": "Muhammad Mas'ud",
            "ibu": "Nurul Faizah",
            "pekerjaan_ayah": "Guru Swasta",
            "pekerjaan_ibu": "IBU RUMAH TANGGA",
            "pend_terakhir_ayah": "S2",
            "pend_terakhir_ibu": "S1",
            "alamat_ortu": "Jl. RA. Basoeni, Desa Jampirogo, Kec Sooko, Kab Mojokerto",
            "telp_ortu": "",
            "tanggal_diterima": "2021-06-08",
            "foto": "19111800656.jpeg",
            "id_lemari": "0",
            "nama": "Mukhammad Khafidl Wildani",
            "pengantar": "",
            "namanya": "",
            "last_update": "2021-06-08",
            "alamat_pengantar": "",
            "saudarakandung": "",
            "pengurus": "0",
            "baru": "0",
            "kosmakan": "0",
            "petugas": "133",
            "tanggal_lulus": "0000-00-00",
            "keterangan_status": "",
            "tanggal_aktif": "2021-06-08",
            "tanggalfoto": "2021-06-08",
            "sekolah1": "10",
            "sekolah2": "Pilih Sekolah",
            "sekolah3": "",
            "tanggallahir": "2000-04-06",
            "fb": "",
            "ig": "",
            "nik": "3516130406000003",
            "kemampuanbhs": "Arab",
            "darah": "",
            "keahlian": "",
            "hobi": "Membaca",
            "penghargaan": "",
            "kk": "3516132811020475",
            "nikayah": "",
            "nikibu": "",
            "penghasilan": "",
            "hportu": "082333035596",
            "ketertarikan": "",
            "jikakursus": "",
            "alasan": "Mencari ilmu agama, terlebih dalam bidang menghafalkan al-Qur'an",
            "motor": "ya",
            "laundry": "tidak",
            "platmotor": "L 3799 NE",
            "kelas1": "0",
            "kelas2": "0",
            "nonformal": "Ponpes Mamba'us Sholihin",
            "formal": "Ponpes Mamba'us Sholihin",
            "id_kamar": "0",
            "pekerjaan": "",
            "tahunmondok": "2021",
            "id_kompleks_asrama": "18"
        },

jika id_kompleks_asrama bukan angka maka data santri ini tidak usah diinput
jika tahunmondok tidak sesuai format, semisal 0000 ini kan bukan tahun, dan lainnya maka tahun_masuk diambil dari tahun yang ada di tanggal_diterima


nama = nama
tanggal_lahir = tanggallahir
informasi_tambahan = "tidak ada"
nomor_induk = nomor_induk
nomor_induk_baru = null
tempat_lahir = lahir
jenis_kelamin = kelamin
foto = "photos/" . foto
hp = hp
email = email
alamat = alamat
kabupaten = null
provinsi = null
nik = nik
kk = kk
foto_ktp = null
foto_kk = null
nama_ayah = ayah
nama_ibu = ibu
pekerjaan_ayah = pekerjaan_ayah
pekerjaan_ibu = pekerjaan_ibu
hp_orang_tua = hportu
alamat_orang_tua = alamat_ortu
anak_ke = anak_ke
jumlah_saudara = jmlsaudara
pendidikan_terakhir = ""
pendidikan_formal = null
pendidikan_non_formal = null
nomor_peserta = null
id_komplek = id_kompleks_asrama
id_admin = 1
id_kamar = null
diterima = 1
diterima_at = tanggal_aktif + jam 00:00:00 (sesuaikan jadi format mysql datetime)
tahun_masuk = tahunmondok
alasan = alasan
is_aktif = jika status == 1 maka 1, 0
aktif_at = tanggal_aktif + jam 00:00:00 (sesuaikan jadi format mysql datetime)
is_nonaktif = jika status == 0 maka 1, 0
is_alumni = jika status == 2 maka 1, 0


pastikan data yang diinput ke table santris sesuai dengan struktur model Santri, dan data sesuai aturan
