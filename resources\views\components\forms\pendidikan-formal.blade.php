@props(['pendidikanOptions'])

<div x-data="{ open: @entangle($attributes->wire('model')).defer, tempPendidikanFormal: { pendidikan: '', instansi: '', tahun_lulus: '' } }">
    <!-- Button to open the modal -->
    <x-mary-button type="button" icon="o-plus" @click="open = true" spinner>Tambah Pendidikan Formal</x-mary-button>

    <!-- Modal Pendidikan Formal -->
    <div x-show="open" @click.away="open = false" class="fixed inset-0 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 shadow-lg w-full max-w-md">
            <h3 class="text-lg font-semibold mb-4">Tambah Pendidikan Formal</h3>
            <div class="space-y-4">
                <x-mary-select x-model="tempPendidikanFormal.pendidikan" :options="$pendidikanOptions" placeholder="Pendidikan" />
                <x-mary-input x-model="tempPendidikanFormal.instansi" placeholder="Instansi" />
                <x-mary-input type="number" x-model="tempPendidikanFormal.tahun_lulus" placeholder="Tahun Lulus" />
            </div>
            <div class="flex justify-end mt-4 space-x-2">
                <x-mary-button @click="$wire.addPendidikanFormal(tempPendidikanFormal); tempPendidikanFormal = { pendidikan: '', instansi: '', tahun_lulus: '' }; open = false">Tambah</x-mary-button>
                <x-mary-button @click="open = false">Batal</x-mary-button>
            </div>
        </div>
    </div>
</div>
