<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Selamat Datang '. auth()->user()->name) }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-6">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg sm:rounded-tl-[4.4rem] p-4">
                <div class="flex flex-row gap-4">
                    <img src="{{ asset('images/huffadhkrapyak.png') }}" alt="" class="w-32 h-32 avatar rounded-full">
                    <div class="flex flex-col gap-2">
                        <div>
                            <h2 class="font-extrabold tracking-widest uppercase text-lg">Al-Munawwir</h2>
                            <p class="text-gray-600 tracking-wide">Sistem Informasi Administrasi Pesantren</p>
                        </div>
                        <p class="text-gray-500 italic">Selamat datang di halaman dashboard, silahkan pilih menu yang tersedia.</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-4">
                <div class="flex flex-col gap-8">
                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Data Santri</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="santri.index" svgName="fluentui-people-48-o" title="Data Santri" />
                            <x-dashboard.link-button route="santri.create" svgName="fluentui-people-add-16-o" title="Tambah Santri" />
                            <x-dashboard.link-button route="kts.index" svgName="hugeicons-student-card" title="Cetak Kartu" />
                        </div>
                    </div>

                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Data Komplek</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="asrama.index" svgName="fluentui-building-retail-more-20-o" title="Data Komplek" />
                            <x-dashboard.link-button route="asrama.create" svgName="fluentui-add-circle-28-o" title="Tambah Komplek" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-4">
                <div class="flex flex-col gap-8">
                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Pengaturan dan Akun</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="users.index" svgName="hugeicons-account-setting-01" title="Kelola Admin" />
                            <x-dashboard.link-button route="santri.index" svgName="fluentui-person-28-o" title="Profil" />
                        </div>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="santri.index" svgName="fluentui-settings-24-o" title="Website" />
                            <x-dashboard.link-button route="santri.index" svgName="fluentui-developer-board-20-o" title="Developer" />

                        </div>
                    </div>


                </div>
            </div>
        </div>

    </div>
</x-app-layout>
