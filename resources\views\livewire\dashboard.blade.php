<div>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Selamat Datang ' . auth()->user()->name) }}
        </h2>
    </x-slot>
    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-6">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg sm:rounded-tl-[4.4rem] p-4">
                <div class="flex flex-row gap-4">
                    <img src="{{ $logo }}" alt="" class="w-32 h-32 avatar rounded-full">
                    <div class="flex flex-col gap-2">
                        <div>
                            <h2 class="font-extrabold tracking-widest uppercase text-lg">{{ $name }}</h2>
                            <p class="text-gray-600 tracking-wide">Sistem Informasi Administrasi Pesantren</p>
                        </div>
                        <p class="text-gray-500 italic">Selamat datang di halaman dashboard, silahkan pilih menu yang
                            tersedia.</p>
                    </div>
                </div>
            </div>
        </div>

        {{-- Jika ada peringatan tampilkan di sini --}}
        <livewire:warnings-component lazy />



        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-4">
            <x-mary-collapse wire:model="showRangkuman" separator class="bg-base-200 bg-gradient-to-r from-teal-700 to-teal-900">
                <x-slot:heading><div class="text-white">Rangkuman Data</div></x-slot:heading>
                <x-slot:content>
                    <div class="flex flex-col sm:flex-row gap-4 p-2 sm:p-0 items-center ">
                        @if (auth()-> user() -> role -> first() -> name === 'superadmin')
                        <livewire:widgets.total-komplek class="shadow-lg" lazy />
                        @endif

                        <div class="flex flex-row sm:flex-col  gap-4 ">
                            <livewire:widgets.total-santri class="shadow-lg" lazy />
                            <livewire:widgets.total-santri-alumni class="shadow-lg" lazy />
                        </div>
                        <div class="bg-white p-2 shadow-lg rounded-md">
                            <livewire:widgets.statistik-tahun-masuk batasan-tahun="5" />
                        </div>

                        @if (auth()-> user() -> role -> first() -> name === 'superadmin')
                        <div class="flex flex-row sm:flex-col  gap-4 ">
                            <livewire:widgets.santri-putra class="shadow-lg" lazy />
                            <livewire:widgets.santri-putri class="shadow-lg" lazy />
                        </div>
                        @endif

                        <div>
                            <x-mary-button link="{{ route('stats.index') }}" label="Selengkapnya"
                                icon="o-chart-pie" class="bg-white" />
                        </div>


                    </div>


                    <x-mary-modal wire:model="modalPengembangan" class="backdrop-blur">
                        <div class="mb-5">Halaman Rangkuman Data masih dalam pengembangan, mohon menunggu. Akan kami
                            informasikan secepatnya jika sudah tersedia. Terimakasih ☺️.</div>
                        <x-mary-button label="Tutup" @click="$wire.modalPengembangan = false" />
                    </x-mary-modal>




                </x-slot:content>
            </x-mary-collapse>


        </div>

        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-4">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-4">
                <div class="flex flex-col gap-8">
                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Data Santri</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="{{ route('santri.index') }}" svgName="fluentui-people-48-o"
                                title="Data Santri" />
                            <x-dashboard.link-button route="{{ route('santri.create') }}"
                                svgName="fluentui-people-add-16-o" title="Tambah Santri" />
                            @if (Auth::user()->is_superadmin)
                            <x-dashboard.link-button route="{{ route('kts.index') }}" svgName="hugeicons-student-card"
                            title="Cetak Kartu" />
                            @endif
                        </div>
                    </div>

                    @if (Auth::user()->is_superadmin)
                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Data Komplek</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="{{ route('asrama.index') }}"
                                svgName="fluentui-building-retail-more-20-o" title="Data Komplek" />
                            <x-dashboard.link-button route="{{ route('asrama.create') }}"
                                svgName="fluentui-add-circle-28-o" title="Tambah Komplek" />
                        </div>
                    </div>
                    @elseif (Auth::user()->is_admin)
                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Detail Komplek</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button
                                route="{{ route('asrama.detail', ['id' => Auth::user()->roleUsers()->value('id_komplek')]) }}"
                                svgName="fluentui-building-retail-more-20-o" title="Detail Komplek" />

                        </div>
                    </div>

                    @endif
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-4">
                <div class="flex flex-col gap-8">
                    <div class="flex flex-col gap-3">
                        <h2 class="font-extrabold tracking-widest uppercase text-lg">Pengaturan dan Akun</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="{{ route('users.index') }}"
                                svgName="hugeicons-account-setting-01" title="Kelola Admin" />
                            <x-dashboard.link-button route="{{ route('users.edit', ['userId' => Auth::user()->id]) }}"
                                svgName="fluentui-person-28-o" title="Profil" />
                        </div>
                        @if (Auth::user()->is_superadmin)
                        <div class="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 gap-4">
                            <x-dashboard.link-button route="{{ route('setting.general') }}"
                                svgName="fluentui-settings-24-o" title="Website" />
                            <x-dashboard.link-button route="{{ route('setting.kts') }}"
                                svgName="fluentui-developer-board-20-o" title="KTS" />

                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
