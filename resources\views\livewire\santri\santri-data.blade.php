<div>
    {{-- Care about people's approval and you will be their prisoner. --}}



    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 bg-white mt-6 rounded-tl-badge shadow-lg">
        <x-mary-header title="Data Santri" subtitle="Data Santri PP. Al-Munawwir"
            class="bg-white overflow-hidden  sm:rounded-lg p-2">
            <x-slot:middle class="!justify-end">
                <x-mary-input icon="o-magnifying-glass" placeholder="Search..." wire:model.live="search" />
            </x-slot:middle>
            <x-slot:actions>
                <x-mary-button icon="o-funnel" @click="$wire.showFilterDrawer = true" />
                <x-mary-button icon="o-plus" class="btn-primary" link="{{ route('santri.create') }}" />
            </x-slot:actions>
        </x-mary-header>
    </div>

    <x-mary-drawer wire:model="showFilterDrawer" title="Filter Data Santri"
        subtitle="Filter data santri berdasarkan kriteria yang diinginkan." separator with-close-button close-on-escape
        right class="w-11/12 lg:w-1/3">
        <div class="space-y-4">
            <x-mary-select label="Komplek" wire:model="komplek" :options="$kompleks" option-label="nama_komplek"
                option-value="id" placeholder="Pilih Komplek" />
            <x-mary-select label="Jenis Kelamin" wire:model="jenis_kelamin" :options="[
                ['id' => '', 'name' => 'Semua'],
                ['id' => 'laki-laki', 'name' => 'Laki-laki'],
                ['id' => 'perempuan', 'name' => 'Perempuan'],
            ]" option-label="name"
                option-value="id" placeholder="Pilih Jenis Kelamin" />
            <x-mary-input type="number" label="Tahun Masuk" wire:model="tahun_masuk"
                placeholder="Masukkan Tahun Masuk" />
            <x-mary-select label="Status" wire:model="status" :options="[
                ['id' => 'is_aktif', 'name' => 'Aktif'],
                ['id' => 'is_nonaktif', 'name' => 'Non-Aktif'],
                ['id' => 'is_alumni', 'name' => 'Alumni'],
            ]" option-label="name" option-value="id"
                placeholder="Pilih Status" />
        </div>

        <x-slot:actions>
            <x-mary-button class="btn-error" label="Reset Filter" @click="$wire.resetFilters()" />
            <x-mary-button label="Terapkan Filter" class="btn-primary" icon="o-check" @click="$wire.applyFilters()"
                spinner />
        </x-slot:actions>
    </x-mary-drawer>

    <div class="py-6">
        <div class="max-w-7xl mx-auto ">

            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg">


                <div>

                    <div class="p-8  text-center w-full" wire:loading wire:target="search, resetFilters, applyFilters">
                        <x-mary-loading class="text-primary loading-lg" target="search, resetFilters, applyFilters" />
                    </div>


                    @if (session()->has('message'))
                        <x-mary-alert type="success" class="alert-success">
                            {{ session('message') }}
                        </x-mary-alert>
                    @endif

                    <div class="space-y-4 p-4" wire:loading.remove wire:target="search, resetFilters, applyFilters">

                        <div class="mb-4">
                            <h2 class="font-semibold mb-2">Filter yang Digunakan:</h2>

                            <div class="flex flex-wrap gap-2">
                                <div class="mb-4 space-x-2 flex flex-wrap gap-2 items-center">
                                    @if ($komplek)
                                        <div
                                            class="inline-flex items-center space-x-1 bg-primary px-3 py-1 rounded-full text-white text-sm font-semibold">
                                            <x-mary-badge
                                                value="{{ \App\Models\Komplek::find($komplek)?->nama_komplek ?? '-' }}"
                                                class="badge-primary badge-sm" />
                                            <button type="button" wire:click="$set('komplek', null)"
                                                aria-label="Hapus komplek"
                                                class="ml-1 font-bold hover:text-gray-200">×</button>
                                        </div>
                                    @endif

                                    @if ($kabupaten)
                                        <div
                                            class="inline-flex items-center space-x-1 bg-secondary px-3 py-1 rounded-full text-white text-sm font-semibold">
                                            <x-mary-badge
                                                value="{{ \Laravolt\Indonesia\Models\City::where('code', $kabupaten)->first()?->name ?? '-' }}"
                                                class="badge-secondary badge-sm" />
                                            <button type="button" wire:click="$set('kabupaten', null)"
                                                aria-label="Hapus kabupaten"
                                                class="ml-1 font-bold hover:text-gray-200">×</button>
                                        </div>
                                    @endif

                                    @if ($tahun_masuk)
                                        <div
                                            class="inline-flex items-center space-x-1 bg-info px-3 py-1 rounded-full text-white text-sm font-semibold">
                                            <x-mary-badge value="Tahun Masuk: {{ $tahun_masuk }}"
                                                class="badge-info badge-sm" />
                                            <button type="button" wire:click="$set('tahun_masuk', null)"
                                                aria-label="Hapus tahun masuk"
                                                class="ml-1 font-bold hover:text-gray-200">×</button>
                                        </div>
                                    @endif

                                    @if ($jenis_kelamin)
                                        <div
                                            class="inline-flex items-center space-x-1 bg-warning px-3 py-1 rounded-full text-white text-sm font-semibold">
                                            <x-mary-badge value="Jenis Kelamin: {{ ucfirst($jenis_kelamin) }}"
                                                class="badge-warning badge-sm" />
                                            <button type="button" wire:click="$set('jenis_kelamin', null)"
                                                aria-label="Hapus jenis kelamin"
                                                class="ml-1 font-bold hover:text-gray-200">×</button>
                                        </div>
                                    @endif

                                    @if ($status)
                                        <div
                                            class="inline-flex items-center space-x-1 bg-error px-3 py-1 rounded-full text-white text-sm font-semibold">
                                            @php
                                                $statusLabel = match ($status) {
                                                    'is_aktif' => 'Aktif',
                                                    'is_nonaktif' => 'Nonaktif',
                                                    'is_alumni' => 'Alumni',
                                                    default => $status,
                                                };
                                            @endphp
                                            <x-mary-badge value="Status: {{ $statusLabel }}"
                                                class="badge-error badge-sm" />
                                            <button type="button" wire:click="$set('status', null)"
                                                aria-label="Hapus status"
                                                class="ml-1 font-bold hover:text-gray-200">×</button>
                                        </div>
                                    @endif

                                    @if ($search)
                                        <div
                                            class="inline-flex items-center space-x-1 bg-neutral px-3 py-1 rounded-full text-white text-sm font-semibold">
                                            <x-mary-badge value="Cari: &quot;{{ $search }}&quot;"
                                                class="badge-neutral badge-sm" />
                                            <button type="button" wire:click="$set('search', null)"
                                                aria-label="Hapus pencarian"
                                                class="ml-1 font-bold hover:text-gray-200">×</button>
                                        </div>
                                    @endif
                                </div>

                            </div>

                            @if ($komplek || $kabupaten || $tahun_masuk || $jenis_kelamin || $status || $search)
                                <div class="mt-3">
                                    <x-mary-button color="red" size="sm" wire:click="resetFilters">
                                        Reset Semua Filter
                                    </x-mary-button>
                                </div>
                            @endif
                        </div>

                        @if (count($santri) < 1)
                            <x-mary-alert type="info">
                                Data Santri tidak ditemukan.
                            </x-mary-alert>
                        @else
                            @foreach ($santri as $item)
                                <x-mary-list-item :link="route('santri.detail', $item->id)" no-wire-navigate :item="$item" no-separator>
                                    <x-slot:avatar>
                                        <img src="{{ asset('storage/' . $item->foto) }}" alt="{{ $item->nama }}"
                                            class="w-10 h-auto  rounded-box">
                                    </x-slot:avatar>
                                    <x-slot:value>
                                        <span class="uppercase">{{ $item->nama }}</span>
                                    </x-slot:value>
                                    <x-slot:sub-value>
                                        {{ $item->komplek->nama_komplek }}
                                    </x-slot:sub-value>

                                    <x-slot:actions>
                                        <div class="flex flex-col items-end">
                                            @if ($item->is_nonaktif)
                                                <x-mary-badge value="Nonaktif" class="badge-error" />
                                            @elseif($item->is_alumni)
                                                <x-mary-badge value="Alumni" class="badge-neutral" />
                                            @endif
                                            <span>{{ $item->tahun_masuk }}</span>
                                        </div>
                                    </x-slot:actions>
                                </x-mary-list-item>
                            @endforeach
                        @endif
                    </div>

                    <div class="mt-4 p-4">
                        {{ $santri->links() }}
                    </div>

                </div>

            </div>

        </div>

    </div>
</div>
