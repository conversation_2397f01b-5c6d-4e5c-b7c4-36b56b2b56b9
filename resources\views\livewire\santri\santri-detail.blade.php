<div>
    {{-- If you look to others for fulfillment, you will never truly be fulfilled. --}}

    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">


        <div class="bg-white p-4 flex items-center flex-wrap">
            <ul class="flex items-center">
                <li class="inline-flex items-center">
                    <a href="{{ route('dashboard') }}" class="text-gray-600 hover:text-blue-500">
                        <svg class="w-5 h-auto fill-current mx-2 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24" fill="#000000">
                            <path d="M0 0h24v24H0V0z" fill="none" />
                            <path
                                d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" />
                        </svg>
                    </a>

                    <span class="mx-4 h-auto text-gray-400 font-medium">/</span>
                </li>

                <li class="inline-flex items-center">
                    <a wire:navigate href="{{ route('santri.index') }}" class="text-gray-600 hover:text-blue-500">
                        Santri
                    </a>

                    <span class="mx-4 h-auto text-gray-400 font-medium">/</span>
                </li>

                <li class="inline-flex items-center">
                    <a href="#" class="text-gray-600 hover:text-blue-500  uppercase">
                        {{ $santri->nama }}
                    </a>
                </li>


            </ul>
        </div>

        <div class="mt-8 grid grid-cols-1  gap-4">
            <div class="bg-white p-6 rounded-2xl shadow-md">
                <h2 class="text-2xl font-semibold mb-4 uppercase">{{ $santri->nama }}</h2>
                <div class="flex flex-wrap space-y-4 lg:space-y-0 lg:space-x-4">
                    <!-- Left Section -->
                    <div class="flex-1 space-y-6">
                        <!-- Profile -->
                        <div class="flex items-center space-x-4">
                            <div
                                class="w-16 h-16 rounded-full bg-purple-600 flex items-center justify-center text-white text-2xl">
                                <img class="w-16 h-16 object-cover rounded-full"
                                    src="{{ asset('storage/' . $santri->foto) }}" alt="">
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold">{{ $santri->komplek->nama_komplek }}</h3>
                                <p class="text-gray-500">{{ $santri->hp }}</p>
                                <p class="text-gray-500">{{ $santri->email }}</p>
                            </div>
                        </div>
                        <!-- Metrics -->
                        <div class="grid grid-cols-2 gap-4">
                            <div class="flex items-center space-x-2 sm:space-x-4 bg-gray-100 px-1 py-4 rounded-lg">
                                <div class="text-3xl text-yellow-500"><i class="fas fa-chart-line"></i></div>
                                <div class="text-lg font-semibold">Tahun Masuk<br><span
                                        class="text-gray-500">{{ $santri->tahun_masuk }}</span></div>
                            </div>
                            <div class="flex items-center space-x-2 sm:space-x-4 bg-gray-100 px-1 py-4 rounded-lg">
                                <div class="text-3xl text-green-500"><i class="fas fa-tags"></i></div>
                                <div class="text-lg font-semibold">Status Santri<br>
                                    <div>
                                        @if ($santri->is_aktif)
                                            <x-mary-icon name="o-check-circle" label="Aktif" class="text-teal-700" />
                                        @elseif($santri->is_nonaktif)
                                            <x-mary-icon name="o-x-circle" label="Nonaktif" class="text-red-700" />
                                        @elseif($santri->is_alumni)
                                            <div class="inline-flex items-center gap-1">
                                                <x-heroicon-o-academic-cap class="w-5 text-blue-700" />
                                                <div class="text-blue-700">
                                                    Alumni
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2 sm:space-x-4 bg-gray-100 px-1 py-4  rounded-lg">
                                <div class="text-3xl text-purple-500"><i class="fas fa-chart-pie"></i></div>

                                @if ($santri->nomor_induk_baru)
                                    <div class="text-lg font-semibold">Nomor Induk<br><span
                                            class="text-gray-500">{{ $santri->nomor_induk_baru }}</span></div>
                                @else
                                    <div class="sm:flex-col flex-row">
                                        <div class="text-lg font-semibold">Nomor Induk<br></div>
                                        <x-mary-button class="btn-sm btn-primary" icon-right="o-plus"
                                            label="Nomor Induk" wire:click="modalNomorInduk = true" />
                                    </div>
                                @endif
                            </div>
                            {{-- MODAL TAMBAH NOMOR INDUK --}}
                            <x-mary-modal wire:model="modalNomorInduk" class="backdrop-blur" box-class=" p-5 w-5/4"
                                title="Nomor Induk Santri" subtitle="Buat Nomor Induk Santri" separator persistent>
                                <div class="flex flex-col gap-4">
                                    <x-mary-input readonly label="Nama Santri" value="{{ $santri->nama }}" />
                                    <x-mary-input readonly label="Angkatan Masuk" value="{{ $santri->tahun_masuk }}" />
                                    <x-mary-input readonly label="Komplek"
                                        value="{{ $santri->komplek->nama_komplek }}" />

                                    @if ($santri->nomor_induk_baru)
                                        <div class="w-full p-2 bg-teal-400 rounded-lg mt-4">
                                            <p>Nomor Induk : {{ $santri->nomor_induk_baru }}</p>
                                        </div>
                                    @endif
                                </div>

                                <x-slot:actions>

                                    @if ($santri->nomor_induk_baru)
                                        <x-mary-button label="Tutup" wire:click="modalNomorInduk = false" />
                                    @else
                                        <x-mary-button label="Batalkan" wire:click="modalNomorInduk = false" />
                                        <x-mary-button label="Buat Nomor Induk" wire:click="generateNomorInduk"
                                            class="btn-primary" spinner />
                                    @endif
                                </x-slot:actions>
                            </x-mary-modal>

                            <div class="flex items-center space-x-2 sm:space-x-4 bg-gray-100 px-1 py-4 rounded-lg">
                                <div class="text-3xl text-red-500"><i class="fas fa-undo"></i></div>
                                <div class="text-lg font-semibold">Jenis Kelamin<br><span class="text-gray-500">
                                        @if ($santri->jenis_kelamin == 'laki-laki')
                                            Laki-laki
                                        @elseif ($santri->jenis_kelamin == 'perempuan')
                                            Perempuan
                                        @endif
                                    </span></div>
                            </div>
                        </div>
                        <!-- Sales Plan -->
                        {{-- <div class="bg-gray-100 p-4 rounded-lg"> --}}
                        {{-- <h3 class="text-3xl font-semibold">Sales plan</h3> --}}
                        {{-- <p class="text-5xl font-bold text-gray-900">65%</p> --}}
                        {{-- <p class="text-gray-500">Percentage profit from total sales</p> --}}
                        {{-- </div> --}}
                    </div>
                    <!-- Right Section -->
                    <div class="w-full lg:w-1/3 bg-gray-300 text-white p-6 rounded-lg flex flex-col items-center">

                        <div class="flex-1 flex flex-col items-center justify-center">

                            <x-mary-file wire:model="fotoSantri" accept="image/png, image/jpeg"
                                change-text="Ganti Foto Santri">
                                <img src="{{ $this->getFotoUrl() }}" alt="Foto Santri" class="rounded-lg max-h-64"
                                    id="fotoSantriImage">
                            </x-mary-file>

                            @if ($fotoSantri)
                                <div class="flex flex-row gap-2 mt-4">
                                    <x-mary-button spinner="saveFotoSantri" label="Simpan" wire:click="saveFotoSantri"
                                        class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out" />
                                    <x-mary-button spinner="cancelFotoSantri" label="Batalkan"
                                        wire:click="cancelFotoSantri"
                                        class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out" />
                                </div>
                            @endif
                        </div>
                    </div>
                </div>



            </div>

            <div class="flex flex-col py-4 gap-4 bg-white shadow-md p-6 rounded-2xl">
                <div class="inline-flex items-center gap-2 mb-4">
                    <x-mary-icon name="o-information-circle" class="text-gray-500" />
                    <h3 class="text-xl font-bold">Data Selengkapnya</h3>
                </div>

                <div class="flex flex-col gap-2">
                    @if ($jumlahDuplikat > 0)
                    <a class="cursor-pointer" href="{{route('santri.index', ['search' => $santri->nama])}}" wire:navigate >
                        <div class="bg-red-100 p-4 rounded-lg flex justify-between ">
                            <x-mary-icon name="o-information-circle" class="text-red-500 font-bold" label="Terdapat {{ $jumlahDuplikat }} Data yang sama dengan Santri ini." />
                        </div>
                    </a>
                    @endif

                    @foreach ($santriMenus as $menu)
                        <a class="cursor-pointer" wire:click="openDrawer( '{{ $menu['drawerType'] }}' )">
                            <div class="bg-gray-100 p-4 rounded-lg flex justify-between hover:bg-gray-200">
                                <h3 class="text-lg font-semibold">{{ $menu['name'] }}</h3>
                                <x-mary-icon wire:loading.remove
                                    wire:target="openDrawer( '{{ $menu['drawerType'] }}' )" name="o-chevron-right"
                                    class="text-gray-500" />
                                <x-mary-loading wire:loading
                                    wire:target="openDrawer( '{{ $menu['drawerType'] }}' )" />
                            </div>
                        </a>
                    @endforeach

                    <x-mary-drawer wire:model="showDrawer" class="w-11/12 lg:w-1/3 !px-4 sm:!px-6 md:!px-8" right
                        :title="$edit_mode ? 'Edit Data Santri' : 'Detail Santri'">
                        <div>
                            @if ($drawerType === $dataDiriDrawer)
                                @if ($editDataDiri)
                                    <!-- Form Edit Data Diri -->
                                    <div class="transition-opacity duration-500 ease-in-out">
                                        <h3 class="text-lg font-semibold">Edit Data Diri Santri</h3>
                                        <div class="mt-4 space-y-4">
                                            <x-mary-input label="Nama" wire:model="nama" />
                                            <x-mary-input label="Tempat Lahir" wire:model="tempat_lahir" />
                                            <x-mary-datepicker label="Tanggal Lahir" wire:model="tanggal_lahir"
                                                icon="o-calendar" :config="['locale' => 'id', 'altFormat' => 'j F Y']" />
                                            <x-mary-select label="Jenis Kelamin" icon="o-user"
                                                placeholder="Jenis Kelamin" :options="$jenis_kelamin_options"
                                                wire:model="jenis_kelamin" />
                                            <x-mary-input label="HP" wire:model="hp" />
                                            <x-mary-input label="Email" wire:model="email" />
                                            <x-mary-textarea label="Alamat" wire:model="alamat" />

                                            <x-mary-select wire:model.live="provinsi" placeholder="Pilih Provinsi"
                                                label="Provinsi" :options="$provinces" />

                                            @if ($provinsi && $cities)
                                                <x-mary-select wire:model.live="kabupaten"
                                                    placeholder="Pilih Kota/Kabupaten" label="Kota/Kabupaten"
                                                    :options="$cities" />
                                            @endif

                                            @if ($kabupaten && $districts)
                                                <x-mary-select wire:model.live="kecamatan"
                                                    placeholder="Pilih Kecamatan/Distrik" label="Kecamatan/Distrik"
                                                    :options="$districts" />
                                            @endif

                                            <x-mary-input label="NIK" wire:model="nik" />
                                            <x-mary-input label="KK" wire:model="kk" />
                                        </div>
                                        <div
                                            class="flex justify-end mt-4 transition-transform duration-500 ease-in-out">
                                            <x-mary-button label="Simpan" wire:click="saveDataDiri"
                                                class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out"
                                                spinner="saveDataDiri" />
                                            <x-mary-button label="Batalkan" wire:click="closeEdit" spinner
                                                class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out ml-2" />
                                        </div>
                                    </div>
                                @else
                                    <!-- Tampilkan Data Diri -->
                                    <div
                                        class="p-2 md:p-4 bg-white rounded-lg shadow-lg transition-opacity duration-500 ease-in-out">
                                        <h4 class="text-xl font-semibold text-indigo-600">Data Diri</h4>
                                        <div class="mt-4">
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Nomor Induk</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->nomor_induk }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Nomor Induk Baru</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->nomor_induk_baru }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Tempat Lahir</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->tempat_lahir }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Tanggal Lahir</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ \Carbon\Carbon::parse($santri->tanggal_lahir)->format('d F Y') }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Jenis Kelamin</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ config()->get('constants.jenis_kelamin')[$santri->jenis_kelamin] }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Alamat</p>
                                                    <p class="text-lg font-medium text-gray-900 break-words">
                                                        {{ $santri->alamat }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Kabupaten</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->kabupaten !== null ? $santri->kabupaten->name : '' }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Provinsi</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->kabupaten !== null ? $santri->kabupaten->province->name : '' }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">NIK</p>
                                                    <p class="text-lg font-medium text-gray-900">{{ $santri->nik }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">KK</p>
                                                    <p class="text-lg font-medium text-gray-900">{{ $santri->kk }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">HP</p>
                                                    <p class="text-lg font-medium text-gray-900">{{ $santri->hp }}
                                                    </p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Email</p>
                                                    <p class="text-lg font-medium text-gray-900">{{ $santri->email }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                @endif
                            @elseif ($drawerType === $dataOrangTuaDrawer)
                                @if ($editDataOrangTua)
                                    <!-- Form Edit Data Orang Tua -->
                                    <h3 class="text-lg font-semibold">Edit Data Orang Tua</h3>
                                    <div class="mt-4 space-y-4">
                                        <x-mary-input label="Nama Ayah" wire:model="nama_ayah"
                                            class="transition-opacity duration-500 ease-in-out" />
                                        <x-mary-input label="Nama Ibu" wire:model="nama_ibu"
                                            class="transition-opacity duration-500 ease-in-out" />
                                        <x-mary-input label="Pekerjaan Ayah" wire:model="pekerjaan_ayah"
                                            class="transition-opacity duration-500 ease-in-out" />
                                        <x-mary-input label="Pekerjaan Ibu" wire:model="pekerjaan_ibu"
                                            class="transition-opacity duration-500 ease-in-out" />
                                        <x-mary-input label="HP Orang Tua" wire:model="hp_orang_tua"
                                            class="transition-opacity duration-500 ease-in-out" />
                                        <x-mary-textarea label="Alamat Orang Tua" wire:model="alamat_orang_tua"
                                            class="transition-opacity duration-500 ease-in-out" />
                                    </div>
                                    <div class="flex justify-end mt-4 transition-opacity duration-500 ease-in-out">
                                        <x-mary-button label="Simpan" wire:click="saveDataOrangTua"
                                            class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out"
                                            spinner="saveDataOrangTua" />
                                        <x-mary-button label="Batalkan" wire:click="closeEdit"
                                            class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out ml-2" />
                                    </div>
                                @else
                                    <!-- Tampilkan Data Orang Tua -->
                                    <div
                                        class="p-2 md:p-4 bg-white rounded-lg shadow-lg transition-opacity duration-500 ease-in-out">
                                        <h4 class="text-xl font-semibold text-indigo-600">Data Orang Tua</h4>
                                        <div class="mt-4">
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Nama Ayah</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->nama_ayah }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Nama Ibu</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->nama_ibu }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Pekerjaan Ayah</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->pekerjaan_ayah }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Pekerjaan Ibu</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->pekerjaan_ibu }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">HP Orang Tua</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->hp_orang_tua }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Alamat Orang Tua</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->alamat_orang_tua }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @elseif ($drawerType === $dataPendidikanDrawer)
                                @if ($editDataPendidikan)
                                    <!-- Form Edit Data Pendidikan -->
                                    <h3 class="text-lg font-semibold">Edit Data Pendidikan</h3>
                                    <div class="mt-4 space-y-4">
                                        {{--
                <x-mary-input label="Pendidikan Terakhir" wire:model="pendidikan_terakhir"
                    class="transition-opacity duration-500 ease-in-out" /> --}}
                                        <x-mary-select label="Pendidikan Terakhir" wire:model="pendidikan_terakhir"
                                            :options="$pendidikanOptions" placeholder="Pendidikan Terakhir" />
                                        <div>
                                            <h4 class="text-lg font-semibold">Pendidikan Formal</h4>
                                            @if (is_array($pendidikan_formal))
                                                @foreach ($pendidikan_formal as $index => $formal)
                                                    <div class="flex space-x-2">
                                                        <x-mary-select label="Pendidikan"
                                                            wire:model="pendidikan_formal.{{ $index }}.pendidikan"
                                                            :options="$pendidikanOptions" placeholder="Pendidikan" />
                                                        {{--
                        <x-mary-input label="Pendidikan" wire:model="pendidikan_formal.{{ $index }}.pendidikan"
                            class="transition-opacity duration-500 ease-in-out" /> --}}
                                                        <x-mary-input label="Instansi"
                                                            wire:model="pendidikan_formal.{{ $index }}.instansi"
                                                            class="transition-opacity duration-500 ease-in-out" />
                                                        <x-mary-input label="Tahun Lulus"
                                                            wire:model="pendidikan_formal.{{ $index }}.tahun_lulus"
                                                            class="transition-opacity duration-500 ease-in-out" />
                                                        <x-mary-button icon="o-trash"
                                                            wire:click="removePendidikanFormal({{ $index }})"
                                                            class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out self-end" />
                                                    </div>
                                                @endforeach
                                            @endif
                                            <div class="flex flex-col sm:flex-row mt-2 gap-2 ">

                                                <x-mary-select label="Pendidikan"
                                                    wire:model="tempPendidikanFormal.pendidikan" :options="$pendidikanOptions"
                                                    placeholder="Pendidikan" />
                                                <x-mary-input label="Instansi"
                                                    wire:model="tempPendidikanFormal.instansi"
                                                    class="transition-opacity duration-500 ease-in-out" />
                                                <x-mary-input label="Tahun Lulus"
                                                    wire:model="tempPendidikanFormal.tahun_lulus"
                                                    class="transition-opacity duration-500 ease-in-out" />
                                                <x-mary-button icon="o-plus" wire:click="addPendidikanFormal"
                                                    class="bg-green-500 hover:bg-green-700 transition-opacity duration-500 ease-in-out self-end" />
                                            </div>
                                        </div>
                                        <div class="mt-4">
                                            <h4 class="text-lg font-semibold">Pendidikan Non-Formal</h4>
                                            @if (is_array($pendidikan_non_formal))
                                                @foreach ($pendidikan_non_formal as $index => $nonFormal)
                                                    <div class="flex space-x-2">
                                                        <x-mary-input label="Instansi"
                                                            wire:model="pendidikan_non_formal.{{ $index }}.instansi"
                                                            class="transition-opacity duration-500 ease-in-out" />
                                                        <x-mary-input label="Tahun Lulus"
                                                            wire:model="pendidikan_non_formal.{{ $index }}.tahun_lulus"
                                                            class="transition-opacity duration-500 ease-in-out" />
                                                        <x-mary-button icon="o-trash"
                                                            wire:click="removePendidikanNonFormal({{ $index }})"
                                                            class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out self-end" />
                                                    </div>
                                                @endforeach
                                            @endif
                                            <div class="flex flex-col sm:flex-row mt-2 gap-2">
                                                <x-mary-input label="Instansi"
                                                    wire:model="tempPendidikanNonFormal.instansi"
                                                    class="transition-opacity duration-500 ease-in-out" />
                                                <x-mary-input label="Tahun Lulus"
                                                    wire:model="tempPendidikanNonFormal.tahun_lulus"
                                                    class="transition-opacity duration-500 ease-in-out" />
                                                <x-mary-button icon="o-plus" wire:click="addPendidikanNonFormal"
                                                    class="bg-green-500 hover:bg-green-700 transition-opacity duration-500 ease-in-out self-end" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex justify-start mt-8 transition-opacity duration-500 ease-in-out">
                                        <x-mary-button label="Batalkan" wire:click="closeEdit"
                                            class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out text-white"
                                            spinner />
                                        <x-mary-button label="Simpan" wire:click="saveDataPendidikan"
                                            class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out text-white"
                                            spinner="saveDataPendidikan" />
                                    </div>
                                @else
                                    <!-- Tampilkan Data Pendidikan -->
                                    <div
                                        class="p-6 bg-white rounded-lg shadow-lg transition-opacity duration-500 ease-in-out">
                                        <h4 class="text-xl font-semibold text-indigo-600">Data Pendidikan</h4>
                                        <div class="mt-4">
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Pendidikan Terakhir</p>
                                                    <p class="text-lg font-medium text-gray-900">
                                                        {{ $santri->pendidikan_terakhir }}</p>
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Pendidikan Formal</p>
                                                    @if (is_array($santri->pendidikan_formal) && count($santri->pendidikan_formal) > 0)
                                                        @foreach ((object) $santri->pendidikan_formal as $pendidikan)
                                                            <span
                                                                class="block text-lg font-medium text-gray-900">{{ $pendidikan['pendidikan'] }}
                                                                - {{ $pendidikan['instansi'] }}
                                                                ({{ $pendidikan['tahun_lulus'] }})</span>
                                                        @endforeach
                                                    @else
                                                        <span class="block text-lg font-medium text-gray-900">Tidak ada
                                                            data</span>
                                                    @endif
                                                </div>
                                                <div class="bg-indigo-50 p-4 rounded-lg shadow-sm">
                                                    <p class="text-sm text-gray-500">Pendidikan Non-Formal</p>
                                                    @if (is_array($santri->pendidikan_non_formal) && count($santri->pendidikan_non_formal) > 0)
                                                        @foreach ((object) $santri->pendidikan_non_formal as $pendidikan)
                                                            <span
                                                                class="block text-lg font-medium text-gray-900">{{ $pendidikan['instansi'] }}
                                                                ({{ $pendidikan['tahun_lulus'] }})</span>
                                                        @endforeach
                                                    @else
                                                        <span class="block text-lg font-medium text-gray-900">Tidak ada
                                                            data</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @elseif ($drawerType === $dataLainnyaDrawer)
                                @if ($editDataLainnya)
                                    <!-- Form Edit Data Lainnya -->
                                    <h3 class="text-lg font-semibold">Edit Data Lainnya</h3>
                                    <div class="mt-4 space-y-4">
                                        <x-mary-input label="Tahun Masuk" wire:model="tahun_masuk"
                                            class="transition-opacity duration-500 ease-in-out" />
                                        <x-mary-select label="Komplek" wire:model="komplek"
                                            class="transition-opacity duration-500 ease-in-out" :options="$komplekOptions" />
                                    </div>
                                    <div class="flex justify-end mt-4 transition-opacity duration-500 ease-in-out">
                                        <x-mary-button label="Simpan" wire:click="saveDataLainnya"
                                            class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out"
                                            spinner="saveDataLainnya" />
                                        <x-mary-button label="Batalkan" wire:click="closeEdit" spinner
                                            class="bg-red-500 hover:bg-red-700 transition-opacity duration-500 ease-in-out ml-2" />
                                    </div>
                                @else
                                    <!-- Tampilkan Data Lainnya -->
                                    <div
                                        class="p-4 bg-gray-50 rounded-lg shadow transition-opacity duration-500 ease-in-out">
                                        <h4 class="text-lg font-semibold text-gray-700">Data Lainnya</h4>
                                        <p class="mt-2 text-gray-600"><strong>Tahun Masuk:</strong>
                                            {{ $santri->tahun_masuk }}</p>
                                        <p class="mt-2 text-gray-600"><strong>Komplek:</strong>
                                            {{ $santri->komplek->nama_komplek }}</p>

                                    </div>
                                @endif
                            @endif
                        </div>
                        <div class="flex flex-row gap-2 mt-4">
                            @if ($drawerType === $dataDiriDrawer && !$editDataDiri)
                                <x-mary-button label="Edit" wire:click="openEdit('{{ $dataDiriDrawer }}')" spinner
                                    class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out" />
                            @elseif($drawerType === $dataOrangTuaDrawer && !$editDataOrangTua)
                                <x-mary-button label="Edit" wire:click="openEdit('{{ $dataOrangTuaDrawer }}')"
                                    spinner
                                    class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out" />
                            @elseif($drawerType === $dataPendidikanDrawer && !$editDataPendidikan)
                                <x-mary-button label="Edit" wire:click="openEdit('{{ $dataPendidikanDrawer }}')"
                                    spinner
                                    class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out" />
                            @elseif($drawerType === $dataLainnyaDrawer && !$editDataLainnya)
                                <x-mary-button label="Edit" wire:click="openEdit('{{ $dataLainnyaDrawer }}')"
                                    spinner
                                    class="bg-blue-500 hover:bg-blue-700 transition-opacity duration-500 ease-in-out" />
                            @endif


                            @if ($edit_mode === false)
                                <x-mary-button label="Tutup" @click="$wire.closeDrawer()"
                                    class="transition-opacity duration-500 ease-in-out" />
                            @endif
                        </div>
                    </x-mary-drawer>
                </div>

                <div class="flex items-center my-4 px-2">
                    <span class="mr-4 text-gray-500">Lainnya</span>
                    <div class="flex-grow border-t border-gray-300"></div>
                </div>


                <div class="flex flex-col gap-2">
                    <a class="cursor-pointer">
                        <div wire:click="modalStatusSantri = true"
                            class="bg-gray-100 p-4 rounded-lg flex justify-between hover:bg-gray-200">
                            <h3 class="text-lg font-semibold">Status Santri</h3>
                            <x-mary-icon name="o-chevron-right" class="text-gray-500" />
                        </div>
                    </a>

                    @if (auth()-> user() -> role -> first() -> name === 'superadmin')
                    <a class="cursor-pointer">
                        <div wire:click="modalDeleteSantri = true"
                            class="bg-gray-100 p-4 rounded-lg flex justify-between hover:bg-gray-200">
                            <h3 class="text-lg font-semibold"><x-mary-icon name="o-trash" label="Hapus Santri" class="text-red-500"/> </h3>
                        </div>
                    </a>

                    <x-mary-modal class="bakcdrop-blur" wire:model="modalDeleteSantri" title="Hapus Santri"
                        subtitle="Hapus Santri Secara Permanen?" separator persistent
                    >
                        <div class="mb-4">
                            <p>Apakah anda yakin ingin menghapus santri secara permanen?</p>
                            <p class="text-red-700 font-bold">Data yang telah dihapus tidak dapat dikembalikan.</p>
                        </div>

                        <x-slot:actions>
                            <x-mary-button label="Batalkan" wire:click="modalDeleteSantri = false" />
                            <x-mary-button label="Hapus" class="btn-error" spinner
                                wire:click="deleteSantri" />
                        </x-slot:actions>
                    </x-mary-modal>
                    @endif

                    <x-mary-modal class="backdrop-blur" wire:model="modalStatusSantri" title="Status Santri"
                        subtitle="Ubah Status Santri" separator persistent>
                        <div class="flex flex-col gap-2">
                            <x-mary-select label="Status Santri" wire:model.live.debounce.500ms="status_santri"
                                :options="$statusSantriOptions" placeholder="Status Santri" />


                            @if ((int) $status_santri === 0)
                                <x-mary-datepicker label="Tgl. Alumni" wire:model="non_active_at" icon="o-calendar"
                                    :config="['locale' => 'id', 'altFormat' => 'j F Y']" class="transition-opacity duration-500 ease-in-out" />
                            @elseif((int) $status_santri === 2)
                                <x-mary-datepicker label="Tgl. Alumni" wire:model="alumni_at" icon="o-calendar"
                                    :config="['locale' => 'id', 'altFormat' => 'j F Y']" class="transition-opacity duration-500 ease-in-out" />
                            @endif

                            <div class="flex items-center justify-center w-full mt-4">
                                <x-mary-loading wire:loading="$status_santri"
                                    class="text-teal-700 loading-md loading-bars" />
                            </div>

                        </div>

                        <x-slot:actions>
                            <x-mary-button label="Cancel" wire:click="modalStatusSantri = false" />
                            <x-mary-button label="Simpan" class="btn-primary" spinner
                                wire:click="saveStatusSantri" />
                        </x-slot:actions>
                    </x-mary-modal>
                </div>

            </div>

        </div>

    </div>



</div>
