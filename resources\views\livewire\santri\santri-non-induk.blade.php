<div>
    {{-- A good traveler has no fixed plans and is not intent upon arriving. --}}

    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Data Santri yang Belum Memiliki Nomor Induk') }}
        </h2>
    </x-slot>
    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-6">
            <div class="flex sm:flex-row flex-col gap-4">
                <div class="bg-white p-4 shadow-lg rounded-lg sm:h-[350px]">
                    <h2 class="font-bold text-center">Data Santri Tanpa Nomor Induk</h2>
                    <div class="text-center">
                        <p class="text-3xl font-bold">{{ $noninduk }}</p>

                        <x-mary-button spinner wire:click="generateAll" class="mt-4">Generate Nomor Induk Massal
                        </x-mary-button>

                        {{-- <div class="mt-2" wire:loading wire:target="generateAll">
                            <x-mary-progress-radial value="{{ $progress }}" class="text-success"
                                style="--size:4rem; --thickness: 2px" />
                        </div> --}}

                        <!-- Progres radial ditampilkan hanya saat proses berjalan -->
                        {{-- <div wire:stream="generate-progress">
                            <div class="radial-progress bg-primary text-primary-content border-primary border-4"
                                style="--value:{{ $progress }};" role="progressbar">
                                {{ $progress }}%
                            </div>
                        </div> --}}

                        <div class="mt-2" wire:loading wire:target="generateAll">
                            {!! $this->progressContent() !!}
                        </div>




                    </div>
                </div>

                <div class="w-full">
                    <div class="rounded-lg shadow-lg bg-white p-4">
                        @foreach ($dataSantri as $item)
                        <x-mary-list-item :item="$item"  link="{{ route('santri.detail', ['id' => $item->id]) }}">

                            <x-slot:value>
                                {{ $item->nama }}
                            </x-slot:value>
                            <x-slot:sub-value>
                                Angkatan {{ $item->tahun_masuk }}
                            </x-slot:sub-value>
                            <x-slot:actions>
                                <button wire:click="generate({{ $item }})"
                                    class="px-4 py-2 rounded-xl w-auto bg-gray-200 hover:bg-gray-300 flex flex-row gap-2 justify-start items-center">
                                    <div wire:loading.remove wire:target="generate({{ $item }})">
                                        @svg('iconpark-reload', 'w-6 h-6')</div>

                                    <x-mary-loading wire:loading wire:target="generate({{ $item }})" />
                                    <span class="font-semibold text-sm">
                                        Buat
                                    </span>
                                </button>
                            </x-slot:actions>
                        </x-mary-list-item>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
