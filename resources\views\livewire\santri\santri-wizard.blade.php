<div>
    {{-- Be like water. --}}

    <div x-data="{ step: @entangle('currentStep') }"
        x-init="$watch('step', () => window.scrollTo({ top: 0, behavior: 'smooth' }))">
        <x-slot name="header">
            <x-mary-header title="Input Data Santri" subtitle="Check this on mobile"
                class="bg-white overflow-hidden sm:rounded-lg p-2">
            </x-mary-header>
        </x-slot>

        <div class="sm:py-6">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="flex flex-col sm:flex-row bg-white overflow-hidden shadow-xl sm:rounded-lg sm:bg-gray-100">
                    <!-- Langkah-langkah form wizard -->
                    <div
                        class="flex flex-row sm:flex-col justify-between sm:justify-start sm:self-start sm:gap-8 mb-4 sm:w-2/5 px-6 pb-4 sm:py-10 border-b-2 sm:border-none ">
                        <template x-for="(stepTitle, index) in ['Data Diri Santri', 'Data Orang Tua', 'Data Lainnya']"
                            :key="index">
                            <div class="flex-1 text-center relative">
                                <div class="w-10 h-10 mx-auto rounded-full flex items-center justify-center duration-1000 transition-colors "
                                    :class="{
                                        'bg-blue-500 text-white animate-flip': step === index +
                                            1,
                                        'bg-gray-200 text-gray-500 animate-flip': step !== index + 1
                                    }">
                                    <template x-if="index + 1 === 1">
                                        <img @click.prevent="step = 1" src="{{ asset('images/datadiri.svg') }}"
                                            alt="Data Diri Icon"
                                            class="w-6 h-6 cursor-pointer transition-transform duration-500"
                                            :class="{ 'animate-flip': step === 1 }">
                                    </template>
                                    <template x-if="index + 1 === 2">
                                        <img @click.prevent="step = 2" src="{{ asset('images/orangtua.svg') }}"
                                            alt="Data Orang Tua Icon"
                                            class="w-6 h-6 cursor-pointer transition-transform duration-500"
                                            :class="{ 'animate-flip': step === 2 }">
                                    </template>
                                    <template x-if="index + 1 === 3">
                                        <svg @click.prevent="step = 3"
                                            class="w-6 h-6 cursor-pointer transition-transform duration-500" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                                            :class="{ 'animate-flip': step === 3 }">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 10V3a1 1 0 011-1h6a1 1 0 011 1v7M3 21V8a1 1 0 011-1h7m10 0h1a1 1 0 011 1v13M3 21h18">
                                            </path>
                                        </svg>
                                    </template>
                                </div>
                                <div class="mt-2 text-sm font-medium"
                                    :class="{ 'text-blue-500': step === index + 1, 'text-gray-400': step !== index + 1 }">
                                    <span x-text="stepTitle"></span>
                                </div>
                            </div>
                        </template>
                    </div>

                    <form wire:submit.prevent="submit" class="w-full bg-white">
                        <div class="p-6 sm:p-10">
                            <!-- Step 1 -->
                            <div x-show="step === 1" x-transition:enter="transition ease-out duration-500"
                                x-transition:enter-start="opacity-0 transform scale-90"
                                x-transition:enter-end="opacity-100 transform scale-100">
                                <h3 class="text-lg font-semibold">Data Diri Santri</h3>
                                <div class="mt-4 space-y-4">
                                    <x-mary-input label="Nama" wire:model="nama" />
                                    <x-mary-input label="Tempat Lahir" wire:model="tempat_lahir" />

                                    @php
                                    $tanggalConfig = ['locale' => 'id', 'altFormat' => 'j F Y'];
                                    @endphp

                                    <x-mary-datepicker placeholder="Pilih Tanggal Lahir" label="Tanggal Lahir"
                                        wire:model="tanggal_lahir" icon="o-calendar" :config="$tanggalConfig" />
                                    <x-mary-select placeholder="Pilih Jenis Kelamin" label="Jenis Kelamin" icon="o-user"
                                        :options="$jenisKelaminOpts" wire:model="jenis_kelamin" />

                                    <x-mary-input label="HP" wire:model="hp" />
                                    <x-mary-input label="Email" wire:model="email" />
                                    <x-mary-select wire:model.live="selectedProvince" placeholder="Pilih Provinsi"
                                        label="Provinsi" :options="$provinces" />

                                    @if ($selectedProvince)
                                    <x-mary-select wire:model.live="selectedCity" placeholder="Pilih Kota/Kabupaten"
                                        label="Kota/Kabupaten" :options="$cities" />
                                    @endif

                                    @if ($selectedCity)
                                    <x-mary-select wire:model.live="selectedDistrict"
                                        placeholder="Pilih Kecamatan/Distrik" label="Kecamatan/Distrik"
                                        :options="$districts" />
                                    @endif

                                    <x-mary-textarea label="Alamat Lengkap" wire:model="alamat" />
                                    <x-mary-input label="NIK" wire:model="nik" />
                                    <x-mary-input label="KK" wire:model="kk" />
                                </div>
                            </div>

                            <!-- Step 2 -->
                            <div x-show="step === 2" x-transition:enter="transition ease-out duration-500"
                                x-transition:enter-start="opacity-0 transform scale-90"
                                x-transition:enter-end="opacity-100 transform scale-100">
                                <h3 class="text-lg font-semibold">Data Orang Tua</h3>
                                <div class="mt-4 space-y-4">
                                    <x-mary-input label="Nama Ayah" wire:model="nama_ayah" />
                                    <x-mary-input label="Nama Ibu" wire:model="nama_ibu" />
                                    <x-mary-input label="Pekerjaan Ayah" wire:model="pekerjaan_ayah" />
                                    <x-mary-input label="Pekerjaan Ibu" wire:model="pekerjaan_ibu" />
                                    <x-mary-input label="HP Orang Tua" wire:model="hp_orang_tua" />
                                    <x-mary-textarea label="Alamat Orang Tua" wire:model="alamat_orang_tua" />
                                    <x-mary-input label="Anak Ke" wire:model="anak_ke" />
                                    <x-mary-input label="Jumlah Saudara" wire:model="jumlah_saudara" />
                                </div>
                            </div>

                            <!-- Step 3 -->
                            <div x-show="step === 3" x-transition:enter="transition ease-out duration-500"
                                x-transition:enter-start="opacity-0 transform scale-90"
                                x-transition:enter-end="opacity-100 transform scale-100">
                                <h3 class="text-lg font-semibold">Data Lainnya</h3>
                                <div class="mt-4 space-y-4">
                                    <x-mary-select label="Pendidikan Terakhir" wire:model="pendidikan_terakhir"
                                        :options="$pendidikanOptions" placeholder="Pilih Pendidikan Terakhir" />

                                    <!-- Pendidikan Formal -->
                                    <h4 class="text-md font-semibold">Pendidikan Formal</h4>
                                    <div class="space-y-4">
                                        @foreach ($pendidikan_formal as $index => $pendidikan)
                                        <div class="flex items-center justify-between space-x-4 p-2 bg-gray-100">
                                            <div class="flex justify-start gap-2 w-full">
                                                <div class="font-semibold">{{ $pendidikan['pendidikan'] }}</div>
                                                <div class="text-gray-600">{{ $pendidikan['instansi'] }}</div>
                                                <div class="text-gray-600">{{ $pendidikan['tahun_lulus'] }}</div>
                                            </div>
                                            <x-mary-button type="button" icon="o-trash" class="btn-error btn-sm"
                                                wire:click="removePendidikanFormal({{ $index }})" spinner />
                                        </div>
                                        @endforeach
                                        <x-mary-button type="button" icon="o-plus"
                                            @click="$wire.pendidikanFormalModal = true" spinner>Tambah Pendidikan
                                            Formal
                                        </x-mary-button>
                                    </div>

                                    <!-- Pendidikan Non-Formal -->
                                    <h4 class="text-md font-semibold">Pendidikan Non-Formal</h4>
                                    <div class="space-y-4">
                                        @foreach ($pendidikan_non_formal as $index => $pendidikan)
                                        <div class="flex items-center justify-between space-x-4 p-2 bg-gray-100">
                                            <div class="flex justify-start gap-2 w-full">
                                                <div class="font-semibold">{{ $pendidikan['instansi'] }}</div>
                                                <div class="text-gray-600">{{ $pendidikan['tahun_lulus'] }}</div>
                                            </div>
                                            <x-mary-button type="button" icon="o-trash" class="btn-error btn-sm"
                                                wire:click="removePendidikanNonFormal({{ $index }})" spinner />
                                        </div>
                                        @endforeach
                                        <x-mary-button type="button" icon="o-plus"
                                            @click="$wire.pendidikanNonFormalModal = true" spinner>Tambah Pendidikan
                                            Non-Formal</x-mary-button>
                                    </div>

                                    <x-mary-choices label="Komplek" wire:model="id_komplek"
                                        no-result-text="Komplek tidak ditemukan! ..." search-function="searchKompleks"
                                        :options="$kompleks" single searchable />

                                    <x-mary-datepicker label="Tanggal Diterima" wire:model="diterima_at"
                                        :config="$tanggalConfig" icon="o-calendar" />
                                    <x-mary-input label="Tahun Masuk" wire:model="tahun_masuk" />
                                    <x-mary-textarea label="Alasan" wire:model="alasan" />
                                </div>
                            </div>

                            <!-- Menampilkan error -->
                            @if ($errors->any())

                            @foreach ($errors->all() as $error)
                            <x-mary-alert title="{{ $error }}" description="Validasi gagal!" class="alert-error mt-2"
                                icon="o-exclamation-triangle" dismissible>
                            </x-mary-alert>
                            @endforeach

                            @endif

                            <!-- Navigation Buttons -->
                            <div class="mt-4 flex justify-between">
                                <x-mary-button x-show="step > 1" @click.prevent="step--" spinner>Sebelumnya
                                </x-mary-button>
                                <x-mary-button x-show="step < 3" @click.prevent="step++" spinner>Selanjutnya
                                </x-mary-button>
                                <x-mary-button class="btn btn-success" x-show="step === 3" type="submit" spinner>Simpan
                                </x-mary-button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Pendidikan Formal -->
    <x-mary-modal wire:model="pendidikanFormalModal" title="Tambah Pendidikan Formal">
        <div class="space-y-4">
            <x-mary-select wire:model="tempPendidikanFormal.pendidikan" :options="$pendidikanOptions"
                placeholder="Pendidikan" />
            <x-mary-input wire:model="tempPendidikanFormal.instansi" placeholder="Instansi" />
            <x-mary-input type="number" wire:model="tempPendidikanFormal.tahun_lulus" placeholder="Tahun Lulus" />
        </div>
        <x-slot:actions>
            <x-mary-button wire:click="addPendidikanFormal">Tambah</x-mary-button>
            <x-mary-button @click="$wire.pendidikanFormalModal = false">Batal</x-mary-button>
        </x-slot:actions>
    </x-mary-modal>

    <!-- Modal Pendidikan Non-Formal -->
    <x-mary-modal wire:model="pendidikanNonFormalModal" title="Tambah Pendidikan Non-Formal">
        <div class="space-y-4">
            <x-mary-input wire:model="tempPendidikanNonFormal.instansi" placeholder="Instansi" />
            <x-mary-input type="number" wire:model="tempPendidikanNonFormal.tahun_lulus" placeholder="Tahun Lulus" />
        </div>
        <x-slot:actions>
            <x-mary-button wire:click="addPendidikanNonFormal">Tambah</x-mary-button>
            <x-mary-button @click="$wire.pendidikanNonFormalModal = false">Batal</x-mary-button>
        </x-slot:actions>
    </x-mary-modal>

</div>
{{--
@assets
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://npmcdn.com/flatpickr/dist/l10n/id.js"></script>
@endassets --}}
