<div>
    {{-- Header --}}
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Pengaturan KTS') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-6">

            <div class="bg-white shadow-lg rounded-lg p-4">
                {{-- Pesan sukses --}}
                @if (session()->has('message'))
                <div class="bg-green-100 text-green-800 p-4 mb-4 rounded-md">
                    {{ session('message') }}
                </div>
                @endif

                {{-- Form --}}
                <x-mary-form wire:submit="save">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        {{-- Background Front --}}
                        <div>
                            <x-mary-file wire:model="background_front" label="Background Front" />
                            @if ($background_front)
                            <div class="mt-4">
                                <h3 class="font-semibold text-lg">Preview Background Front:</h3>
                                <img src="{{ $background_front->temporaryUrl() }}" alt="Background Front"
                                    class="w-full max-w-sm mx-auto mt-2 rounded-md shadow-md">
                            </div>
                            @elseif($savedBackgroundFront)
                            <div class="mt-4">
                                <h3 class="font-semibold text-lg">Current Background Front:</h3>
                                <img src="{{ asset('storage/' . $savedBackgroundFront) }}" alt="Background Front"
                                    class="w-full max-w-sm mx-auto mt-2 rounded-md shadow-md">
                            </div>
                            @endif
                        </div>

                        {{-- Background Back --}}
                        <div>
                            <x-mary-file wire:model="background_back" label="Background Back" />
                            @if ($background_back)
                            <div class="mt-4">
                                <h3 class="font-semibold text-lg">Preview Background Back:</h3>
                                <img src="{{ $background_back->temporaryUrl() }}" alt="Background Back"
                                    class="w-full max-w-sm mx-auto mt-2 rounded-md shadow-md">
                            </div>
                            @elseif($savedBackgroundBack)
                            <div class="mt-4">
                                <h3 class="font-semibold text-lg">Current Background Back:</h3>
                                <img src="{{ asset('storage/' . $savedBackgroundBack) }}" alt="Background Back"
                                    class="w-full max-w-sm mx-auto mt-2 rounded-md shadow-md">
                            </div>
                            @endif
                        </div>

                        {{-- Default Photo --}}
                        <div>
                            <x-mary-file wire:model="default_photo" label="Default Photo" />
                            @if ($default_photo)
                            <div class="mt-4">
                                <h3 class="font-semibold text-lg">Preview Default Photo:</h3>
                                <img src="{{ $default_photo->temporaryUrl() }}" alt="Default Photo"
                                    class="w-full max-w-sm mx-auto mt-2 rounded-md shadow-md">
                            </div>
                            @elseif($savedDefaultPhoto)
                            <div class="mt-4">
                                <h3 class="font-semibold text-lg">Current Default Photo:</h3>
                                <img src="{{ asset('storage/' . $savedDefaultPhoto) }}" alt="Default Photo"
                                    class="w-full max-w-sm mx-auto mt-2 rounded-md shadow-md">
                            </div>
                            @endif
                        </div>

                    </div>

                    <x-slot:actions>
                        <x-mary-button label="Cancel" class="bg-gray-300 text-gray-800 hover:bg-gray-400" />
                        <x-mary-button label="Save KTS Settings" class="btn-primary" type="submit" spinner="save" />
                    </x-slot:actions>
                </x-mary-form>
            </div>
        </div>
    </div>
</div>