<div>
    {{-- The best athlete wants his opponent at his best. --}}
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Restore Data') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto ">
            <div class="bg-white shadow-xl sm:rounded-lg sm:px-6 lg:px-8 mb-6 py-4">
                <x-mary-form wire:submit="process">

                    <x-mary-file wire:model="file" label="Json Data" accept="application/json" />
                    <x-mary-select label="Pilih <PERSON>mplek" icon="o-user" :options="$kompleks" wire:model="selectedKomplek" />
                    <x-mary-input label="Tahun Angkatan" wire:model="angkatan" />
                    <x-slot:actions>
                        <x-mary-button label="Cancel" />
                        <x-mary-button label="Process" class="btn-primary" type="submit" spinner="process" />
                    </x-slot:actions>
                </x-mary-form>

                @if (session()->has('error'))
                    <div class="text-red-600 mt-4">{{ session('error') }}</div>
                @endif

                @if (session()->has('success'))
                    <div class="text-green-600 mt-4">{{ session('success') }}</div>
                @endif

                @if (!empty($errorsList))
                    <div class="mt-4">
                        <h3 class="text-red-600">Errors:</h3>
                        <ul class="list-disc pl-6">
                            @foreach ($errorsList as $error)
                                <li>
                                    Data: {{ json_encode($error['data']) }}<br>
                                    Issues: {{ json_encode($error['errors']) }}
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif


            </div>
        </div>
    </div>
</div>
