<div>
    {{-- The best athlete wants his opponent at his best. --}}

    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('<PERSON><PERSON> Per Kabupaten di Provinsi ' . $province->name . '') }}
        </h2>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-6 bg-white">
            <div class="overflow-hidden ">
                <div class="w-full p-4 ">


                    @php
                        $maxSantri = $data->max('total_santri'); // Ambil jumlah santri terbanyak sebagai patokan maksimum
                    @endphp

                    @if ($maxSantri > 0)
                        <div class="flex flex-col gap-2">
                            @foreach ($data as $item)
                                @php
                                    $width = ($item->total_santri / $maxSantri) * 100; // Lebar bar sesuai proporsi terhadap maxSantri
                                    $color = 'bg-teal-500';
                                @endphp
                                <a href="{{ url()->route('santri.index') . '?' . http_build_query(['kabupaten' => $item->city_id]) }}" wire:navigate class="flex items-center gap-2 hover:bg-teal-50 py-4 px-2">
                                    <span class="w-1/3 text-gray-700 font-medium">{{ $item->city_name }}</span>
                                    <div class="w-2/3 h-6 relative rounded-lg overflow-hidden">
                                        <div class="{{ $color }} h-full" style="width: {{ $width }}%;">
                                        </div>
                                        <div
                                            class="absolute inset-0 flex items-center justify-end pr-2 text-sm font-semibold text-black">
                                            {{ $item->total_santri }} Santri
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 text-center font-medium">Tidak ada data santri untuk ditampilkan.</p>
                    @endif

                </div>
            </div>
        </div>
    </div>

</div>
