<div>
    {{-- If you look to others for fulfillment, you will never truly be fulfilled. --}}

    <x-slot name="header">
        <x-mary-header title="Tambah Akun" subtitle="Tambahkan akun untuk melakukan login"
            class="bg-white overflow-hidden sm:rounded-lg p-2">
        </x-mary-header>
    </x-slot>

    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 bg-white mt-6 rounded-tl-badge shadow-lg">

        @if ($isEditMode)
        @if (!$user -> is_confirmed && $user -> role -> first() -> name !== 'superadmin')
        <div class="my-4">
            <x-mary-alert title="Email belum dikonfirmasi" description="Pengguna ini belum melakukan konfirmasi email" icon="o-envelope" class="alert-warning">
                <x-slot:actions>
                    <x-mary-button label="Kirim Ulang" spinner="sendConfirmationEmail" wire:click="sendConfirmationEmail"/>
                </x-slot:actions>
            </x-mary-alert>
        </div>

        @endif
        @endif
        <x-mary-form wire:submit="save">
            <x-mary-input label="Nama Pengguna" wire:model.defer="name" />
            <x-mary-input label="Email" type="email" wire:model.defer="email" />
            <x-mary-input label="Nomor Telepon" wire:model.defer="phone" />
            {{-- <x-mary-input label="Password" type="password" wire:model.defer="password" /> --}}

            <x-mary-select label="Jabatan" wire:model.live="role" :options="$roles" class="uppercase"
                placeholder="Pilih Jabatan" />

            @if ($role !== 'superadmin' && $role !== null)
                <x-mary-select label="Komplek" wire:model="selectedKomplek" :options="$kompleks" class="uppercase"
                    placeholder="Pilih Komplek" />
            @endif



            <!-- Submit button -->
            <x-slot:actions>
                <x-mary-button label="Batalkan" />
                <x-mary-button :label="$isEditMode ? 'Simpan Perubahan' : 'Simpan'" class="btn-primary" type="submit" spinner="save" />
            </x-slot:actions>
        </x-mary-form>

        @if ($isEditMode)
        <x-mary-button label="Ganti Password" class="btn-secondary" icon="o-key"
        @click="$wire.modalPassword = true" />
        @endif

        <x-mary-modal wire:model="modalPassword" persistent class="backdrop-blur">
            <div>
                <x-mary-form wire:submit="savePassword">
                    <x-mary-input label="Password" type="password" wire:model.defer="password" />
                    <x-mary-input label="Confirm Password" type="password" wire:model.defer="password_confirmation" />

                    <x-slot:actions>
                        <x-mary-button label="Save Password" class="btn-primary" type="submit" spinner="savePassword" />
                        <x-mary-button label="Cancel" @click="$wire.modalPassword = false" />
                    </x-slot:actions>
                </x-mary-form>
            </div>

        </x-mary-modal>

    </div>


</div>
