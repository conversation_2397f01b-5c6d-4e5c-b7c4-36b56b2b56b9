<div>
    {{-- The whole world belongs to you. --}}

    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-4 mt-6">
        <x-mary-header title="Daftar Akun Pengguna" subtitle="Kelola akun pengguna untuk melakukan login"
            class="bg-white overflow-hidden  sm:rounded-xl p-6">
            <x-slot:middle class="!justify-end">
                <x-mary-input icon="o-magnifying-glass" placeholder="Search..." wire:model.live="search" />
            </x-slot:middle>
            <x-slot:actions>
                <x-mary-button icon="o-plus" class="btn-primary" link="{{ route('users.create') }}" />
            </x-slot:actions>
        </x-mary-header>
    </div>

    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 ">
        <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-4">
            <div class="p-8  text-center w-full" wire:loading wire:target="search">
                <x-mary-loading class="text-primary loading-lg" target="search" />
            </div>
            <div wire:loading.remove wire:target="search">
                @foreach ($users as $user)
                <x-mary-list-item :item="$user" no-separator :link="route('users.edit', ['userId' => $user->id])">
                    <x-slot:avatar>
                        <x-mary-badge value="{{ $user->role->first()->name }}" class="badge-primary" />
                    </x-slot:avatar>
                    <x-slot:value>
                        {{ $user->name }} @if (!$user -> is_confirmed && $user -> role -> first() -> name !== 'superadmin')
                        <x-mary-badge value="Belum Diverifikasi" class="badge-warning" />
                        @endif
                    </x-slot:value>
                    <x-slot:sub-value>
                        {{ $user->assignedKomplek() ? $user -> assignedKomplek() -> nama_komplek : 'Akun Pusat' }}
                    </x-slot:sub-value>
                    @if (auth()->user()->id !== $user->id)
                    <x-slot:actions>
                        <x-mary-button icon="o-trash" class="text-red-500" wire:click="" spinner />
                    </x-slot:actions>
                    @endif
                    </x-list-item>
                    @endforeach
            </div>
        </div>
    </div>

</div>
