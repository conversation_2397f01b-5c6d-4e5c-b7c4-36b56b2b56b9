<div>
    @if ($warnings !== null)

    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 mb-5">


        <x-mary-collapse wire:model="show" separator class="bg-base-200">
            <x-slot:heading class="bg-white">
                @if (count($warnings) > 0)
                <span>Pemberitahuan
                    <x-mary-badge value="{{ count($warnings) }}" class="badge-secondary indicator-item" />
                </span>
                @else
                <span>
                    Pemberitahuan
                </span>
                @endif
            </x-slot:heading>
            <x-slot:content>

                <div class="flex flex-col gap-2 overflow-hidden shadow-xl">

                    @foreach ($warnings as $warning)
                    <x-mary-alert title="{{ $warning->title }}" description="{{ $warning->message }}"
                        icon="o-exclamation-triangle" class="alert-{{ $warning->type }}">
                        <x-slot:actions>
                            @if (isset($warning->link))
                            <x-mary-button label="Lihat" link="{{ $warning->link }}" />
                            @endif
                        </x-slot:actions>
                    </x-mary-alert>
                    @endforeach

                </div>
            </x-slot:content>
        </x-mary-collapse>
    </div>

    @endif
</div>