<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.4.2/cdn.min.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1', // Replace with your primary color
                        secondary: '#22D3EE', // Replace with your secondary color
                        accent: '#F87171', // Replace with your accent color
                    }
                }
            }
        }
    </script>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('uiHandler', () => ({
                activeDropdown: null,
                drawerOpen: false,
                toggleDropdown(target) {
                    this.activeDropdown = this.activeDropdown === target ? null : target;
                },
                closeDropdown() {
                    this.activeDropdown = null;
                },
                toggleDrawer() {
                    this.drawerOpen = !this.drawerOpen;
                },
                closeDrawer() {
                    this.drawerOpen = false;
                }
            }));

            Alpine.data('dropdown', () => ({
                open: false,
                triggerElement: null,
                trigger: {
                    ['x-ref']: 'trigger',
                    ['@click']() {
                        this.open = !this.open;
                        this.triggerElement = this.$el;

                        this.$nextTick(() => {
                            const rect = this.triggerElement.getBoundingClientRect();
                            const dropdown = this.$refs.dialogue;
                            const dropdownRect = dropdown.getBoundingClientRect();

                            // Position vertically
                            if (window.innerHeight - rect.bottom < dropdownRect.height) {
                                dropdown.style.top = `${rect.top + window.scrollY - dropdownRect.height}px`;
                            } else {
                                dropdown.style.top = `${rect.bottom + window.scrollY}px`;
                            }

                            // Position horizontally
                            if (window.innerWidth - rect.left <= dropdownRect.width) {
                                dropdown.style.left = `${rect.right + window.scrollX - dropdownRect.width}px`;
                            } else {
                                dropdown.style.left = `${rect.left + window.scrollX}px`;
                            }
                        });
                    }
                },
                dialogue: {
                    ['x-ref']: 'dialogue',
                    ['x-show']() {
                        return this.open;
                    },
                    ['@click.away']() {
                        this.open = false;
                    },
                    // ['x-transition:enter']() {
                    //     return 'transition ease-out duration-200';
                    // },
                    ['x-transition:enter-start']() {
                        return 'opacity-0 transform scale-95';
                    },
                    ['x-transition:enter-end']() {
                        return 'opacity-100 transform scale-100';
                    },
                    ['x-transition:leave']() {
                        return 'transition ease-in duration-75';
                    },
                    ['x-transition:leave-start']() {
                        return 'opacity-100 transform scale-100';
                    },
                    ['x-transition:leave-end']() {
                        return 'opacity-0 transform scale-95';
                    },
                    ['@click.outside']() {
                        this.open = false;
                    }
                }
            }));

        });
    </script>

    <style>
        .drawer-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 20;
        }

        .drawer-overlay-open {
            display: block;
        }
    </style>
</head>

<body class="bg-gray-200" x-data="uiHandler">
<div class="flex h-screen overflow-hidden">
    <!-- Sidebar -->
    <div :class="drawerOpen ? 'translate-x-0' : '-translate-x-full'"
         class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg p-4 space-y-4 transform transition-transform duration-300 lg:static lg:translate-x-0 z-30">
        <div class="flex items-center space-x-2">
            <img src="https://i.pravatar.cc/150?img=3" class="w-12 h-12 rounded-full" />
            <div>
                <h2 class="text-xl font-bold">Bessie Cooper</h2>
                <p>UI/UX Design</p>
            </div>
        </div>
        <nav class="space-y-2">
            <a href="#" class="block py-2 px-3 bg-primary text-white rounded-md">Overview</a>
            <a href="#" class="block py-2 px-3 hover:bg-gray-200 rounded-md">Analytics</a>
            <a href="#" class="block py-2 px-3 hover:bg-gray-200 rounded-md">Card</a>
            <a href="#" class="block py-2 px-3 hover:bg-gray-200 rounded-md">Manage</a>
            <a href="#" class="block py-2 px-3 hover:bg-gray-200 rounded-md">Settings</a>
            <a href="#" class="block py-2 px-3 hover:bg-gray-200 rounded-md">Support</a>
            <a href="#" class="block py-2 px-3 hover:bg-gray-200 rounded-md">Sign Out</a>
        </nav>
    </div>

    <!-- Main content -->
    <div class="flex-1 flex flex-col">
        <!-- Navbar -->
        <div class="w-full bg-white shadow-md z-20 flex items-center px-4 py-2">
            <div class="flex-none lg:hidden">
                <button @click="drawerOpen = !drawerOpen" class="btn btn-square btn-ghost">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                         stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 6h16M4 12h16m-7 6h7" />
                    </svg>
                </button>
            </div>
            <div class="flex-1 text-xl font-semibold">iBanKo</div>
            <div x-data="dropdown" class="flex-none ">
                <button x-bind="trigger" class="flex items-center focus:outline-none">
                    <div class="avatar">
                        <div class="w-10 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            <img src="https://i.pravatar.cc/150?img=5" />
                        </div>
                    </div>
                </button>
                <!-- Dropdown menu -->
                <div x-bind="dialogue" class="absolute  mt-2 w-auto bg-white border border-gray-200 rounded-md shadow-lg py-1 z-20" style="display: none;">
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Life expectancy</p>
                                <h2 class="text-2xl font-bold">70</h2>
                                <p class="text-sm text-gray-500">years</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">80 years average</p>
                                <div class="h-12 flex items-end justify-around">
                                    <div class="w-2 bg-green-400" style="height: 60%;"></div>
                                    <div class="w-2 bg-red-400" style="height: 40%;"></div>
                                    <div class="w-2 bg-yellow-400" style="height: 80%;"></div>
                                    <div class="w-2 bg-blue-400" style="height: 70%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <div x-data="dropdown" class="flex-none ml-4">
                <button x-bind="trigger" class="flex items-center focus:outline-none">
                    <div class="avatar">
                        <div class="w-10 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            <img src="https://i.pravatar.cc/150?img=5" />
                        </div>
                    </div>
                </button>
                <!-- Dropdown menu -->
                <div x-bind="dialogue" class="absolute  mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg py-1 z-20" style="display: none;">
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                </div>
            </div>
        </div>
        <!-- Page content here -->
        <div class="p-4 overflow-y-auto flex-1">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Bessie Cooper 👋</h1>
                </div>
            </div>


            <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white shadow-md p-4 rounded-md">
                    <div class="flex items-center">
                        <img src="https://cdn-icons-png.flaticon.com/512/197/197484.png" alt="US Flag"
                             class="w-8 h-8">
                        <div class="ml-4">
                            <p class="text-lg font-bold">4558</p>
                            <p>US Dollar</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="relative pt-1">
                            <div class="flex mb-2 items-center justify-between">
                                <div>
                                        <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200">
                                            33%
                                        </span>
                                </div>
                            </div>
                            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                                <div style="width:33%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow-md p-4 rounded-md">
                    <div class="flex items-center">
                        <img src="https://cdn-icons-png.flaticon.com/512/197/197615.png" alt="EU Flag"
                             class="w-8 h-8">
                        <div class="ml-4">
                            <p class="text-lg font-bold">2670</p>
                            <p>Euro</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="relative pt-1">
                            <div class="flex mb-2 items-center justify-between">
                                <div>
                                        <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-yellow-600 bg-yellow-200">
                                            12%
                                        </span>
                                </div>
                            </div>
                            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                                <div style="width:12%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-yellow-500"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow-md p-4 rounded-md">
                    <div class="flex items-center">
                        <img src="https://cdn-icons-png.flaticon.com/512/197/197374.png" alt="UK Flag"
                             class="w-8 h-8">
                        <div class="ml-4">
                            <p class="text-lg font-bold">5590</p>
                            <p>Pound</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="relative pt-1">
                            <div class="flex mb-2 items-center justify-between">
                                <div>
                                        <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-red-600 bg-red-200">
                                            33%
                                        </span>
                                </div>
                            </div>
                            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                                <div style="width:33%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-500"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold">Progress for Life</h2>
                        <p class="text-sm text-green-600">You're performing better than average</p>
                    </div>
                    <div class="mt-4">
                        <canvas id="progressChart"></canvas>
                    </div>
                    <div class="mt-4 flex items-center">
                        <img src="https://i.pravatar.cc/150?img=3" alt="Profile Picture" class="w-10 h-10 rounded-full">
                        <div class="ml-4">
                            <p class="text-sm text-gray-500">How to live longer</p>
                            <p class="text-sm">In sit amet nibh in magna hru hium...</p>
                        </div>
                    </div>
                </div>

                <!-- Widget: Life Expectancy -->
                <div class="flex flex-col gap-4">
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Life expectancy</p>
                                <h2 class="text-2xl font-bold">70</h2>
                                <p class="text-sm text-gray-500">years</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">80 years average</p>
                                <div class="h-12 flex items-end justify-around">
                                    <div class="w-2 bg-green-400" style="height: 60%;"></div>
                                    <div class="w-2 bg-red-400" style="height: 40%;"></div>
                                    <div class="w-2 bg-yellow-400" style="height: 80%;"></div>
                                    <div class="w-2 bg-blue-400" style="height: 70%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Life expectancy</p>
                                <h2 class="text-2xl font-bold">70</h2>
                                <p class="text-sm text-gray-500">years</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">80 years average</p>
                                <div class="h-12 flex items-end justify-around">
                                    <div class="w-2 bg-green-400" style="height: 60%;"></div>
                                    <div class="w-2 bg-red-400" style="height: 40%;"></div>
                                    <div class="w-2 bg-yellow-400" style="height: 80%;"></div>
                                    <div class="w-2 bg-blue-400" style="height: 70%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Physical</p>
                                <h2 class="text-2xl font-bold">90 <span class="text-lg font-medium">/ 100</span></h2>
                                <p class="text-sm text-gray-500">Average 80 for 20-30 year old females</p>
                            </div>
                            <div>
                                <svg class="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-11.707a1 1 0 00-1.414 0L9 9.586 7.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                    </div>



                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Wellbeing</p>
                                <h2 class="text-2xl font-bold">90 <span class="text-lg font-medium">/ 100</span></h2>
                                <p class="text-sm text-gray-500">Average 70 for 20-30 year old females</p>
                            </div>
                            <div>
                                <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-11.707a1 1 0 00-1.414 0L9 9.586 7.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">Nutrition</p>
                                <h2 class="text-2xl font-bold">100 <span class="text-lg font-medium">/ 100</span></h2>
                                <p class="text-sm text-gray-500">Average 70 for 20-30 year old females</p>
                            </div>
                            <div>
                                <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-11.707a1 1 0 00-1.414 0L9 9.586 7.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-2xl font-semibold mb-4">Sales KPI</h2>
                    <div class="flex flex-wrap space-y-4 lg:space-y-0 lg:space-x-4">
                        <!-- Left Section -->
                        <div class="flex-1 space-y-6">
                            <!-- Profile -->
                            <div class="flex items-center space-x-4">
                                <div class="w-16 h-16 rounded-full bg-purple-600 flex items-center justify-center text-white text-2xl">
                                    S
                                </div>
                                <div>
                                    <h3 class="text-xl font-semibold">Sandersand Inc.</h3>
                                    <p class="text-gray-500">+1 (252) 258-3799</p>
                                    <p class="text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                            <!-- Metrics -->
                            <div class="grid grid-cols-2 gap-4">
                                <div class="flex items-center space-x-4 bg-gray-100 p-4 rounded-lg">
                                    <div class="text-3xl text-yellow-500"><i class="fas fa-chart-line"></i></div>
                                    <div class="text-lg font-semibold">Sales trend<br><span class="text-gray-500">$11 321</span></div>
                                </div>
                                <div class="flex items-center space-x-4 bg-gray-100 p-4 rounded-lg">
                                    <div class="text-3xl text-green-500"><i class="fas fa-tags"></i></div>
                                    <div class="text-lg font-semibold">Discounts<br><span class="text-gray-500">$22 725</span></div>
                                </div>
                                <div class="flex items-center space-x-4 bg-gray-100 p-4 rounded-lg">
                                    <div class="text-3xl text-purple-500"><i class="fas fa-chart-pie"></i></div>
                                    <div class="text-lg font-semibold">Net Profit<br><span class="text-gray-500">$21 234</span></div>
                                </div>
                                <div class="flex items-center space-x-4 bg-gray-100 p-4 rounded-lg">
                                    <div class="text-3xl text-red-500"><i class="fas fa-undo"></i></div>
                                    <div class="text-lg font-semibold">Refunds<br><span class="text-gray-500">$3 848</span></div>
                                </div>
                            </div>
                            <!-- Sales Plan -->
                            <div class="bg-gray-100 p-4 rounded-lg">
                                <h3 class="text-3xl font-semibold">Sales plan</h3>
                                <p class="text-5xl font-bold text-gray-900">65%</p>
                                <p class="text-gray-500">Percentage profit from total sales</p>
                            </div>
                        </div>
                        <!-- Right Section -->
                        <div class="w-full lg:w-1/3 bg-black text-white p-6 rounded-lg flex flex-col items-center">
                            <h3 class="text-lg font-semibold">Revenue Goal</h3>
                            <div class="flex-1 flex items-center justify-center">
                                <div class="relative">
                                    <svg class="w-32 h-32">
                                        <circle cx="50%" cy="50%" r="45%" stroke="yellow" stroke-width="10" fill="none"
                                                stroke-dasharray="113.097" stroke-dashoffset="0"></circle>
                                        <circle cx="50%" cy="50%" r="45%" stroke="green" stroke-width="10" fill="none"
                                                stroke-dasharray="113.097" stroke-dashoffset="37.699"></circle>
                                        <circle cx="50%" cy="50%" r="45%" stroke="purple" stroke-width="10" fill="none"
                                                stroke-dasharray="113.097" stroke-dashoffset="75.398"></circle>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center text-2xl font-semibold">
                                        264.2K
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-400">Total profit</p>
                            <p class="text-2xl font-semibold mt-4">67%</p>
                            <p class="text-gray-400">Plan completed</p>
                        </div>
                    </div>
                    <!-- Bottom Section -->
                    <div class="mt-6 flex justify-between items-center flex-col lg:flex-row">
                        <div class="flex-1 bg-gray-100 p-4 rounded-lg mr-4">
                            <h3 class="text-lg font-semibold">Cohort analysis indicators:</h3>
                            <p class="text-gray-500">Analyzes the behavior of a group of users who joined a product/service at the same time, over a certain period.</p>
                            <div class="mt-4 flex justify-between items-center">
                                <div class="text-purple-600"><i class="fas fa-chart-bar"></i> Open statistic</div>
                                <div class="text-purple-600"><i class="fas fa-percentage"></i> Percentage change</div>
                            </div>
                            <div class="mt-4">
                                <div class="bg-gray-200 h-2 rounded-full">
                                    <div class="bg-purple-600 h-2 rounded-full" style="width: 50%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white shadow-md p-4 rounded-md">
                    <h2 class="text-lg font-bold">Transactions</h2>
                    <div class="flex justify-between items-center mt-2">
                        <p class="text-gray-500">Month</p>
                        <button class="text-primary">View All</button>
                    </div>
                    <ul class="mt-4 space-y-2">
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>Jane Cooper</span>
                            </div>
                            <span>1200 USD</span>
                        </li>
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>Leslie Alexander</span>
                            </div>
                            <span>2890 Euro</span>
                        </li>
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>Flight Ticket</span>
                            </div>
                            <span>1000 Pound</span>
                        </li>
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>Robert Fox</span>
                            </div>
                            <span>2250 USD</span>
                        </li>
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>KFC</span>
                            </div>
                            <span>120 Euro</span>
                        </li>
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>Jacob Jones</span>
                            </div>
                            <span>1700 USD</span>
                        </li>
                        <li class="flex justify-between items-center py-2">
                            <div class="flex items-center">
                                <img src="https://cdn-icons-png.flaticon.com/512/2922/2922510.png" alt="User" class="w-6 h-6 rounded-full mr-2">
                                <span>Dev Cooper</span>
                            </div>
                            <span>4500 Pound</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- Drawer overlay -->
    <div @click="drawerOpen = false" :class="drawerOpen ? 'drawer-overlay drawer-overlay-open' : 'drawer-overlay'"></div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('sendMoneyChart').getContext('2d');
    const sendMoneyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['A month ago', 'Today'],
            datasets: [{
                label: 'Send Money',
                data: [10, 20, 15, 25, 22, 30],
                borderColor: 'rgba(255, 159, 64, 1)',
                borderWidth: 2,
                fill: false
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    display: true
                },
                y: {
                    display: true
                }
            }
        }
    });
</script>

<script>
    const progressChartCtx = document.getElementById('progressChart').getContext('2d');
    const progressChart = new Chart(progressChartCtx, {
        type: 'line',
        data: {
            labels: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
            datasets: [{
                label: 'Your Performance',
                data: [3, 2, 5, 4, 6, 5, 7],
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                fill: false
            },
                {
                    label: 'Average 30yo female',
                    data: [2, 2, 4, 3, 5, 4, 6],
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2,
                    fill: false
                }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    display: true
                },
                y: {
                    display: true
                }
            }
        }
    });
</script>
</body>

</html>
