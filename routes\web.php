<?php

use App\Livewire\Asrama\AsramaData;
use App\Livewire\Asrama\AsramaCreate;
use App\Livewire\HomePage;
use App\Livewire\User\ConfirmationMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;

Route::get('/', HomePage::class)->name('homepage');
Route::get('/kts', [\App\Http\Controllers\KTS\GenerateKts::class, 'generateSingle'])->name('kts.generate');
Route::get('/kts/multiple', [\App\Http\Controllers\KTS\GenerateKts::class, 'generateMultiple'])->name('kts.generate.multiple');
Route::get('/cetak-kts', [\App\Http\Controllers\KTS\GenerateKts::class, 'cetak'])->name('kts.cetak');

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->group(
    callback: function () {
        // Route::get('/dashboard', function () {
        //     return view('dashboard');
        // })->name('dashboard');

        Route::get('/dashboard', \App\Livewire\Dashboard::class)->name('dashboard');

        Route::get('/asrama', AsramaData::class)->name('asrama.index');
        Route::get('/asrama/create', AsramaCreate::class)->name('asrama.create');
        Route::get('/asrama/detail/{id}', \App\Livewire\Asrama\AsramaDetail::class)->name('asrama.detail');
        Route::get('/asrama/edit/{id}', \App\Livewire\Asrama\AsramaCreate::class)->name('asrama.edit');

        Route::get('/santri', \App\Livewire\Santri\SantriData::class)->name('santri.index');
        Route::get('/santri/create', \App\Livewire\Santri\SantriWizard::class)->name('santri.create');
        Route::get('/santri/detail/{id}', \App\Livewire\Santri\SantriDetail::class)->name('santri.detail');
        Route::get('/santri/santrinoninduk', \App\Livewire\Santri\SantriNonInduk::class)->name('santri.santrinoninduk');

        Route::get('/kelas', \App\Livewire\Kelas\KelasData::class)->name('kelas.index');
        Route::get('/kelas/create', \App\Livewire\Kelas\KelasCreate::class)->name('kelas.create');
        Route::get('/kelas/detail/{id}', \App\Livewire\Kelas\KelasDetail::class)->name('kelas.detail');

        Route::get('/kelas/detail/{id}/santri', \App\Livewire\Kelas\KelasSantri::class)->name('kelas.santri');
        Route::get('/kelas/detail/{id}/ustadz', \App\Livewire\Kelas\KelasUstadz::class)->name('kelas.ustadz');

        Route::get('/kts', \App\Livewire\Kts\CetakKts::class)->name('kts.index');

        Route::get('/users', \App\Livewire\User\UsersManagement::class)->name('users.index');
        // Route for creating a new user
        Route::get('/users/create', \App\Livewire\User\UserForm::class)->name('users.create');

        // Route for editing an existing user
        Route::get('/users/{userId}/edit', \App\Livewire\User\UserForm::class)
            ->name('users.edit')
            ->where('id', '[0-9]+');


        Route::get('/setting/general', \App\Livewire\Setting\GeneralSetting::class)
            ->name('setting.general');

        Route::get('/setting/kts', \App\Livewire\Setting\KtsSetting::class)->name('setting.kts');
        Route::get('/setting/restore', \App\Livewire\Setting\RestoreData::class)->name('setting.restore');

        // Stats
        Route::get('/stats', \App\Livewire\Stats\StatsIndex::class)->name('stats.index');
        Route::get('/stats/santri-per-kabupaten/{province_code}', \App\Livewire\Stats\SantriPerKabupaten::class)->name('stats.santri-per-kabupaten');
        Route::get('/stats/santri-per-provinsi', \App\Livewire\Stats\SantriPerProvinsi::class)->name('stats.santri-per-provinsi');
        Route::get('/stats/santri-per-komplek', \App\Livewire\Stats\SantriPerKomplek::class)->name('stats.santri-per-komplek');
        Route::get('/stats/duplicate-santri', \App\Livewire\Stats\DuplicateSantri::class)->name('stats.duplicate-santri');

    },
);

Route::get('/test', function () {
    return view('test');
});


Route::get('/test-email', function () {
    Mail::raw('This is a test email from Al Munawwir System.', function ($message) {
        $message->to('<EMAIL>') // Ganti dengan email tujuan
                ->subject('Test Email from Al Munawwir')
                ->from('<EMAIL>', 'Al Munawwir System');
    });

    return 'Email sent successfully!';
});


Route::get('/confirmation/{token}', ConfirmationMail::class)->name('confirmation-link');
