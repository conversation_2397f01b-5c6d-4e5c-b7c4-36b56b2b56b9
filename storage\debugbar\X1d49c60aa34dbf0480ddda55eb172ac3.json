{"__meta": {"id": "X1d49c60aa34dbf0480ddda55eb172ac3", "datetime": "2025-06-27 12:09:35", "utime": **********.693935, "method": "GET", "uri": "/api/ustadz?id_komplek=18", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[12:09:35] LOG.debug: 1", "message_html": null, "is_string": false, "label": "debug", "time": **********.669536, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.15659, "end": **********.693956, "duration": 0.5373659133911133, "duration_str": "537ms", "measures": [{"label": "Booting", "start": **********.15659, "relative_start": 0, "end": **********.531608, "relative_end": **********.531608, "duration": 0.3750181198120117, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.531634, "relative_start": 0.3750441074371338, "end": **********.693958, "relative_end": 2.1457672119140625e-06, "duration": 0.1623239517211914, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 31677488, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/ustadz", "middleware": "api, auth:sanctum, only.admin.komplek", "controller": "App\\Http\\Controllers\\Api\\UstadzController@index", "namespace": null, "prefix": "api/ustadz", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FUstadzController.php&line=18\" onclick=\"\">app/Http/Controllers/Api/UstadzController.php:18-71</a>"}, "queries": {"nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03964, "accumulated_duration_str": "39.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '30' limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.58959, "duration": 0.025589999999999998, "duration_str": "25.59ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "sistem_pondok", "explain": null, "start_percent": 0, "width_percent": 64.556}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.627441, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "sistem_pondok", "explain": null, "start_percent": 64.556, "width_percent": 2.624}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-27 12:09:35', `personal_access_tokens`.`updated_at` = '2025-06-27 12:09:35' where `id` = 30", "type": "query", "params": [], "bindings": ["2025-06-27 12:09:35", "2025-06-27 12:09:35", 30], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.633439, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "sistem_pondok", "explain": null, "start_percent": 67.18, "width_percent": 8.754}, {"sql": "select * from `role_users` where `role_users`.`user_id` = 2 and `role_users`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.646059, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "only.admin.komplek:22", "source": {"index": 19, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FMiddleware%2FOnlyAdminKomplek.php&line=22", "ajax": false, "filename": "OnlyAdminKomplek.php", "line": "22"}, "connection": "sistem_pondok", "explain": null, "start_percent": 75.933, "width_percent": 6.509}, {"sql": "select * from `role_users` where `role_users`.`user_id` = 2 and `role_users`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.651365, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "only.admin.komplek:22", "source": {"index": 19, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FMiddleware%2FOnlyAdminKomplek.php&line=22", "ajax": false, "filename": "OnlyAdminKomplek.php", "line": "22"}, "connection": "sistem_pondok", "explain": null, "start_percent": 82.442, "width_percent": 2.119}, {"sql": "select exists(select * from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'superadmin') as `exists`", "type": "query", "params": [], "bindings": [2, "superadmin"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 158}, {"index": 20, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 29}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.658427, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "User.php:158", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=158", "ajax": false, "filename": "User.php", "line": "158"}, "connection": "sistem_pondok", "explain": null, "start_percent": 84.561, "width_percent": 2.397}, {"sql": "select exists(select * from `role_users` where `role_users`.`user_id` = 2 and `role_users`.`user_id` is not null and `id_komplek` = 18 and exists (select * from `roles` where `role_users`.`role_id` = `roles`.`id` and `name` in ('admin'))) as `exists`", "type": "query", "params": [], "bindings": [2, 18, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.661645, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "only.admin.kom<PERSON>k:38", "source": {"index": 14, "namespace": "middleware", "name": "only.admin.komplek", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Middleware\\OnlyAdminKomplek.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FMiddleware%2FOnlyAdminKomplek.php&line=38", "ajax": false, "filename": "OnlyAdminKomplek.php", "line": "38"}, "connection": "sistem_pondok", "explain": null, "start_percent": 86.958, "width_percent": 1.766}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `users`.`id` = `role_users`.`user_id` and `name` = 'ustadz') and exists (select * from `role_users` where `users`.`id` = `role_users`.`user_id` and `id_komplek` = '18')", "type": "query", "params": [], "bindings": ["ustadz", "18"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.670957, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "UstadzController.php:41", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FUstadzController.php&line=41", "ajax": false, "filename": "UstadzController.php", "line": "41"}, "connection": "sistem_pondok", "explain": null, "start_percent": 88.724, "width_percent": 3.885}, {"sql": "select * from `role_users` where `role_users`.`user_id` in (15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.675318, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "UstadzController.php:41", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FUstadzController.php&line=41", "ajax": false, "filename": "UstadzController.php", "line": "41"}, "connection": "sistem_pondok", "explain": null, "start_percent": 92.608, "width_percent": 1.816}, {"sql": "select * from `kompleks` where `kompleks`.`id` in (18) and `kompleks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.680818, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "UstadzController.php:41", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FUstadzController.php&line=41", "ajax": false, "filename": "UstadzController.php", "line": "41"}, "connection": "sistem_pondok", "explain": null, "start_percent": 94.425, "width_percent": 3.91}, {"sql": "select * from `roles` where `roles`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.684557, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "UstadzController.php:41", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/UstadzController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\UstadzController.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FUstadzController.php&line=41", "ajax": false, "filename": "UstadzController.php", "line": "41"}, "connection": "sistem_pondok", "explain": null, "start_percent": 98.335, "width_percent": 1.665}]}, "models": {"data": {"App\\Models\\RoleUser": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRoleUser.php&line=1", "ajax": false, "filename": "RoleUser.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\Komplek": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FKomplek.php&line=1", "ajax": false, "filename": "Komplek.php", "line": "?"}}, "App\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 8, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/ustadz", "status_code": "<pre class=sf-dump id=sf-dump-1774286649 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1774286649\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-351209799 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id_komplek</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351209799\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-41529122 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-41529122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1830171287 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 30|kN******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:56721</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost:56721/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1830171287\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1240930571 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1240930571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1806250507 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 12:09:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806250507\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-589033124 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-589033124\", {\"maxDepth\":0})</script>\n"}}