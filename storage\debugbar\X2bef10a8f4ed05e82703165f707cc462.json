{"__meta": {"id": "X2bef10a8f4ed05e82703165f707cc462", "datetime": "2025-06-27 09:14:00", "utime": **********.301137, "method": "GET", "uri": "/api/me", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751015639.094027, "end": **********.301183, "duration": 1.2071559429168701, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1751015639.094027, "relative_start": 0, "end": **********.082947, "relative_end": **********.082947, "duration": 0.9889199733734131, "duration_str": "989ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.082968, "relative_start": 0.***************, "end": **********.301185, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/me", "middleware": "api, auth:sanctum", "uses": "Closure(Request $request) {#503\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#500 …}\n  file: \"C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php\"\n  line: \"17 to 26\"\n}", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Froutes%2Fapi.php&line=17\" onclick=\"\">routes/api.php:17-26</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.039619999999999995, "accumulated_duration_str": "39.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '22' limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1751208, "duration": 0.02238, "duration_str": "22.38ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "sistem_pondok", "explain": null, "start_percent": 0, "width_percent": 56.487}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.223442, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "sistem_pondok", "explain": null, "start_percent": 56.487, "width_percent": 5.628}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-27 09:14:00', `personal_access_tokens`.`updated_at` = '2025-06-27 09:14:00' where `id` = 22", "type": "query", "params": [], "bindings": ["2025-06-27 09:14:00", "2025-06-27 09:14:00", 22], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.230298, "duration": 0.010060000000000001, "duration_str": "10.06ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "sistem_pondok", "explain": null, "start_percent": 62.115, "width_percent": 25.391}, {"sql": "select * from `role_users` where `role_users`.`user_id` = 2 and `role_users`.`user_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 214}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.262839, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "api.php:23", "source": {"index": 20, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Froutes%2Fapi.php&line=23", "ajax": false, "filename": "api.php", "line": "23"}, "connection": "sistem_pondok", "explain": null, "start_percent": 87.506, "width_percent": 2.625}, {"sql": "select * from `roles` where `roles`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 214}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.267919, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "api.php:23", "source": {"index": 22, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Froutes%2Fapi.php&line=23", "ajax": false, "filename": "api.php", "line": "23"}, "connection": "sistem_pondok", "explain": null, "start_percent": 90.131, "width_percent": 2.07}, {"sql": "select exists(select * from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'admin') as `exists`", "type": "query", "params": [], "bindings": [2, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 164}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 199}, {"index": 21, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}], "start": **********.281368, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:164", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=164", "ajax": false, "filename": "User.php", "line": "164"}, "connection": "sistem_pondok", "explain": null, "start_percent": 92.201, "width_percent": 3.407}, {"sql": "select `roles`.*, `role_users`.`user_id` as `pivot_user_id`, `role_users`.`role_id` as `pivot_role_id`, `role_users`.`id_komplek` as `pivot_id_komplek`, `role_users`.`created_at` as `pivot_created_at`, `role_users`.`updated_at` as `pivot_updated_at` from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'admin' limit 1", "type": "query", "params": [], "bindings": [2, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 200}, {"index": 17, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 24}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 214}], "start": **********.290051, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "User.php:200", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=200", "ajax": false, "filename": "User.php", "line": "200"}, "connection": "sistem_pondok", "explain": null, "start_percent": 95.608, "width_percent": 4.392}]}, "models": {"data": {"App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\RoleUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRoleUser.php&line=1", "ajax": false, "filename": "RoleUser.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/me", "status_code": "<pre class=sf-dump id=sf-dump-899216550 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-899216550\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-194674684 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-194674684\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-221408661 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-221408661\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-694010269 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 22|o9******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:56818</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost:56818/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694010269\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-927912422 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-927912422\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-203707939 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:14:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203707939\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-444793228 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-444793228\", {\"maxDepth\":0})</script>\n"}}