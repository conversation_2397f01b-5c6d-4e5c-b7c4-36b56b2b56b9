{"__meta": {"id": "X79014ab46292ae8ddbedec2c78790519", "datetime": "2025-06-27 08:02:09", "utime": **********.291508, "method": "POST", "uri": "/api/login", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.345157, "end": **********.291525, "duration": 0.9463679790496826, "duration_str": "946ms", "measures": [{"label": "Booting", "start": **********.345157, "relative_start": 0, "end": **********.736869, "relative_end": **********.736869, "duration": 0.3917121887207031, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.736886, "relative_start": 0.39172911643981934, "end": **********.291527, "relative_end": 2.1457672119140625e-06, "duration": 0.5546410083770752, "duration_str": "555ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 32276944, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/login", "middleware": "api", "controller": "App\\Http\\Controllers\\Api\\AuthController@login", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=14\" onclick=\"\">app/Http/Controllers/Api/AuthController.php:14-66</a>"}, "queries": {"nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14388, "accumulated_duration_str": "144ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.797601, "duration": 0.03197, "duration_str": "31.97ms", "memory": 0, "memory_str": null, "filename": "AuthController.php:24", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=24", "ajax": false, "filename": "AuthController.php", "line": "24"}, "connection": "sistem_pondok", "explain": null, "start_percent": 0, "width_percent": 22.22}, {"sql": "insert into `personal_access_tokens` (`name`, `token`, `abilities`, `expires_at`, `tokenable_id`, `tokenable_type`, `updated_at`, `created_at`) values ('flutter_app', '68e84604780b6c0cff1656ca985f55d84e7ea60ce32ce539073e9f76ced575b3', '[\\\"*\\\"]', null, 2, 'App\\\\Models\\\\User', '2025-06-27 08:02:09', '2025-06-27 08:02:09')", "type": "query", "params": [], "bindings": ["flutter_app", "68e84604780b6c0cff1656ca985f55d84e7ea60ce32ce539073e9f76ced575b3", "[\"*\"]", null, 2, "App\\Models\\User", "2025-06-27 08:02:09", "2025-06-27 08:02:09"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 53}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 35}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.139401, "duration": 0.03151, "duration_str": "31.51ms", "memory": 0, "memory_str": null, "filename": "HasApiTokens.php:53", "source": {"index": 18, "namespace": null, "name": "vendor/laravel/sanctum/src/HasApiTokens.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FHasApiTokens.php&line=53", "ajax": false, "filename": "HasApiTokens.php", "line": "53"}, "connection": "sistem_pondok", "explain": null, "start_percent": 22.22, "width_percent": 21.9}, {"sql": "select * from `role_users` where `role_users`.`user_id` = 2 and `role_users`.`user_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.189386, "duration": 0.055170000000000004, "duration_str": "55.17ms", "memory": 0, "memory_str": null, "filename": "AuthController.php:45", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=45", "ajax": false, "filename": "AuthController.php", "line": "45"}, "connection": "sistem_pondok", "explain": null, "start_percent": 44.12, "width_percent": 38.344}, {"sql": "select * from `roles` where `roles`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 45}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2507281, "duration": 0.01663, "duration_str": "16.63ms", "memory": 0, "memory_str": null, "filename": "AuthController.php:45", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FHttp%2FControllers%2FApi%2FAuthController.php&line=45", "ajax": false, "filename": "AuthController.php", "line": "45"}, "connection": "sistem_pondok", "explain": null, "start_percent": 82.465, "width_percent": 11.558}, {"sql": "select exists(select * from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'admin') as `exists`", "type": "query", "params": [], "bindings": [2, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 164}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 199}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.275527, "duration": 0.0063, "duration_str": "6.3ms", "memory": 0, "memory_str": null, "filename": "User.php:164", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=164", "ajax": false, "filename": "User.php", "line": "164"}, "connection": "sistem_pondok", "explain": null, "start_percent": 94.023, "width_percent": 4.379}, {"sql": "select `roles`.*, `role_users`.`user_id` as `pivot_user_id`, `role_users`.`role_id` as `pivot_role_id`, `role_users`.`id_komplek` as `pivot_id_komplek`, `role_users`.`created_at` as `pivot_created_at`, `role_users`.`updated_at` as `pivot_updated_at` from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'admin' limit 1", "type": "query", "params": [], "bindings": [2, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 200}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/AuthController.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Http\\Controllers\\Api\\AuthController.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.284093, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "User.php:200", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=200", "ajax": false, "filename": "User.php", "line": "200"}, "connection": "sistem_pondok", "explain": null, "start_percent": 98.401, "width_percent": 1.599}]}, "models": {"data": {"App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\RoleUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRoleUser.php&line=1", "ajax": false, "filename": "RoleUser.php", "line": "?"}}}, "count": 4, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/login", "status_code": "<pre class=sf-dump id=sf-dump-429145325 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-429145325\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-843422426 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-843422426\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-634485768 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634485768\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-65283688 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:49941</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost:49941/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65283688\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-690015034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-690015034\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-117169608 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 08:02:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117169608\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-512045910 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-512045910\", {\"maxDepth\":0})</script>\n"}}