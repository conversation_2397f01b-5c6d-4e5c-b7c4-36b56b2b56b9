{"__meta": {"id": "X8a4575fdb99ac5eeea00afcc0d8f0bc6", "datetime": "2025-06-27 09:56:19", "utime": **********.967494, "method": "GET", "uri": "/api/me", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.544087, "end": **********.967513, "duration": 0.42342615127563477, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.544087, "relative_start": 0, "end": **********.872554, "relative_end": **********.872554, "duration": 0.32846713066101074, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.872569, "relative_start": 0.*****************, "end": **********.967515, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "94.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/me", "middleware": "api, auth:sanctum", "uses": "Closure(Request $request) {#503\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#500 …}\n  file: \"C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php\"\n  line: \"17 to 26\"\n}", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Froutes%2Fapi.php&line=17\" onclick=\"\">routes/api.php:17-26</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01808, "accumulated_duration_str": "18.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '24' limit 1", "type": "query", "params": [], "bindings": ["24"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.90766, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "sistem_pondok", "explain": null, "start_percent": 0, "width_percent": 22.124}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9198759, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "sistem_pondok", "explain": null, "start_percent": 22.124, "width_percent": 4.259}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-06-27 09:56:19', `personal_access_tokens`.`updated_at` = '2025-06-27 09:56:19' where `id` = 24", "type": "query", "params": [], "bindings": ["2025-06-27 09:56:19", "2025-06-27 09:56:19", 24], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.923007, "duration": 0.0103, "duration_str": "10.3ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "sistem_pondok", "explain": null, "start_percent": 26.383, "width_percent": 56.969}, {"sql": "select * from `role_users` where `role_users`.`user_id` = 2 and `role_users`.`user_id` is not null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 214}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.948037, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "api.php:23", "source": {"index": 20, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Froutes%2Fapi.php&line=23", "ajax": false, "filename": "api.php", "line": "23"}, "connection": "sistem_pondok", "explain": null, "start_percent": 83.352, "width_percent": 5.088}, {"sql": "select * from `roles` where `roles`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 214}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.951993, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "api.php:23", "source": {"index": 22, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Froutes%2Fapi.php&line=23", "ajax": false, "filename": "api.php", "line": "23"}, "connection": "sistem_pondok", "explain": null, "start_percent": 88.44, "width_percent": 3.927}, {"sql": "select exists(select * from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'admin') as `exists`", "type": "query", "params": [], "bindings": [2, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 164}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 199}, {"index": 21, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}], "start": **********.959657, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "User.php:164", "source": {"index": 14, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=164", "ajax": false, "filename": "User.php", "line": "164"}, "connection": "sistem_pondok", "explain": null, "start_percent": 92.367, "width_percent": 4.314}, {"sql": "select `roles`.*, `role_users`.`user_id` as `pivot_user_id`, `role_users`.`role_id` as `pivot_role_id`, `role_users`.`id_komplek` as `pivot_id_komplek`, `role_users`.`created_at` as `pivot_created_at`, `role_users`.`updated_at` as `pivot_updated_at` from `roles` inner join `role_users` on `roles`.`id` = `role_users`.`role_id` where `role_users`.`user_id` = 2 and `name` = 'admin' limit 1", "type": "query", "params": [], "bindings": [2, "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 200}, {"index": 17, "namespace": null, "name": "routes/api.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\routes\\api.php", "line": 24}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 243}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 214}], "start": **********.962111, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "User.php:200", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\laragon\\www\\sistem-pondok-dashboard\\app\\Models\\User.php", "line": 200}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=200", "ajax": false, "filename": "User.php", "line": "200"}, "connection": "sistem_pondok", "explain": null, "start_percent": 96.681, "width_percent": 3.319}]}, "models": {"data": {"App\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\RoleUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fsistem-pondok-dashboard%2Fapp%2FModels%2FRoleUser.php&line=1", "ajax": false, "filename": "RoleUser.php", "line": "?"}}}, "count": 5, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/me", "status_code": "<pre class=sf-dump id=sf-dump-970515501 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-970515501\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1920718864 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1920718864\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1847587093 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1847587093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1059750155 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 24|mJ******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:58652</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost:58652/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059750155\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1019134009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1019134009\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1619588999 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 09:56:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1619588999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2055320948 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2055320948\", {\"maxDepth\":0})</script>\n"}}