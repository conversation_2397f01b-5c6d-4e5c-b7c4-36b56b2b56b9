import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
		'./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
		 './vendor/laravel/jetstream/**/*.blade.php',
		 './storage/framework/views/*.php',
		 './resources/views/**/*.blade.php',
		 "./vendor/robsontenorio/mary/src/View/Components/**/*.php"
	],
    darkMode: false,


    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },

            transitionProperty: {
                'height': 'height',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                flip: {
                    '0%': { transform: 'rotateY(90deg)', opacity: '0' },
                    '100%': { transform: 'rotateY(0deg)', opacity: '1' },
                },
            },
            animation: {
                fadeIn: 'fadeIn 0.5s ease-in-out',
                flip: 'flip 0.75s ease-in-out',
            },

        },
    },

    plugins: [
		// forms,
		typography,
		require("daisyui")
	],
    daisyui: {
        themes: ["light"],
      },
};
