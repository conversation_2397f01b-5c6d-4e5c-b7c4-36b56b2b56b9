<?php

namespace Tests\Feature;

use App\Http\Controllers\Api\KelasController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Gate;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class KelasApiTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Disable middleware agar kita fokus pada logika route/controller saja
        $this->withoutMiddleware();

        // Definisikan route sementara
        Route::middleware('api')->prefix('api/kelas')->group(function () {
            Route::get('/', [KelasController::class, 'index']);
            Route::get('{id}/santri', [KelasController::class, 'santri']);
        });
    }

    #[Test]
    public function it_returns_kelas_list(): void
    {
        $mock = $this->mock(KelasController::class, function ($mock) {
            $mock->shouldReceive('index')->andReturn(response()->json([
                ['id' => 1, 'nama' => 'Kelas A'],
                ['id' => 2, 'nama' => 'Kelas B'],
            ]));
        });

        $response = $this->getJson('/api/kelas');

        $response->assertStatus(200)
                 ->assertJson([
                     ['id' => 1, 'nama' => 'Kelas A'],
                     ['id' => 2, 'nama' => 'Kelas B'],
                 ]);
    }

    #[Test]
    public function it_returns_santri_in_kelas(): void
    {
        $mock = $this->mock(KelasController::class, function ($mock) {
            $mock->shouldReceive('santri')->with(1)->andReturn(response()->json([
                ['id' => 101, 'nama' => 'Santri 1'],
                ['id' => 102, 'nama' => 'Santri 2'],
            ]));
        });

        $response = $this->getJson('/api/kelas/1/santri');

        $response->assertStatus(200)
                 ->assertJson([
                     ['id' => 101, 'nama' => 'Santri 1'],
                     ['id' => 102, 'nama' => 'Santri 2'],
                 ]);
    }
}
