<?php

namespace Tests\Feature;

use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MiddlewareKomplekAccessTest extends TestCase
{
    // Tambahkan ini jika ingin DB di-refresh setiap test
    // use RefreshDatabase;

    protected $komplekId = 1;

    protected function setUp(): void
    {
        parent::setUp();

        // Dummy route yang pakai middleware terkait
        Route::middleware(['api', 'auth:sanctum', 'can.access.absensi'])
            ->get('/api/test-access', fn () => response()->json(['ok' => true]));

        Route::middleware(['api', 'auth:sanctum', 'can.manage.absensi'])
            ->post('/api/test-manage', fn () => response()->json(['ok' => true]));
    }

    public function test_admin_can_access_absensi()
    {
        $user = $this->createUserWithRole('admin');

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/test-access?id_komplek=' . $this->komplekId);
        $response->assertStatus(200)->assertJson(['ok' => true]);
    }

    public function test_ustadz_can_manage_absensi()
    {
        $user = $this->createUserWithRole('ustadz');

        Sanctum::actingAs($user);

        $response = $this->postJson('/api/test-manage', ['id_komplek' => $this->komplekId]);
        $response->assertStatus(200)->assertJson(['ok' => true]);
    }

    public function test_superadmin_cannot_access_or_manage_absensi()
    {
        $user = $this->createUserWithRole('superadmin');

        Sanctum::actingAs($user);

        $this->getJson('/api/test-access?id_komplek=' . $this->komplekId)
            ->assertStatus(403)
            ->assertJsonFragment(['message' => 'Forbidden: Superadmin cannot access absensi']);

        $this->postJson('/api/test-manage', ['id_komplek' => $this->komplekId])
            ->assertStatus(403)
            ->assertJsonFragment(['message' => 'Forbidden: Superadmin cannot manage absensi']);
    }

    public function test_unauthorized_user_blocked()
    {
        $user = User::factory()->create(); // user tanpa role

        Sanctum::actingAs($user);

        $this->getJson('/api/test-access?id_komplek=' . $this->komplekId)
            ->assertStatus(403);

        $this->postJson('/api/test-manage', ['id_komplek' => $this->komplekId])
            ->assertStatus(403);
    }

    private function createUserWithRole(string $roleName): User
    {
        $user = User::factory()->create();

        $role = Role::where('name', $roleName)->firstOrFail(); // pakai role yang sudah ada

        $user->roles()->attach($role->id, ['id_komplek' => $this->komplekId]);

        return $user;
    }
}
