<?php

namespace Tests\Feature;

use App\Http\Controllers\Api\PengaturanTahfidzController;
use App\Models\PengaturanTahfidzKomplek;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\Facades\Route;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class PengaturanTahfidzApiTest extends TestCase
{
    use WithoutMiddleware;

    #[Test]
    public function it_returns_setting_when_data_exists(): void
    {
        $mock = Mockery::mock('alias:' . PengaturanTahfidzKomplek::class);
        $mock->shouldReceive('where')->with('id_komplek', 1)->andReturnSelf();
        $mock->shouldReceive('where')->with('aktif', true)->andReturnSelf();
        $mock->shouldReceive('orderByDesc')->with('berlaku_mulai')->andReturnSelf();
        $mock->shouldReceive('first')->andReturn((object)[
            'mode' => 'ayat',
            'berlaku_mulai' => '2024-06-01',
        ]);

        Route::get('/api/pengaturan-tahfidz/{id}', [PengaturanTahfidzController::class, 'show']);

        $this->getJson('/api/pengaturan-tahfidz/1')
            ->assertStatus(200)
            ->assertJson([
                'mode' => 'ayat',
                'berlaku_mulai' => '2024-06-01',
            ]);
    }

    #[Test]
    public function it_returns_default_when_data_not_found(): void
    {
        $mock = Mockery::mock('alias:' . PengaturanTahfidzKomplek::class);
        $mock->shouldReceive('where')->andReturnSelf();
        $mock->shouldReceive('orderByDesc')->andReturnSelf();
        $mock->shouldReceive('first')->andReturn(null);

        Route::get('/api/pengaturan-tahfidz/{id}', [PengaturanTahfidzController::class, 'show']);

        $this->getJson('/api/pengaturan-tahfidz/99')
            ->assertStatus(200)
            ->assertJson([
                'mode' => 'halaman',
                'berlaku_mulai' => null,
            ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
