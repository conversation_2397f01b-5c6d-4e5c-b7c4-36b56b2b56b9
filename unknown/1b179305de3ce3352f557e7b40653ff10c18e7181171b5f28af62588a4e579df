<?php

namespace App\Livewire\Santri;

use App\Models\Santri;
use App\Services\NomorIndukService;
use App\Services\SantriService;
use Livewire\Component;
use Log;
use Mary\Traits\Toast;

class SantriNonInduk extends Component
{
    use Toast;
    public $noninduk = 0;
    public $dataSantri;
    public $idKomplek;

    public $tahunMasuk;

    public $progress = 0;

    public function mount()
    {
        $auth = auth()->user();

        if (!$auth->isAdmin && !$auth->isSuperadmin) {
            return abort(404);
        }
        $this->idKomplek = $auth->roleUsers()->value('id_komplek');
        $this->getNonInduk();

        $this->getSantri();

    }

    public function getNonInduk()
    {
        $santriService = new SantriService();
        $this->noninduk = $santriService->countSantriWithoutNomorIndukBaru($this->idKomplek);
    }

    public function getSantri()
    {
        $santriService = new SantriService();
        $this->dataSantri = $santriService->getSantriWithoutNomorIndukBaru($this->idKomplek);
    }

    public function generate(Santri $item)
    {
        try {
            $nomorIndukService = new NomorIndukService();
            $nomorIndukService->generateNomorIndukForSingleSantri($item);

            $this->success('Nomor induk berhasil di generate');

            $this->getNonInduk();
            $this->getSantri();
        } catch (\Throwable $th) {
            $this->error('Nomor induk gagal di generate');
            Log::error('Error generating nomor induk for Santri ID ' . $item->id . ': ' . $th->getMessage());
        }
    }

    public function generateAll()
    {
        try {
            $totalBefore = $this->noninduk;
            $totalProgress = 0;

            $nomorIndukService = new NomorIndukService();

            // $this->dispatch('set-progress', $this->progress);

            foreach ($this->dataSantri as $item) {
                $nomorIndukService->generateNomorIndukForSingleSantri($item);
                $totalProgress++;
                $this->progress = round($totalProgress / $totalBefore * 100);

                // $this->dispatch('set-progress', $this->progress);

                $this->stream(
                    to: 'generate-progress',
                    content: $this->progressContent(),
                    replace: true,
                );
            }

            $this->getNonInduk();
            $this->getSantri();

            $this->progress = 0;

            $this->success($totalProgress . ' Nomor induk berhasil di buat');

        } catch (\Throwable $th) {
            $this->error('Nomor induk gagal di generate');
            Log::error('Error generating nomor induk for Santri ID ' . $item->id . ': ' . $th->getMessage());
        }
    }

    public function progressContent()
    {
        return '<div wire:stream="generate-progress"
                class="radial-progress bg-primary text-primary-content border-primary border-4"
                style="--value: ' . $this->progress . ';"
                role="progressbar">
                ' . $this->progress . ' %
            </div>';
    }

    public function render()
    {
        return view('livewire.santri.santri-non-induk');
    }

}
