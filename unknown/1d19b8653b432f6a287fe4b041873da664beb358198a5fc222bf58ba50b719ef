<?php

namespace App\Livewire\User;

use App\Models\Komplek;
use App\Models\Role;
use App\Models\User;
use App\Services\ConfirmationLinkService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Livewire\Component;
use Mary\Traits\Toast;

class UserForm extends Component
{
    use Toast;

    public $roles = [];
    public $kompleks = [];
    public $user;
    public $isEditMode = false;

    // Form fields
    public $name;
    public $email;
    public $phone;
    public $password;
    public $password_confirmation;
    public $role;
    public $selectedKomplek;

    public $modalPassword = false;

    public function mount($userId = null)
    {
        $this->initializeRoles();
        $this->initializeKompleks();

        if ($userId) {
            $this->isEditMode = true;
            $this->user = User::findOrFail($userId);

            $currentUser = auth()->user();
            if (!($currentUser->isAdmin || $currentUser->isSuperadmin || $currentUser->id === $userId)) {
                return redirect()->route('users.index');
            }

            $this->initializeFormFields();
            $this->role = $this->user->role()->first()->id ?? null;
        }
    }

    protected function initializeRoles()
    {
        $currentUser = auth()->user();
        $rolesQuery = Role::query();

        if (!$currentUser->hasRole('superadmin')) {
            $rolesQuery->where('name', '!=', 'superadmin');
        }

        $this->roles = $rolesQuery->get()->map(fn($role) => ['id' => $role->id, 'name' => $role->name]);
    }

    protected function initializeKompleks()
    {
        $currentUser = auth()->user();

        if ($currentUser->hasRole('superadmin')) {
            $this->kompleks = Komplek::all()->map(fn($komplek) => ['id' => $komplek->id, 'name' => $komplek->nama_komplek]);
        } else {
            $assignedKomplek = $currentUser->assignedKomplek();
            $this->kompleks = $assignedKomplek ? [['id' => $assignedKomplek->id, 'name' => $assignedKomplek->nama_komplek]] : [];
        }

        $this->selectedKomplek = $this->user?->assignedKomplek()?->id ?? null;
    }

    protected function initializeFormFields()
    {
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        $this->phone = $this->user->phone;
    }

    public function save()
    {
        $validatedData = $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . ($this->user->id ?? 'NULL'),
            'phone' => 'nullable|numeric|digits_between:8,15',
            'role' => 'required|exists:roles,id',
            'selectedKomplek' => 'nullable|exists:kompleks,id',
        ]);

        if ($this->isEditMode) {
            $this->updateUser();
        } else {
            $this->createUser();
        }

        return redirect()->route('users.index');
    }

    protected function updateUser()
    {
        $this->user->update([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
        ]);

        if (!empty($this->password)) {
            $this->user->update(['password' => Hash::make($this->password)]);
        }

        $this->user->roles()->sync([$this->role => ['id_komplek' => $this->selectedKomplek]]);
        $this->user->save();

        $this->success('User updated successfully!', timeout: 3000);
    }

    protected function createUser()
    {
        $newUser = User::create([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'password' => Hash::make(Str::random(8)),
        ]);

        $newUser->roles()->attach($this->role, ['id_komplek' => $this->selectedKomplek]);
        $newUser->save();

        app(ConfirmationLinkService::class)->createConfirmationLink($newUser);

        $this->success('User created successfully!', timeout: 3000);
    }

    public function savePassword()
    {
        $this->validate([
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required',
        ], [
            'password.required' => 'Password wajib diisi.',
            'password.min' => 'Password harus terdiri dari minimal 8 karakter.',
            'password.confirmed' => 'Password dan konfirmasi password tidak cocok.',
            'password_confirmation.required' => 'Konfirmasi password wajib diisi.',
        ]);

        $this->user->update(['password' => Hash::make($this->password)]);
        $this->modalPassword = false;
        $this->success('Password updated successfully!', timeout: 3000);
    }

    public function sendConfirmationEmail()
    {
        app(ConfirmationLinkService::class)->createConfirmationLink($this->user);
        $this->success('Confirmation email sent successfully!', timeout: 3000);
    }

    public function render()
    {
        return view('livewire.user.user-form', [
            'roles' => $this->roles,
            'kompleks' => $this->kompleks,
        ]);
    }
}
