<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_users', function (Blueprint $table) {
            $table->id();

            // Foreign key untuk user_id
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Foreign key untuk role_id
            $table->unsignedBigInteger('role_id');
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');

            // Foreign key untuk id_komplek
            $table->unsignedBigInteger('id_komplek')->nullable();
            $table->foreign('id_komplek')->references('id')->on('kompleks')->onDelete('set null');

            $table->timestamps();

            // Unique constraint agar tiap user hanya punya satu role per komplek
            $table->unique(['user_id', 'role_id', 'id_komplek']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role_users');
    }
};
