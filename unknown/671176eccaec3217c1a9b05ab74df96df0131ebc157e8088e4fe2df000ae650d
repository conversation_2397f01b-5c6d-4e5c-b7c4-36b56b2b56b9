stages:
  - build
  - test
  - deploy

variables:
  PHP_VERSION: "8.3"
  NODE_VERSION: "20"
  DEPLOY_PATH: "~/sistem-pondok-dashboard"

before_script:
  # Instalasi dependencies
  - apt-get update && apt-get install -y openssh-client curl rsync git unzip libpng-dev libjpeg-dev libfreetype6-dev zlib1g-dev

  # Menyiapkan SSH Key
  - mkdir -p ~/.ssh
  - eval $(ssh-agent -s)
  - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  - ssh-keyscan -t rsa ************** >> ~/.ssh/known_hosts

  # Instalasi Composer
  - curl -sS https://getcomposer.org/installer | php
  - mv composer.phar /usr/local/bin/composer

  # Instalasi Node.js
  - curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
  - apt-get install -y nodejs

  # Verifikasi versi <PERSON>, Composer, dan <PERSON>.js
  - php -v
  - composer --version
  - node -v
  - npm -v

build:
  stage: build
  image: php:$PHP_VERSION-cli
  script:
    - composer install --no-dev --optimize-autoloader
    - npm ci
    - npm run build
    - |
      if [ ! -d "public/build" ]; then
        echo "Folder public/build tidak ditemukan. Membuat folder kosong."
        mkdir -p public/build
      fi
  artifacts:
    paths:
      - public/build
    expire_in: 1 week

test:
  stage: test
  image: php:$PHP_VERSION-cli
  script:
    - composer install
    - php artisan test

deploy:
  stage: deploy
  image: php:$PHP_VERSION-cli
  script:
    # Periksa folder build di server
    - ssh -T almunaww@************** "if [ ! -d \"$DEPLOY_PATH/public/build\" ]; then mkdir -p \"$DEPLOY_PATH/public/build\"; fi"

    # Transfer file menggunakan SCP
    - scp -r public/build/* almunaww@**************:$DEPLOY_PATH/public/build/

    # Migrasi database dan clear cache
    - |
      ssh -T almunaww@************** << EOF
        cd $DEPLOY_PATH
        php artisan migrate --force
        php artisan config:cache
        php artisan icons:clear
        php artisan icons:cache
      EOF
  only:
    - main
