<?php

namespace App\Livewire\Kts;

use App\Models\Komplek;
use App\Models\Santri;
use App\Services\KtsService;
use Illuminate\Support\Facades\Crypt;
use Livewire\Component;

class CetakKts extends Component
{
    public $id_komplek;
    public $kompleks;
    public array $tahun;
    public $angkatan;

    public $resumeData;

    public function mount()
    {
        $this->kompleks = Komplek::all()->map(
            fn ($komplek) => [
                'id' => $komplek->id,
                'name' => $komplek->nama_komplek,
            ]
        )->toArray();

        $this->tahun = array_merge(
            [['id' => 0, 'name' => 'Semua Angkatan']],
            array_map(function($year) {
                return ['id' => $year, 'name' => $year];
            }, range(date('Y'), 2000))
        );
    }

    public function check(){
        $this->validate(
            [
                'id_komplek' => 'required',
                'angkatan' => 'required',
            ],
            [
                'id_komplek.required' => 'Komplek harus dipilih',
                'angkatan.required' => 'Angkatan harus dipilih',
            ]
        );

        $angkatan = $this->angkatan;

        if ($this->angkatan == 0) {
            $angkatan = "Semua Angkatan";
            $data = Santri::where('id_komplek', $this->id_komplek)->where('is_aktif', 1)->orderBy('nomor_induk_baru', 'asc')->pluck('id');

        } else {
            $data = Santri::where('id_komplek', $this->id_komplek)->where('tahun_masuk', $this->angkatan)->where('is_aktif', 1)->pluck('id');
        }



        $this->resumeData = (object) [
            'komplek' => collect($this->kompleks)->firstWhere('id', $this->id_komplek)['name'],
            'angkatan' => $angkatan,
            'data' => $data,
            'encryptedData' => Crypt::encrypt($data),
        ];

    }



    public function render()
    {
        return view('livewire.kts.cetak-kts');
    }

}
