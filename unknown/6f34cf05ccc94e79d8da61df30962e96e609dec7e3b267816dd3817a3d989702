<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class TestSantriSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Data santri yang diberikan
        $item = [
            "id_santri" => "1575",
            "unik" => "607b81ac8e9b55.86218990",
            "nomor_induk" => "19111201575",
            "alamat" => "Sumbersuko - Purwosari - Pasuruan - Jawa Timur",
            "hp" => "085855023549",
            "email" => "<EMAIL>",
            "lahir" => "KABUPATEN PASURUAN",
            "kelamin" => "perempuan",
            "ortu" => "",
            "tgl" => "23",
            "bln" => "1",
            "thn" => "2001",
            "pend_terakhir" => "SMA\/MA",
            "jamdaftar" => "07:47:40",
            "status" => "1",
            "anak_ke" => "2",
            "jmlsaudara" => "3",
            "jmlmahrom" => "",
            "mahrom" => "",
            "pernah_mondok" => "",
            "pondok_sebelumnya" => "",
            "tujuan_mondok" => "",
            "pilihan_program" => "",
            "ayah" => "Mukhammad Sholeh",
            "ibu" => "Mufarrohah",
            "pekerjaan_ayah" => "Kontraktor",
            "pekerjaan_ibu" => "Ibu Rumah Tangga",
            "pend_terakhir_ayah" => "SMA\/MA",
            "pend_terakhir_ibu" => "SMA\/MA",
            "alamat_ortu" => "Sumbersuko - Purwosari - Pasuruan - Jawa Timur",
            "telp_ortu" => "",
            "tanggal_diterima" => "2019-06-26",
            "foto" => "19111201575.jpeg",
            "id_lemari" => "0",
            "nama" => "Tasya Nur Diana",
            "pengantar" => "",
            "namanya" => "",
            "last_update" => "2021-07-28",
            "alamat_pengantar" => "",
            "saudarakandung" => null,
            "pengurus" => "0",
            "baru" => "0",
            "kosmakan" => "0",
            "petugas" => "138",
            "tanggal_lulus" => "0000-00-00",
            "keterangan_status" => "",
            "tanggal_aktif" => "2019-07-01",
            "tanggalfoto" => "2021-04-18",
            "sekolah1" => "14",
            "sekolah2" => "Pilih Sekolah",
            "sekolah3" => "",
            "tanggallahir" => "1970-01-01",
            "fb" => "Tasya Nur Diana",
            "ig" => "@tasyadiane",
            "nik" => "3514086301010001",
            "kemampuanbhs" => null,
            "darah" => "A",
            "keahlian" => "Fotografi",
            "hobi" => "Menggambar, Memotret, Memasak, Menulis",
            "penghargaan" => "Santri Berprestasi (2021)",
            "kk" => "3514080101053009",
            "nikayah" => "",
            "nikibu" => "",
            "penghasilan" => "",
            "hportu" => "081252689829",
            "ketertarikan" => "Multimedia",
            "jikakursus" => "Menjahit, Melukis",
            "alasan" => "Sanad Al Qur'an yang jelas",
            "motor" => "tidak",
            "laundry" => "tidak",
            "platmotor" => "-",
            "kelas1" => "2",
            "kelas2" => "0",
            "nonformal" => "PP.  Putri Al Ishlahiyah Singosari Malang",
            "formal" => "MA Almaarif Singosari Malang (2017-2019)",
            "id_kamar" => "0",
            "pekerjaan" => "",
            "tahunmondok" => "2019",
            "id_kompleks_asrama" => "12"
        ];

        // Validasi data
        $isValid = true;

        if (!is_numeric($item['id_kompleks_asrama'])) {
            Log::info('Invalid id_kompleks_asrama:', $item);
            $isValid = false;
        }

        if (!$this->isValidDate($item['tanggallahir'])) {
            Log::info('Invalid tanggallahir:', ['date' => $item['tanggallahir'], 'timestamp' => strtotime($item['tanggallahir'])]);
            $isValid = false;
        }

        if (!$this->isValidDate($item['tanggal_aktif'])) {
            Log::info('Invalid tanggal_aktif:', ['date' => $item['tanggal_aktif'], 'timestamp' => strtotime($item['tanggal_aktif'])]);
            $isValid = false;
        }

        if (!$isValid) {
            Log::info('Invalid data:', $item);
            return;
        }

        // Menentukan diterima_at dan aktif_at
        $tanggal_aktif = Carbon::createFromFormat('Y-m-d', $item['tanggal_aktif'])->toDateString();
        $diterima_at = "$tanggal_aktif 00:00:00";
        $aktif_at = "$tanggal_aktif 00:00:00";

        // Menentukan tahun_masuk
        $tahun_masuk = (int)$item['tahunmondok'];
        if (!$this->isValidYear($tahun_masuk)) {
            $tahun_masuk = Carbon::createFromFormat('Y-m-d', $item['tanggal_aktif'])->year;
        }

        try {
            DB::table('santris')->insert([
                'nama' => $item['nama'],
                'tanggal_lahir' => $item['tanggallahir'],
                'informasi_tambahan' => 'tidak ada',
                'nomor_induk' => $item['nomor_induk'],
                'nomor_induk_baru' => null,
                'tempat_lahir' => $item['lahir'],
                'jenis_kelamin' => $item['kelamin'],
                'foto' => 'photos/' . $item['foto'],
                'hp' => $item['hp'],
                'email' => $item['email'],
                'alamat' => $item['alamat'],
                'kabupaten' => null,
                'provinsi' => null,
                'nik' => $item['nik'],
                'kk' => $item['kk'],
                'foto_ktp' => null,
                'foto_kk' => null,
                'nama_ayah' => $item['ayah'],
                'nama_ibu' => $item['ibu'],
                'pekerjaan_ayah' => $item['pekerjaan_ayah'],
                'pekerjaan_ibu' => $item['pekerjaan_ibu'],
                'hp_orang_tua' => $item['hportu'],
                'alamat_orang_tua' => $item['alamat_ortu'],
                'anak_ke' => $item['anak_ke'],
                'jumlah_saudara' => $item['jmlsaudara'],
                'pendidikan_terakhir' => '',
                'pendidikan_formal' => null,
                'pendidikan_non_formal' => null,
                'nomor_peserta' => null,
                'id_komplek' => $item['id_kompleks_asrama'],
                'id_admin' => 1,
                'id_kamar' => null,
                'diterima' => 1,
                'diterima_at' => $diterima_at,
                'tahun_masuk' => $tahun_masuk,
                'alasan' => $item['alasan'],
                'is_aktif' => $item['status'] == 1 ? 1 : 0,
                'aktif_at' => $aktif_at,
                'is_nonaktif' => $item['status'] == 0 ? 1 : 0,
                'is_alumni' => $item['status'] == 2 ? 1 : 0,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            // Log sukses
            Log::info('Data inserted successfully:', $item);
        } catch (\Exception $e) {
            // Log error jika terjadi kesalahan saat insert data
            $this->logError($item, $e);
        }
    }

    /**
     * Validasi format tanggal
     *
     * @param string $date
     * @return bool
     */
    private function isValidDate($date)
    {
        // Log tambahan untuk melihat input tanggal
        Log::info('Validating date:', ['date' => $date]);
        if ($date === '0000-00-00') {
            return false;
        }
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return false;
        }
        $year = date('Y', $timestamp);
        $month = date('m', $timestamp);
        $day = date('d', $timestamp);
        return checkdate($month, $day, $year);
    }

    /**
     * Validasi format tahun
     *
     * @param string $year
     * @return bool
     */
    private function isValidYear($year)
    {
        return is_numeric($year) && (int)$year > 0 && strlen($year) === 4;
    }

    /**
     * Log error saat insert data
     *
     * @param array $item
     * @param \Exception $e
     * @return void
     */
    private function logError($item, $e)
    {
        Log::error('Error inserting data:', ['item' => $item, 'error' => $e->getMessage()]);
    }
}
