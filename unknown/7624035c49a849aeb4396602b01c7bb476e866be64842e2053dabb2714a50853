<?php

namespace App\Livewire\Widgets;

use App\Models\Santri;
use Livewire\Component;

class TotalSantriAlumni extends Component
{
    public $totalSantri;
    public $class;


    public function mount($class = '')
    {
        $user = auth()->user();
        if ($user->isAdmin) {
            $idKomplek = $user->roleUsers()->value('id_komplek');
            $this->totalSantri = Santri::where('is_alumni', 1)->where('id_komplek', $idKomplek)->count();
        } else {
            // Menghitung jumlah santri alumni
            $this->totalSantri = Santri::where('is_alumni', 1)->count();
        }
    }

    public function render()
    {
        return view('livewire.widgets.total-santri-alumni');
    }
}
