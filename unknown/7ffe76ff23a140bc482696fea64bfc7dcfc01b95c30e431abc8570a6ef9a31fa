<?php

namespace App\Livewire\Setting;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Services\WebsiteSettingCacheService;

class KtsSetting extends Component
{
    use WithFileUploads;

    public $background_front, $background_back, $default_photo;
    public $savedBackgroundFront, $savedBackgroundBack, $savedDefaultPhoto;

    protected $websiteSettingService;

    protected $rules = [
        'background_front' => 'nullable|image|mimes:jpg,png,jpeg|max:2048', // 2MB
        'background_back' => 'nullable|image|mimes:jpg,png,jpeg|max:2048', // 2MB
        'default_photo' => 'nullable|image|mimes:jpg,png,jpeg|max:2048',
    ];

    public function __construct()
    {
        $this->websiteSettingService = app(WebsiteSettingCacheService::class);
    }

    public function mount()
    {
        // Ambil data gambar yang sudah ada di cache atau default
        $this->savedBackgroundFront = $this->websiteSettingService->getSettingValue('kts_background_front', '');
        $this->savedBackgroundBack = $this->websiteSettingService->getSettingValue('kts_background_back', '');
        $this->savedDefaultPhoto = $this->websiteSettingService->getSettingValue('kts_default_photo', '');
    }

    public function save()
    {
        $this->validate();

        // Proses file upload background depan dan belakang untuk KTS
        if ($this->background_front) {
            $backgroundFrontPath = $this->background_front->store('backgrounds', 'public');
            $this->websiteSettingService->saveSetting('kts_background_front', $backgroundFrontPath);
        }

        if ($this->background_back) {
            $backgroundBackPath = $this->background_back->store('backgrounds', 'public');
            $this->websiteSettingService->saveSetting('kts_background_back', $backgroundBackPath);
        }

        if ($this->default_photo) {
            $defaultPhotoPath = $this->default_photo->store('backgrounds', 'public');
            $this->websiteSettingService->saveSetting('kts_default_photo', $defaultPhotoPath);
        }

        // Menyimpan pesan sukses
        session()->flash('message', 'KTS settings have been saved successfully!');
    }

    public function render()
    {
        return view('livewire.setting.kts-setting');
    }
}
