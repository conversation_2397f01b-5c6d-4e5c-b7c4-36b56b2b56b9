<div>
    <x-slot name="header">
        <x-mary-header title="{{ $asrama->nama_komplek }}" subtitle="Check this on mobile" class="bg-white overflow-hidden sm:rounded-lg p-2">
            <x-slot:actions>
                <x-mary-button icon="o-pencil" class="btn-primary" link="{{ route('asrama.edit', ['id' => $asrama->id]) }}" no-wire-navigate />
            </x-slot:actions>
        </x-mary-header>
    </x-slot>

    <div class="py-4">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white shadow-xl sm:rounded-lg p-6">
                <div class="flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-8">
                    <div class="flex-shrink-0">
                        <img class="h-32 w-32 rounded-full" src="{{ asset('storage/' . $asrama->logo) }}" alt="Logo Komplek">
                    </div>
                    <div class="text-center lg:text-left">
                        <h3 class="text-2xl font-bold text-gray-800">{{ $asrama->nama_komplek }}</h3>
                        <p class="mt-1 text-lg text-gray-600">{{ $asrama->nama_pengasuh }}</p>
                        <p class="mt-1 text-lg text-gray-600">{{ $asrama->komplek_type }}</p>
                        <p class="mt-1 text-lg text-gray-600">{{ $asrama->alamat }}</p>
                        <p class="mt-1 text-lg text-gray-600">{{ $asrama->hp }}</p>
                    </div>
                </div>

                {{-- <div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-blue-50 p-4 rounded-lg shadow">
                        <h4 class="text-lg font-semibold text-blue-600">Jumlah Santri</h4>
                        <p class="mt-2 text-2xl font-bold text-gray-800">{{ $asrama->santris_count }}</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg shadow">
                        <h4 class="text-lg font-semibold text-green-600">Jumlah Kamar</h4>
                        <p class="mt-2 text-2xl font-bold text-gray-800">{{ $asrama->kamar_count }}</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg shadow">
                        <h4 class="text-lg font-semibold text-yellow-600">Jumlah Santri per Tahun</h4>
                        <div class="mt-2">
                            @foreach($santriPerYear as $data)
                                <p class="text-gray-700">{{ $data['year'] }}: <span class="font-bold">{{ $data['count'] }}</span></p>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="mt-8">
                    <h4 class="text-lg font-semibold text-gray-800">Grafik Jumlah Santri per Tahun</h4>
                    <canvas id="santriChart" class="mt-4"></canvas>
                </div> --}}
            </div>
        </div>
    </div>



</div>

@assets
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endassets

@script
<script>

    if (document.getElementById('santriChart') !== null){
        const ctx = document.getElementById('santriChart').getContext('2d');
        const santriChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json(array_column($santriPerYear, 'year')),
                datasets: [{
                    label: 'Jumlah Santri',
                    data: @json(array_column($santriPerYear, 'count')),
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderWidth: 1,
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

</script>
@endscript

