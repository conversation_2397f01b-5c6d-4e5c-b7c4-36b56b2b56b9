<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Komplek extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'nama_komplek', 'nama_pengasuh', 'komplek_type', 'alamat', 'description', 'logo', 'alias_name', 'badan_status', 'kode_komplek'
    ];

    protected $dates = ['deleted_at'];

    public function santris()
    {
        return $this->hasMany(Santri::class, 'id_komplek');
    }

    public function kamar()
    {
        return $this->hasMany(Kamar::class, 'id_komplek');
    }
}
