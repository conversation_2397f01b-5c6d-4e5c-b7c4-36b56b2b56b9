<?php

namespace App\Services;

use App\Models\Santri;
use App\Models\WebsiteSetting;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use PDF_Code128;

class KtsService
{
    protected $websiteSettingService;

    public function __construct()
    {
        $this->websiteSettingService = app(WebsiteSettingCacheService::class);
    }
    /**
     * Generate Kartu Tanda Santri (KTS) for a single santri.
     */
    public function generateKtsForSingleSantri(Santri $santri, $method = 'D')
    {
        return $this->generateKts([$santri], $method);
    }

    /**
     * Generate Kartu Tanda Santri (KTS) for multiple santri.
     */
    public function generateKtsForMultipleSantri(Collection $santris, $method = 'D')
    {
        return $this->generateKts($santris, $method);
    }

    /**
     * Generate Kartu Tanda Santri (KTS) for multiple santri by id_komplek and tahun_masuk.
     */
    public function generateKtsForMultipleSantriByIdKomplekAndTahunMasuk($id_komplek, $tahun_masuk)
    {
        $santris = Santri::where('id_kompleks_asrama', $id_komplek)
            ->whereYear('tanggal_diterima', $tahun_masuk)
            ->where('status', 1)
            ->get();

        return $this->generateKts($santris);
    }

    /**
     * Common function to generate Kartu Tanda Santri (KTS).
     */
    /**
     * Common function to generate Kartu Tanda Santri (KTS).
     */
    private function generateKts($santriList, $method = 'D')
    {
        ini_set('memory_limit', '-1');
        $pdf = $this->initializePdf();

        $y_axis_initial = 10; // Set initial Y position for the first data entry
        $yn = 60; // Space between each card (controls the vertical spacing between santri entries)
        $max = 4; // Max number of entries per page
        $i = 0; // Counter for the number of santri per page
        $yinduk = $y_axis_initial; // Set the initial Y position

        $santriList = $santriList->sortBy('tahun_masuk')->values();

        foreach ($santriList as $index => $santri) {

            // Add background image for each row
            $this->addBackground($pdf, $yinduk);

            // Add Santri data (front and back)
            $this->addSantriDataToPdf($pdf, $santri, $yinduk);

            // Move Y position down for the next entry
            $yinduk += $yn;
            $i++;

            // If we've reached the max number of entries per page and there are more entries to process, add a new page
            if ($i === $max) {
                // Only add a new page if there are more items left in the list
                if ($index < count($santriList) - 1) {
                    $pdf->AddPage(); // Add a new page
                    $i = 0; // Reset counter for santri per page
                    $yinduk = $y_axis_initial; // Reset Y position for new page
                }
            }
        }

        $fileName = "DataSantri_KTS_" . date('d-m-y') . ".pdf";
        return $pdf->Output($method, $fileName);
    }




    /**
     * Initialize PDF settings.
     */
    private function initializePdf()
    {
        Carbon::setLocale('id');
        $pdf = new PDF_Code128();
        $pdf->SetAutoPageBreak(false);
        $pdf->SetMargins(10, 40, 10);
        $pdf->AddPage();

        return $pdf;
    }

    private function addBackground($pdf, $yPosition)
    {
        // Ambil path gambar dari database
        $backgroundFront = optional(\App\Models\WebsiteSetting::where('name', 'kts_background_front')->first())->value;
        $backgroundBack = optional(\App\Models\WebsiteSetting::where('name', 'kts_background_back')->first())->value;

        // dd($backgroundFront);

        // Path default jika tidak ada data di database
        $defaultFrontPath = storage_path('app/public/kts/depan-baru.jpg');
        $defaultBackPath = storage_path('app/public/kts/belakang-baru.jpg');

        // Tentukan path final
        $foto_depan = $backgroundFront
            ? storage_path('app/public/' . $backgroundFront)
            : $defaultFrontPath;

        $foto_belakang = $backgroundBack
            ? storage_path('app/public/' . $backgroundBack)
            : $defaultBackPath;

        // Validasi apakah file ada sebelum ditambahkan
        if (file_exists($foto_depan)) {
            $pdf->Image($foto_depan, 10, $yPosition, 87, 57); // Sesuaikan ukuran dan posisi
        }

        if (file_exists($foto_belakang)) {
            $pdf->Image($foto_belakang, 107, $yPosition, 87, 57); // Sesuaikan ukuran dan posisi
        }
    }

    private function addFrontSide($pdf, $santri, $yinduk)
    {
        // BAGIAN DEPAN
        // Set font and text color for the header
        $pdf->SetTextColor(0, 0, 0);
        $pdf->SetFont('Arial', 'UB', 10);

        // Add the title "KARTU TANDA SANTRI"
        $pdf->text(37, $yinduk + 35, "KARTU TANDA SANTRI");

        // Set font for santri details
        $pdf->SetFont('Arial', 'B', 9);

        // Add santri name and nomor_induk
        $pdf->text(13, $yinduk + 45, strtoupper($santri->nama));
        $pdf->text(13, $yinduk + 50, $santri->nomor_induk);

        // Set fill color for the barcode
        $pdf->SetFillColor(0, 0, 0);

        // Add the barcode (Code128)
        $pdf->Code128(20, $yinduk + 55, $santri->nomor_induk, 30, 8);

        // Ambil pengaturan default foto dari database
        $defaultPhotoSetting = WebsiteSetting::where('name', 'kts_default_photo')->first();
        $defaultPhotoPath = storage_path('app/public/' . $defaultPhotoSetting->value);

        // Tentukan path foto santri
        $foto = $santri->foto ? storage_path('app/public/' . $santri->foto) : $defaultPhotoPath;


        // Add the photo if it exists, otherwise use a placeholder
        // $foto = $santri->foto ? storage_path('app/public/' . $santri->foto) : storage_path('app/public/kts/kosong.jpg');
        if (file_exists($foto)) {
            $pdf->Image($foto, 72, $yinduk + 37, 20, 26.7);
        }
    }

    private function addFrontSideNew($pdf, $santri, $yinduk)
    {
        // BAGIAN DEPAN
        // Set font and text color for the header
        $pdf->SetTextColor(0, 0, 0);
        $pdf->SetFont('Arial', 'UB', 10);

        // Set font for santri details
        $pdf->SetFont('Arial', 'B', 9);

        // Calculate the position of the name
        $nameX = 43; // X position for name
        $nameY = $yinduk + 25; // Y position for name
        $nameWidth = 50; // Max width for name before wrapping
        $nameHeight = 5; // Line height for name

        // Add santri name using MultiCell for automatic word wrap
        // The last parameter is border (0 for no border)
        $pdf->SetXY($nameX, $nameY);
        $pdf->MultiCell($nameWidth, $nameHeight, strtoupper($santri->nama), 0, 'L');

        // Get the current Y position after adding the name
        $currentY = $pdf->GetY();

        // Add nomor_induk_baru below the name
        $pdf->SetXY($nameX, $currentY);
        $pdf->Cell(0, $nameHeight, $santri->nomor_induk_baru, 0, 1, 'L');

        // Set fill color for the barcode
        $pdf->SetFillColor(0, 0, 0);

        // Add the barcode (Code128)
        $pdf->Code128(75, $yinduk + 47.5, $santri->nomor_induk_baru, 17.5, 5);

        // Check if the santri has a photo, else use a placeholder
        if ($this->checkImageFile(storage_path('app/public/' . $santri->foto))) {
            $foto = storage_path('app/public/' . $santri->foto);
        } else {

            // Ambil pengaturan default foto dari database
            $defaultPhotoSetting = WebsiteSetting::where('name', 'kts_default_photo')->first();
            $defaultPhotoPath = storage_path('app/public/' . $defaultPhotoSetting->value);

            // Tentukan path foto santri
            $foto = $defaultPhotoPath;
            // $foto = storage_path('app/public/kts/kosong.jpg');
        }

        // Add the santri photo
        $pdf->Image($foto, 19.5, $yinduk + 12.5, 21, 28.5);
    }

    private function addAddressAndNumber($pdf, $santri, $yinduk): void
    {
        // BAGIAN ALAMAT
        // Set font and text color for the header
        $pdf->SetTextColor(0, 0, 0);

        // Set font for santri details
        $pdf->SetFont('Arial', '', 6);

        // Calculate the position of the name
        $nameX = 14; // X position for name
        $nameY = $yinduk + 47.5; // Y position for name
        $nameWidth = 60; // Max width for name before wrapping
        $nameHeight = 3; // Line height for name

        // Add santri name using MultiCell for automatic word wrap
        // The last parameter is border (0 for no border)
        $address = $this->websiteSettingService->getSettingValue('website_address', '');
        $pdf->SetXY($nameX, $nameY);
        $pdf->MultiCell($nameWidth, $nameHeight, $address, 0);

        // Get the current Y position after adding the name
        $currentY = $pdf->GetY();
        // Add nomor_induk_baru below the name
        $phone = $this->websiteSettingService->getSettingValue('website_phone', '');
        $pdf->SetXY($nameX, $currentY);
        $pdf->Cell(0, $nameHeight, "Kontak : " . $phone, 0, 1, 'L');

    }


    private function addBackSide($pdf, $santri, $yinduk)
    {
        // BAGIAN BELAKANG
        $pdf->SetFont('Arial', 'B', 8);

        // Data TTL
        $ttl = strtoupper($santri->tempat_lahir) . ', ' . $santri->tgl . '-' . $santri->bln . '-' . $santri->thn;

        // Lebar kolom untuk label, ":" separator, dan nilai
        $labelWidth = 17; // Lebar untuk label seperti "Nama"
        $separatorWidth = 3; // Lebar untuk ":"
        $valueWidth = 60; // Lebar untuk nilai
        $lineHeight = 4; // Tinggi setiap baris

        // X & Y starting positions
        $startX = 110;
        $startY = $yinduk + 10; // Adjust for positioning

        // No. Induk
        $pdf->SetXY($startX, $startY);
        $pdf->Cell($labelWidth, $lineHeight, "NO. INDUK", 0, 0, 'L');
        $pdf->Cell($separatorWidth, $lineHeight, ":", 0, 0, 'L');
        $pdf->Cell($valueWidth, $lineHeight, $santri->nomor_induk_baru, 0, 1, 'L');

        // Nama
        $pdf->SetXY($startX, $startY + 5);
        $pdf->Cell($labelWidth, $lineHeight, "NAMA", 0, 0, 'L');
        $pdf->Cell($separatorWidth, $lineHeight, ":", 0, 0, 'L');
        $pdf->Cell($valueWidth, $lineHeight, strtoupper($santri->nama), 0, 1, 'L');

        // TTL
        $pdf->SetXY($startX, $startY + 10);
        $pdf->Cell($labelWidth, $lineHeight, "TTL", 0, 0, 'L');
        $pdf->Cell($separatorWidth, $lineHeight, ":", 0, 0, 'L');
        $tanggalLahirFormatted =  strtoupper($santri->tempat_lahir) . ', ' . Carbon::parse($santri->tanggal_lahir)->translatedFormat('d F Y');
        $pdf->Cell($valueWidth, $lineHeight, $tanggalLahirFormatted, 0, 1, 'L');

        // Komplek
        $pdf->SetXY($startX, $startY + 15);
        $pdf->Cell($labelWidth, $lineHeight, "KOMPLEK", 0, 0, 'L');
        $pdf->Cell($separatorWidth, $lineHeight, ":", 0, 0, 'L');
        $pdf->Cell($valueWidth, $lineHeight, strtoupper($santri->komplek->alias_name), 0, 1, 'L'); // Adjust accordingly if data is available

        // Alamat

        $kabupatenName = str_starts_with(strtoupper($santri->kabupaten->name), 'KABUPATEN')
            ? str_replace('KABUPATEN', 'KAB.', strtoupper($santri->kabupaten->name))
            : strtoupper($santri->kabupaten->name);

        $alamat = strtoupper($santri->kecamatan->name) . ", " . $kabupatenName . ", " . strtoupper($santri->kabupaten->provinceName);



        // dd($alamat);
        $pdf->SetXY($startX, $startY + 20);
        $pdf->Cell($labelWidth, $lineHeight, "ALAMAT", 0, 0, 'L');
        $pdf->Cell($separatorWidth, $lineHeight, ":", 0, 0, 'L');

        // Alamat menggunakan MultiCell untuk wrap jika terlalu panjang
        $pdf->SetXY($startX + $labelWidth + $separatorWidth, $startY + 20);
        $pdf->MultiCell($valueWidth, $lineHeight, strtoupper($alamat), 0, 'L');

        // Tanggal dan tanda tangan
        $pdf->SetTextColor(0, 0, 0);
        $pdf->SetFont('Arial', '', 7);

        // Lebar kertas PDF (misalnya A4, 210 mm) dikurangi margin (misal 10 mm di setiap sisi)
        $pageWidth = 210; // Lebar halaman A4 dalam mm
        $rightMargin = 10; // Margin kanan
        $cellWidth = $pageWidth - $rightMargin - 172; // Lebar cell yang disesuaikan agar teks rata kanan

        // Set posisi X dan Y, kemudian buat cell dengan alignment ke kanan
        $pdf->SetXY(162, $yinduk + 39);
        $pdf->Cell($cellWidth, $lineHeight, "Yogyakarta, " . Carbon::parse(date("d-F-Y"))->translatedFormat('d F Y'), 0, 1, 'R');



        // Pengasuh PP. Al-Munawwir
        $pdf->SetXY(162, $yinduk + 49);
        $pdf->Cell($cellWidth, $lineHeight, "Pengasuh PP. Al-Munawwir", 0, 1, 'R');
    }



    /**
     * Add a santri's data to the PDF.
     */
    private function addSantriDataToPdf($pdf, $santri, $yinduk)
    {
        $this->addFrontSideNew($pdf, $santri, $yinduk);
        $this->addAddressAndNumber($pdf, $santri, $yinduk);
        $this->addBackSide($pdf, $santri, $yinduk);
    }

    private function checkImageFile($filePath)
    {
        // Check if file exists
        if (!file_exists($filePath)) {
            return false;
        }

        // Get the file extension
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);

        // Check if the extension is empty or not valid
        if (empty($extension) || !in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif'])) {
            return false;
        }

        return true;
    }


}
