<?php

namespace App\Livewire\Setting;

use App\Models\Komplek;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Laravolt\Indonesia\Models\City;
use Livewire\Component;
use Livewire\WithFileUploads;

class RestoreData extends Component
{
    use WithFileUploads;

    public $kompleks = [];
    public $selectedKomplek;
    public $file;
    public $angkatan;
    public $errorsList = [];

    public function mount()
    {
        $this->kompleks = Komplek::all()->map(
            fn($komplek) => [
                'id' => $komplek->id,
                'name' => $komplek->nama_komplek,
            ]
        )->toArray();
    }

    public function updatedFile()
    {
        $this->validate([
            'file' => 'file|mimetypes:application/json',
        ]);
    }

    public function process()
    {
        $this->validate([
            'file' => 'required|file|mimetypes:application/json',
            'selectedKomplek' => 'required|exists:kompleks,id',
            'angkatan' => 'required|integer|min:1900|max:' . date('Y'),
        ]);

        $fileContent = file_get_contents($this->file->getRealPath());
        $data = json_decode($fileContent, true)['data'];

        if (!is_array($data)) {
            $this->addError('file', 'The uploaded file is not valid JSON data.');
            return;
        }

        $santris = [];
        $errors = [];

        foreach ($data as $item) {
            if (
                $item['tahunmondok'] != $this->angkatan ||
                $item['id_kompleks_asrama'] != $this->selectedKomplek
            ) {
                continue;
            }

            $validator = Validator::make($item, [
                'nama' => 'required|string|max:255',
                'id_kompleks_asrama' => 'required|numeric',
                'tanggallahir' => 'required|date',
                'tanggal_aktif' => 'required|date',
                'kelamin' => 'required|string|in:laki-laki,perempuan',
            ]);

            if ($validator->fails()) {
                $errors[] = [
                    'data' => $item,
                    'errors' => $validator->errors()->toArray(),
                ];
                continue;
            }

            $email = filter_var($item['email'], FILTER_VALIDATE_EMAIL) ? $item['email'] : null;

            $cityName = $this->extractCityName($item['lahir']);
            $city = City::where('name', 'like', "%$cityName%")->first();

            $tanggal_aktif = Carbon::createFromFormat('Y-m-d', $item['tanggal_aktif'])->toDateString();
            $santris[] = [
                'nama' => $item['nama'],
                'tanggal_lahir' => $item['tanggallahir'],
                'tempat_lahir' => $item['lahir'],
                'jenis_kelamin' => strtolower($item['kelamin']),
                'foto' => 'santri-foto/' . $item['foto'],
                'id_komplek' => $item['id_kompleks_asrama'],
                'tahun_masuk' => $item['tahunmondok'],
                'diterima_at' => "$tanggal_aktif 00:00:00",
                'aktif_at' => "$tanggal_aktif 00:00:00",
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'city_code' => $city ? $city->code : null,
                'is_aktif' => $item['status'] == 1 ? 1 : 0,
                'is_nonaktif' => $item['status'] == 0 ? 1 : 0,
                'is_alumni' => $item['status'] == 2 ? 1 : 0,
                'informasi_tambahan' => 'tidak ada',
                'nomor_induk' => $item['nomor_induk'],
                'nomor_induk_baru' => null,
                'hp' => $item['hp'],
                'email' => $email,
                'alamat' => $item['alamat'],
                'nik' => $item['nik'],
                'kk' => $item['kk'],
                'foto_ktp' => null,
                'foto_kk' => null,
                'nama_ayah' => $item['ayah'],
                'nama_ibu' => $item['ibu'],
                'pekerjaan_ayah' => $item['pekerjaan_ayah'],
                'pekerjaan_ibu' => $item['pekerjaan_ibu'],
                'hp_orang_tua' => $item['hportu'],
                'alamat_orang_tua' => $item['alamat_ortu'],
                'anak_ke' => $item['anak_ke'],
                'jumlah_saudara' => $item['jmlsaudara'],
                'pendidikan_terakhir' => '',
                'pendidikan_formal' => null,
                'pendidikan_non_formal' => null,
                'nomor_peserta' => null,
                'id_admin' => 1,
                'id_kamar' => null,
                'diterima' => 1,
                'alasan' => $item['alasan'],
            ];

            if (count($santris) >= 500) {
                DB::table('santris')->insert($santris);
                $santris = [];
            }
        }

        if (!empty($santris)) {
            DB::table('santris')->insert($santris);
        }

        $this->errorsList = $errors;

        $totalBerhasil = count($santris);
        $totalGagal = count($errors);

        if (empty($errors)) {
            session()->flash(
                'success',
                "Data restored successfully. Total records restored: $totalBerhasil. Failed: $totalGagal."
            );
        } else {
            session()->flash(
                'error',
                "Some records failed validation. Successfully restored: $totalBerhasil. Failed: $totalGagal."
            );
        }
    }

    private function extractCityName($fullName)
    {
        $keywords = ['Kabupaten', 'Kota'];
        foreach ($keywords as $keyword) {
            if (stripos($fullName, $keyword) !== false) {
                return trim(str_ireplace($keyword, '', $fullName));
            }
        }
        return $fullName;
    }

    public function render()
    {
        return view('livewire.setting.restore-data');
    }
}
