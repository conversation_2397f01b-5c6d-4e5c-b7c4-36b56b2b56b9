<?php

namespace App\Livewire\Setting;

use Livewire\Component;
use Livewire\WithFileUploads;
use Mary\Traits\Toast;
use App\Services\WebsiteSettingCacheService;

class GeneralSetting extends Component
{
    use WithFileUploads;
    use Toast;

    public $name, $description, $logo, $favicon, $phone, $address;
    protected $websiteSettingService;

    // Untuk validasi
    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'logo' => 'nullable|image|mimes:jpg,png,jpeg|max:1024', // 1MB
        'favicon' => 'nullable|image|mimes:jpg,png,jpeg|max:512', // 512KB
        'phone' => 'nullable|string|max:20',
        'address' => 'nullable|string|max:255',
    ];

    public function __construct()
    {
        $this->websiteSettingService = app(WebsiteSettingCacheService::class);
    }

    public function mount()
    {
        // $this->websiteSettingService = new WebsiteSettingCacheService();

        // Ambil pengaturan dari cache
        $this->name = $this->websiteSettingService->getSettingValue('website_name', '');
        $this->description = $this->websiteSettingService->getSettingValue('website_description', '');
        $this->phone = $this->websiteSettingService->getSettingValue('website_phone', '');
        $this->address = $this->websiteSettingService->getSettingValue('website_address', '');
    }

    public function save()
    {
        $this->validate();

        // Simpan data pengaturan melalui service
        $this->websiteSettingService->saveSetting('website_name', $this->name);
        $this->websiteSettingService->saveSetting('website_description', $this->description);
        $this->websiteSettingService->saveSetting('website_phone', $this->phone);
        $this->websiteSettingService->saveSetting('website_address', $this->address);

        // Proses file upload logo dan favicon
        if ($this->logo) {
            $logoPath = $this->logo->store('logos', 'public');
            $this->websiteSettingService->saveSetting('website_logo', $logoPath);
        }

        if ($this->favicon) {
            $faviconPath = $this->favicon->store('favicons', 'public');
            $this->websiteSettingService->saveSetting('website_favicon', $faviconPath);
        }

        $this->success('Pengaturan umum berhasil disimpan');
    }

    public function render()
    {
        return view('livewire.setting.general-setting');
    }
}
