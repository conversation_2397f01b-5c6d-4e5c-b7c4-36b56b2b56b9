<?php

namespace App\Mail;

use App\Models\ConfirmationLink;
use App\Models\RoleUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ConfirmationEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $komplekData;
    public $role;

    /**
     * Create a new message instance.
     */
    public function __construct(protected ConfirmationLink $data)
    {
        $roleUser = RoleUser::where("user_id", $this->data->user->id)->first();
        $this->role = $roleUser->role;

        if ($this->role->name != 'superadmin') {
            $this->komplekData = $roleUser->komplek;
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Konfirmasi Akun',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.confirmation',
            with: [
                'name' => $this->data->user->name,
                'confirmationLink' => route('confirmation-link', ['token' => $this->data->token]),
                'komplek' => $this->komplekData->nama_komplek,
                'status' => $this->role->name,
                'email' => $this->data->user->email,
                'created_at' => now(),
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
