<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            // Menambahkan indeks pada kolom 'tahun_masuk'
            $table->index('tahun_masuk');

            // Menambahkan indeks pada kolom 'id_komplek'
            $table->index('id_komplek');

            // Menambahkan indeks gabungan untuk 'tahun_masuk' dan 'id_komplek'
            $table->index(['tahun_masuk', 'id_komplek'], 'tahun_komplek_index');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('santris', function (Blueprint $table) {
            $table->dropIndex(['tahun_masuk']);
            $table->dropIndex(['id_komplek']);
            $table->dropIndex(['tahun_komplek_index']);
        });
    }
};
