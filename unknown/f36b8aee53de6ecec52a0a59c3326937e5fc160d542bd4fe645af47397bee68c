<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesSeeder extends Seeder
{
    public function run()
    {
        $adminPermissions = Permission::all();
        $adminRole = Role::create(['name' => 'superadmin']);
        $adminRole->givePermissionTo($adminPermissions);

//        $userRole = Role::create(['name' => 'user']);
//        $userRole->givePermissionTo([
//            'view users',
//            'view roles',
//        ]);
    }
}
