<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Kamar extends Model
{
    use HasFactory;

    protected $fillable = [
        'id_komplek', 'nama_kamar'
    ];

    protected $dates = ['deleted_at'];

    // Relationship with Komplek
    public function komplek()
    {
        return $this->hasOne(Komplek::class, 'id', 'id_komplek');
    }
}
