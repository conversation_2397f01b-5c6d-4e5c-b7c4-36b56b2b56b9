<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Buat pengguna baru
        $user = User::create([
            'name' => 'Madrasah Huffadh 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('mhrq1989'), // Hash password
        ]);

        // <PERSON>i role admin, jika ada, tambahkan role ini ke user
        $adminRole = Role::where('name', 'admin')->first();

        if ($adminRole) {
            $user->roles()->attach($adminRole->id, ['id_komplek' => 18]);
        }
    }
}
